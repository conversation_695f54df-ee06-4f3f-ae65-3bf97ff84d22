#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的数据源选择策略

验证强制下载使用adata（前复权），日常更新使用mootdx（快速）的策略
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.database_manager import DatabaseManager
from data.market_fetcher import MarketDataFetcher
import pandas as pd
import logging
from datetime import datetime, timedelta

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_data_source_strategy():
    """测试数据源选择策略"""
    
    # 初始化数据库和数据采集器
    db = DatabaseManager()
    
    with MarketDataFetcher(db) as fetcher:
        test_stock = "000001"  # 平安银行
        start_date = "2024-01-01"
        end_date = "2024-12-31"
        
        logger.info("🧪 测试新的数据源选择策略")
        logger.info("=" * 80)
        
        # 测试1: 强制下载模式（应该使用adata前复权数据）
        logger.info("\n📊 测试1: 强制下载模式（force_download=True）")
        logger.info("预期：使用adata获取前复权数据，不回退到mootdx")
        
        try:
            df_force = fetcher.get_stock_daily_data(
                stock_code=test_stock,
                start_date=start_date,
                end_date=end_date,
                limit=100,  # 获取100条数据进行测试
                force_download=True  # 强制下载模式
            )
            
            if not df_force.empty:
                logger.info(f"✅ 强制下载模式成功: {len(df_force)} 条数据")
                logger.info(f"📊 数据时间范围: {df_force['trade_time'].min()} 到 {df_force['trade_time'].max()}")
                logger.info(f"📊 最新收盘价: {df_force['close'].iloc[-1]}")
                logger.info("🎯 数据源：adata（前复权）")
            else:
                logger.warning("⚠️ 强制下载模式返回空数据")
                
        except Exception as e:
            logger.error(f"❌ 强制下载模式测试失败: {e}")
        
        # 测试2: 日常更新模式（应该使用mootdx快速获取）
        logger.info("\n📊 测试2: 日常更新模式（force_download=False）")
        logger.info("预期：使用mootdx快速获取数据（不复权）")
        
        try:
            df_daily = fetcher.get_stock_daily_data(
                stock_code=test_stock,
                start_date=start_date,
                end_date=end_date,
                limit=10,  # 获取10条数据进行测试
                force_download=False  # 日常更新模式
            )
            
            if not df_daily.empty:
                logger.info(f"✅ 日常更新模式成功: {len(df_daily)} 条数据")
                logger.info(f"📊 数据时间范围: {df_daily['trade_time'].min()} 到 {df_daily['trade_time'].max()}")
                logger.info(f"📊 最新收盘价: {df_daily['close'].iloc[-1]}")
                logger.info("🎯 数据源：mootdx（不复权）")
            else:
                logger.warning("⚠️ 日常更新模式返回空数据")
                
        except Exception as e:
            logger.error(f"❌ 日常更新模式测试失败: {e}")
        
        # 测试3: 分钟数据强制下载模式
        logger.info("\n📊 测试3: 分钟数据强制下载模式（force_download=True）")
        logger.info("预期：使用adata获取前复权5分钟数据")
        
        try:
            df_minute_force = fetcher.get_stock_minute_data(
                stock_code=test_stock,
                start_date=start_date,
                end_date=end_date,
                period=5,  # 5分钟数据
                limit=50,  # 获取50条数据进行测试
                force_download=True  # 强制下载模式
            )
            
            if not df_minute_force.empty:
                logger.info(f"✅ 分钟数据强制下载成功: {len(df_minute_force)} 条数据")
                logger.info(f"📊 数据时间范围: {df_minute_force['trade_time'].min()} 到 {df_minute_force['trade_time'].max()}")
                logger.info(f"📊 最新收盘价: {df_minute_force['close'].iloc[-1]}")
                logger.info("🎯 数据源：adata（前复权）")
            else:
                logger.warning("⚠️ 分钟数据强制下载返回空数据")
                
        except Exception as e:
            logger.error(f"❌ 分钟数据强制下载测试失败: {e}")
        
        # 测试4: 分钟数据日常更新模式
        logger.info("\n📊 测试4: 分钟数据日常更新模式（force_download=False）")
        logger.info("预期：使用mootdx快速获取5分钟数据（不复权）")
        
        try:
            df_minute_daily = fetcher.get_stock_minute_data(
                stock_code=test_stock,
                start_date=start_date,
                end_date=end_date,
                period=5,  # 5分钟数据
                limit=20,  # 获取20条数据进行测试
                force_download=False  # 日常更新模式
            )
            
            if not df_minute_daily.empty:
                logger.info(f"✅ 分钟数据日常更新成功: {len(df_minute_daily)} 条数据")
                logger.info(f"📊 数据时间范围: {df_minute_daily['trade_time'].min()} 到 {df_minute_daily['trade_time'].max()}")
                logger.info(f"📊 最新收盘价: {df_minute_daily['close'].iloc[-1]}")
                logger.info("🎯 数据源：mootdx（不复权）")
            else:
                logger.warning("⚠️ 分钟数据日常更新返回空数据")
                
        except Exception as e:
            logger.error(f"❌ 分钟数据日常更新测试失败: {e}")
        
        # 测试5: 数据质量对比（如果两种模式都有数据）
        logger.info("\n📊 测试5: 数据质量对比")
        
        try:
            if 'df_force' in locals() and 'df_daily' in locals() and not df_force.empty and not df_daily.empty:
                force_price = df_force['close'].iloc[-1]
                daily_price = df_daily['close'].iloc[-1]
                price_diff = abs(force_price - daily_price)
                
                logger.info(f"强制下载（adata前复权）最新收盘价: {force_price}")
                logger.info(f"日常更新（mootdx不复权）最新收盘价: {daily_price}")
                logger.info(f"价格差异: {price_diff:.4f}")
                
                if price_diff > 0.01:
                    logger.info("✅ 数据源策略生效！前复权和不复权数据存在差异")
                else:
                    logger.info("ℹ️ 价格差异较小，可能该股票近期无除权除息")
            else:
                logger.warning("⚠️ 无法进行数据质量对比，部分测试数据为空")
                
        except Exception as e:
            logger.error(f"❌ 数据质量对比失败: {e}")

def test_configuration_validation():
    """测试配置验证"""
    
    logger.info("\n🔧 测试配置验证")
    logger.info("=" * 80)
    
    # 初始化数据库和数据采集器
    db = DatabaseManager()
    
    with MarketDataFetcher(db) as fetcher:
        # 检查配置
        config = fetcher.mootdx_data_config
        
        logger.info("📋 当前配置:")
        logger.info(f"  复权类型: {config['adjust_type']}")
        logger.info(f"  优先使用adata获取复权数据: {config.get('prefer_adata_for_adjust', True)}")
        logger.info(f"  强制下载数据量: {config['force_download_limit']}")
        logger.info(f"  默认数据量: {config['default_limit']}")
        logger.info(f"  单次请求最大数据量: {config['max_single_request']}")
        
        # 验证配置合理性
        if config['adjust_type'] == 'qfq':
            logger.info("✅ 复权类型配置正确（前复权）")
        else:
            logger.warning(f"⚠️ 复权类型配置异常: {config['adjust_type']}")
        
        if config.get('prefer_adata_for_adjust', True):
            logger.info("✅ 优先使用adata获取复权数据配置正确")
        else:
            logger.warning("⚠️ 未启用adata复权数据优先策略")

def main():
    """主测试函数"""
    
    logger.info("🚀 开始测试新的数据源选择策略")
    logger.info("💡 策略说明：")
    logger.info("   - 强制下载（force_download=True）：使用adata获取前复权数据")
    logger.info("   - 日常更新（force_download=False）：使用mootdx快速获取数据")
    logger.info("=" * 80)
    
    try:
        # 测试数据源策略
        test_data_source_strategy()
        
        # 测试配置验证
        test_configuration_validation()
        
        logger.info("\n" + "=" * 80)
        logger.info("🎉 数据源选择策略测试完成")
        logger.info("💡 总结：")
        logger.info("   - 强制下载模式确保获取前复权数据（adata）")
        logger.info("   - 日常更新模式确保快速获取数据（mootdx）")
        logger.info("   - 数据源选择策略按预期工作")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
