#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自定义时间范围的强制下载功能

验证日线数据从2015-01-01开始，分钟数据从2025-01-01开始的配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.database_manager import DatabaseManager
from data.market_fetcher import MarketDataFetcher
import pandas as pd
import logging
from datetime import datetime

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_custom_date_ranges():
    """测试自定义时间范围的数据获取"""
    
    # 初始化数据库和数据采集器
    db = DatabaseManager()
    
    with MarketDataFetcher(db) as fetcher:
        test_stock = "000001"  # 平安银行
        
        # 定义测试的时间范围
        daily_start_date = "2015-01-01"    # 日线数据从2015年开始
        minute_start_date = "2025-01-01"   # 分钟数据从2025年开始
        end_date = datetime.now().strftime('%Y-%m-%d')  # 结束时间为当前日期
        
        logger.info("🧪 测试自定义时间范围的数据获取功能")
        logger.info("=" * 80)
        logger.info(f"📅 测试配置:")
        logger.info(f"   - 日线数据时间范围: {daily_start_date} 到 {end_date}")
        logger.info(f"   - 分钟数据时间范围: {minute_start_date} 到 {end_date}")
        logger.info(f"   - 测试股票: {test_stock}")
        
        # 测试1: 日线数据（2015-01-01 到当前）
        logger.info("\n📊 测试1: 日线数据获取（2015-01-01 到当前）")
        try:
            daily_data = fetcher.get_stock_daily_data(
                stock_code=test_stock,
                start_date=daily_start_date,
                end_date=end_date,
                force_download=True  # 强制下载模式，使用adata前复权数据
            )
            
            if not daily_data.empty:
                logger.info(f"✅ 日线数据获取成功: {len(daily_data)} 条")
                logger.info(f"📊 数据时间范围: {daily_data['trade_time'].min()} 到 {daily_data['trade_time'].max()}")
                logger.info(f"📊 最新收盘价: {daily_data['close'].iloc[-1]}")
                logger.info(f"🎯 数据源: adata（前复权）")
                
                # 验证数据时间范围
                data_start = daily_data['trade_time'].min().strftime('%Y-%m-%d')
                if data_start <= daily_start_date:
                    logger.info(f"✅ 时间范围验证通过: 数据开始时间 {data_start}")
                else:
                    logger.warning(f"⚠️ 时间范围验证失败: 期望 {daily_start_date}，实际 {data_start}")
                    
            else:
                logger.warning("⚠️ 日线数据获取失败")
                
        except Exception as e:
            logger.error(f"❌ 日线数据测试失败: {e}")
        
        # 测试2: 分钟数据（2025-01-01 到当前）
        logger.info("\n📊 测试2: 5分钟数据获取（2025-01-01 到当前）")
        try:
            minute_data = fetcher.get_stock_minute_data(
                stock_code=test_stock,
                start_date=minute_start_date,
                end_date=end_date,
                period=5,
                force_download=True  # 强制下载模式，使用adata前复权数据
            )
            
            if not minute_data.empty:
                logger.info(f"✅ 5分钟数据获取成功: {len(minute_data)} 条")
                logger.info(f"📊 数据时间范围: {minute_data['trade_time'].min()} 到 {minute_data['trade_time'].max()}")
                logger.info(f"📊 最新收盘价: {minute_data['close'].iloc[-1]}")
                logger.info(f"🎯 数据源: adata（前复权）")
                
                # 验证数据时间范围
                data_start = minute_data['trade_time'].min().strftime('%Y-%m-%d')
                if data_start >= minute_start_date:
                    logger.info(f"✅ 时间范围验证通过: 数据开始时间 {data_start}")
                else:
                    logger.warning(f"⚠️ 时间范围验证失败: 期望 {minute_start_date}，实际 {data_start}")
                    
            else:
                logger.warning("⚠️ 5分钟数据获取失败（可能adata分钟数据有限制）")
                
        except Exception as e:
            logger.error(f"❌ 5分钟数据测试失败: {e}")
        
        # 测试3: 自定义时间范围的批量数据获取
        logger.info("\n📊 测试3: 自定义时间范围的批量数据获取")
        try:
            # 只测试一只股票，避免时间过长
            test_stocks = [test_stock]
            
            logger.info(f"🔄 开始批量数据获取测试（股票: {test_stocks}）")
            
            fetcher.collect_daily_incremental_data_with_custom_dates(
                daily_start_date=daily_start_date,
                minute_start_date=minute_start_date,
                end_date=end_date,
                stock_codes=test_stocks,
                force_download=True
            )
            
            logger.info("✅ 批量数据获取测试完成")
            
        except Exception as e:
            logger.error(f"❌ 批量数据获取测试失败: {e}")

def test_app_integration():
    """测试app.py集成功能"""
    
    logger.info("\n🧪 测试app.py集成功能")
    logger.info("=" * 80)
    
    try:
        from app import StockDataApp
        
        # 创建应用实例
        app = StockDataApp()
        
        logger.info("✅ StockDataApp 实例创建成功")
        logger.info("💡 可以通过以下方式调用强制下载:")
        logger.info("   python app.py --force-stock-data")
        logger.info("📅 将使用以下时间范围:")
        logger.info("   - 日线数据: 2015-01-01 到当前日期")
        logger.info("   - 分钟数据: 2025-01-01 到当前日期")
        
    except Exception as e:
        logger.error(f"❌ app.py集成测试失败: {e}")

def test_configuration_validation():
    """测试配置验证"""
    
    logger.info("\n🔧 测试配置验证")
    logger.info("=" * 80)
    
    # 验证时间配置的合理性
    daily_start_date = "2015-01-01"
    minute_start_date = "2025-01-01"
    current_date = datetime.now().strftime('%Y-%m-%d')
    
    logger.info("📋 时间范围配置验证:")
    logger.info(f"   - 日线数据开始时间: {daily_start_date}")
    logger.info(f"   - 分钟数据开始时间: {minute_start_date}")
    logger.info(f"   - 当前日期: {current_date}")
    
    # 验证日期格式
    try:
        datetime.strptime(daily_start_date, '%Y-%m-%d')
        datetime.strptime(minute_start_date, '%Y-%m-%d')
        logger.info("✅ 日期格式验证通过")
    except ValueError as e:
        logger.error(f"❌ 日期格式验证失败: {e}")
    
    # 验证时间逻辑
    daily_start = datetime.strptime(daily_start_date, '%Y-%m-%d')
    minute_start = datetime.strptime(minute_start_date, '%Y-%m-%d')
    current = datetime.strptime(current_date, '%Y-%m-%d')
    
    if daily_start < current:
        logger.info("✅ 日线数据时间范围合理")
    else:
        logger.warning("⚠️ 日线数据开始时间不应晚于当前日期")
    
    if minute_start <= current:
        logger.info("✅ 分钟数据时间范围合理")
    else:
        logger.warning("⚠️ 分钟数据开始时间晚于当前日期，可能无数据")
    
    # 计算数据量估算
    daily_days = (current - daily_start).days
    minute_days = max(0, (current - minute_start).days)
    
    logger.info("📊 数据量估算:")
    logger.info(f"   - 日线数据: 约 {daily_days} 个交易日")
    logger.info(f"   - 分钟数据: 约 {minute_days} 个交易日")
    
    if daily_days > 3000:
        logger.warning("⚠️ 日线数据量较大，下载可能需要较长时间")
    
    if minute_days > 100:
        logger.warning("⚠️ 分钟数据量较大，下载可能需要较长时间")

def main():
    """主测试函数"""
    
    logger.info("🚀 开始测试自定义时间范围的强制下载功能")
    logger.info("💡 功能说明：")
    logger.info("   - 日线数据：从2015-01-01开始，获取长期历史数据")
    logger.info("   - 分钟数据：从2025-01-01开始，获取近期数据")
    logger.info("   - 数据源策略：强制使用adata获取前复权数据")
    logger.info("=" * 80)
    
    try:
        # 测试配置验证
        test_configuration_validation()
        
        # 测试自定义时间范围数据获取
        test_custom_date_ranges()
        
        # 测试app.py集成
        test_app_integration()
        
        logger.info("\n" + "=" * 80)
        logger.info("🎉 自定义时间范围功能测试完成")
        logger.info("💡 总结：")
        logger.info("   - 配置验证：时间范围设置合理")
        logger.info("   - 数据获取：支持不同数据类型的自定义时间范围")
        logger.info("   - 集成功能：app.py支持新的强制下载策略")
        logger.info("   - 使用方法：python app.py --force-stock-data")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
