#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试概念数据修复
"""

import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.logger import setup_logger
from data.database_manager import DatabaseManager
from data.market_fetcher import MarketDataFetcher

logger = setup_logger(__name__)

def test_single_concept():
    """测试单个概念数据处理"""
    try:
        logger.info("🧪 开始简单概念数据测试...")
        
        # 初始化数据库和fetcher
        db = DatabaseManager()
        if not db.connect():
            logger.error("❌ 数据库连接失败")
            return False
            
        fetcher = MarketDataFetcher(db)
        
        # 测试概念代码
        test_index_code = "885311"
        today = pd.Timestamp.now().strftime('%Y-%m-%d')
        
        logger.info(f"🎯 测试概念代码: {test_index_code}")
        logger.info(f"📅 测试日期: {today}")
        
        # 直接调用方法并捕获详细错误
        logger.info("📊 测试日线数据...")
        try:
            result = fetcher.get_concept_ths_daily_data_incremental(test_index_code, today, today)
            logger.info(f"✅ 日线数据测试完成，结果: {result}")
        except Exception as e:
            logger.error(f"❌ 日线数据测试失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
        logger.info("📊 测试分钟数据...")
        try:
            result = fetcher.collect_concept_min_data_incremental(test_index_code, today, today)
            logger.info(f"✅ 分钟数据测试完成，结果: {result}")
        except Exception as e:
            logger.error(f"❌ 分钟数据测试失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    finally:
        if 'db' in locals():
            db.disconnect()

if __name__ == "__main__":
    test_single_concept()
