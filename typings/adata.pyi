# Type stubs for adata library
from typing import Any, Optional, Union
import pandas as pd

class stock:
    class info:
        @staticmethod
        def get_concept_ths(stock_code: str) -> Optional[pd.DataFrame]: ...
        
        @staticmethod
        def get_concept_info_ths() -> Optional[pd.DataFrame]: ...
    
    class market:
        @staticmethod
        def get_market_concept_ths(index_code: str) -> Union[pd.DataFrame, Exception]: ...
        
        @staticmethod
        def get_market_concept_min_ths(index_code: str) -> Union[pd.DataFrame, Exception]: ...
        
        @staticmethod
        def get_market_index_daily(index_code: str, k_type: int = 1) -> Union[pd.DataFrame, Exception]: ...
        
        @staticmethod
        def get_market_index_min(index_code: str) -> Union[pd.DataFrame, Exception]: ...

class sentiment:
    class mine:
        @staticmethod
        def mine_clearance_tdx(stock_code: str) -> Optional[pd.DataFrame]: ...

# 模块级别的函数和变量
def __getattr__(name: str) -> Any: ...
