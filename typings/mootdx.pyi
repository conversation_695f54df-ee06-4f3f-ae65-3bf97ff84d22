# Type stubs for mootdx library
from typing import Any, Optional, List, Dict
import pandas as pd

class Quotes:
    def __init__(self, **kwargs: Any) -> None: ...
    
    def bars(self, symbol: str, frequency: int, offset: int = 0, adjust: str = 'qfq') -> Optional[pd.DataFrame]: ...
    
    def __enter__(self) -> 'Quotes': ...
    
    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None: ...
    
    @property
    def client(self) -> Any: ...

def __getattr__(name: str) -> Any: ...
