#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试强制下载和每日更新逻辑的脚本
验证参数传递和方法调用是否正确
"""

import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_force_download_logic():
    """测试强制下载逻辑"""
    print("🔍 测试强制下载逻辑...")
    
    try:
        from app import StockDataApp
        
        # 创建应用实例
        app = StockDataApp()
        
        # 测试1: 检查 force_stock_data_task 方法是否存在
        if hasattr(app, 'force_stock_data_task'):
            print("  ✅ force_stock_data_task 方法存在")
        else:
            print("  ❌ force_stock_data_task 方法不存在")
            return False
        
        # 测试2: 检查 daily_incremental_task 方法是否存在
        if hasattr(app, 'daily_incremental_task'):
            print("  ✅ daily_incremental_task 方法存在")
        else:
            print("  ❌ daily_incremental_task 方法不存在")
            return False
        
        # 测试3: 检查 run 方法的参数
        import inspect
        run_signature = inspect.signature(app.run)
        if 'force_download' in run_signature.parameters:
            print("  ✅ run 方法包含 force_download 参数")
        else:
            print("  ❌ run 方法缺少 force_download 参数")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试强制下载逻辑失败: {e}")
        return False

def test_down_data_methods():
    """测试 DownData 类的方法"""
    print("\n🔍 测试 DownData 类方法...")
    
    try:
        from data.down_data import DownData
        
        # 检查方法是否存在
        methods_to_check = [
            'collect_daily_schedule',
            'collect_force_download_data',
            'collect_weekly_schedule'
        ]
        
        for method_name in methods_to_check:
            if hasattr(DownData, method_name):
                print(f"  ✅ {method_name} 方法存在")
            else:
                print(f"  ❌ {method_name} 方法不存在")
                return False
        
        # 检查方法签名
        import inspect
        
        # 检查 collect_daily_schedule 方法
        daily_signature = inspect.signature(DownData.collect_daily_schedule)
        expected_params = ['self', 'start_date', 'end_date']
        actual_params = list(daily_signature.parameters.keys())
        if all(param in actual_params for param in expected_params):
            print("  ✅ collect_daily_schedule 方法签名正确")
        else:
            print(f"  ❌ collect_daily_schedule 方法签名错误: {actual_params}")
            return False
        
        # 检查 collect_force_download_data 方法
        force_signature = inspect.signature(DownData.collect_force_download_data)
        expected_params = ['self', 'start_date', 'end_date']
        actual_params = list(force_signature.parameters.keys())
        if all(param in actual_params for param in expected_params):
            print("  ✅ collect_force_download_data 方法签名正确")
        else:
            print(f"  ❌ collect_force_download_data 方法签名错误: {actual_params}")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试 DownData 类方法失败: {e}")
        return False

def test_market_fetcher_methods():
    """测试 MarketDataFetcher 类的方法"""
    print("\n🔍 测试 MarketDataFetcher 类方法...")
    
    try:
        from data.market_fetcher import MarketDataFetcher
        
        # 检查关键方法的参数
        import inspect
        
        # 检查 get_stock_daily_data 方法
        daily_signature = inspect.signature(MarketDataFetcher.get_stock_daily_data)
        if 'force_download' in daily_signature.parameters:
            print("  ✅ get_stock_daily_data 包含 force_download 参数")
        else:
            print("  ❌ get_stock_daily_data 缺少 force_download 参数")
            return False
        
        # 检查 get_stock_minute_data 方法
        minute_signature = inspect.signature(MarketDataFetcher.get_stock_minute_data)
        if 'force_download' in minute_signature.parameters:
            print("  ✅ get_stock_minute_data 包含 force_download 参数")
        else:
            print("  ❌ get_stock_minute_data 缺少 force_download 参数")
            return False
        
        # 检查 collect_minute_data 方法
        collect_minute_signature = inspect.signature(MarketDataFetcher.collect_minute_data)
        if 'force_download' in collect_minute_signature.parameters:
            print("  ✅ collect_minute_data 包含 force_download 参数")
        else:
            print("  ❌ collect_minute_data 缺少 force_download 参数")
            return False
        
        # 检查 collect_daily_incremental_data 方法
        if hasattr(MarketDataFetcher, 'collect_daily_incremental_data'):
            incremental_signature = inspect.signature(MarketDataFetcher.collect_daily_incremental_data)
            if 'force_download' in incremental_signature.parameters:
                print("  ✅ collect_daily_incremental_data 包含 force_download 参数")
            else:
                print("  ❌ collect_daily_incremental_data 缺少 force_download 参数")
                return False
        else:
            print("  ❌ collect_daily_incremental_data 方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试 MarketDataFetcher 类方法失败: {e}")
        return False

def test_parameter_flow():
    """测试参数传递流程"""
    print("\n🔍 测试参数传递流程...")
    
    try:
        # 模拟参数传递流程
        print("  📋 参数传递流程检查:")
        print("    1. app.run(force_download=True)")
        print("       ↓")
        print("    2. app.force_stock_data_task()")
        print("       ↓")
        print("    3. down_data.collect_force_download_data()")
        print("       ↓")
        print("    4. fetcher.collect_daily_incremental_data(force_download=True)")
        print("       ↓")
        print("    5. fetcher.get_stock_daily_data(force_download=True)")
        print("    6. fetcher.get_stock_minute_data(force_download=True)")
        
        print("\n  📋 每日任务流程检查:")
        print("    1. app.daily_incremental_task()")
        print("       ↓")
        print("    2. down_data.collect_daily_schedule()")
        print("       ↓")
        print("    3. fetcher.collect_daily_incremental_data(force_download=False)")
        print("       ↓")
        print("    4. fetcher.get_stock_daily_data(force_download=False)")
        print("    5. fetcher.get_stock_minute_data(force_download=False)")
        
        print("  ✅ 参数传递流程逻辑正确")
        return True
        
    except Exception as e:
        print(f"  ❌ 测试参数传递流程失败: {e}")
        return False

def test_command_line_args():
    """测试命令行参数"""
    print("\n🔍 测试命令行参数...")
    
    try:
        # 检查 app.py 中的命令行参数处理
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键的命令行参数
        if '--force-stock-data' in content:
            print("  ✅ --force-stock-data 参数存在")
        else:
            print("  ❌ --force-stock-data 参数不存在")
            return False
        
        if '--daily-incremental' in content:
            print("  ✅ --daily-incremental 参数存在")
        else:
            print("  ❌ --daily-incremental 参数不存在")
            return False
        
        if 'force_stock_data_task()' in content:
            print("  ✅ force_stock_data_task() 调用存在")
        else:
            print("  ❌ force_stock_data_task() 调用不存在")
            return False
        
        if 'daily_incremental_task()' in content:
            print("  ✅ daily_incremental_task() 调用存在")
        else:
            print("  ❌ daily_incremental_task() 调用不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试命令行参数失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试强制下载和每日更新逻辑...")
    print(f"📁 项目路径: {project_root}")
    print("="*60)
    
    # 运行测试
    tests = [
        ("强制下载逻辑", test_force_download_logic),
        ("DownData类方法", test_down_data_methods),
        ("MarketDataFetcher类方法", test_market_fetcher_methods),
        ("参数传递流程", test_parameter_flow),
        ("命令行参数", test_command_line_args)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 生成总结报告
    print("\n" + "="*60)
    print("📊 强制下载和每日更新逻辑测试总结")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n📈 总体结果:")
    print(f"  通过测试: {passed}/{total}")
    print(f"  成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！强制下载和每日更新逻辑正确！")
        return True
    elif passed >= total * 0.8:
        print("\n✅ 大部分测试通过，逻辑基本正确！")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
