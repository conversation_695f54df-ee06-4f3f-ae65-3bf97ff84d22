#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的类型安全验证脚本
验证项目的核心类型安全性
"""

import sys
import importlib
from pathlib import Path
from typing import List, Dict, Any, Optional
import pandas as pd
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_core_imports():
    """测试核心模块导入"""
    print("🔍 测试核心模块导入...")
    
    modules = [
        'config.config',
        'data.market_fetcher', 
        'data.down_data',
        'utils.logger'
    ]
    
    success_count = 0
    for module in modules:
        try:
            importlib.import_module(module)
            print(f"  ✅ {module}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {module}: {e}")
    
    print(f"📊 导入成功率: {success_count}/{len(modules)} ({success_count/len(modules)*100:.1f}%)")
    return success_count == len(modules)

def test_type_annotations():
    """测试类型注解"""
    print("\n🔍 测试类型注解...")
    
    try:
        from data.market_fetcher import MarketDataFetcher
        
        # 检查关键方法的类型注解
        methods_to_check = [
            'get_stock_daily_data',
            'get_stock_minute_data',
            '__exit__'
        ]
        
        annotated_methods = 0
        for method_name in methods_to_check:
            method = getattr(MarketDataFetcher, method_name, None)
            if method and hasattr(method, '__annotations__'):
                annotations = method.__annotations__
                if annotations:  # 有类型注解
                    print(f"  ✅ MarketDataFetcher.{method_name} 有类型注解")
                    annotated_methods += 1
                else:
                    print(f"  ⚠️ MarketDataFetcher.{method_name} 类型注解为空")
            else:
                print(f"  ❌ MarketDataFetcher.{method_name} 无类型注解")
        
        print(f"📊 类型注解覆盖率: {annotated_methods}/{len(methods_to_check)} ({annotated_methods/len(methods_to_check)*100:.1f}%)")
        return annotated_methods >= len(methods_to_check) * 0.8  # 80% 通过率
        
    except Exception as e:
        print(f"  ❌ 类型注解测试失败: {e}")
        return False

def test_pandas_type_safety():
    """测试 pandas 类型安全性"""
    print("\n🔍 测试 pandas 类型安全性...")
    
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            'trade_time': [datetime.now()],
            'stock_code': ['000001'],
            'open': [10.0],
            'close': [10.5],
            'volume': [1000]
        })
        
        # 测试常见操作
        operations = []
        
        # 1. 排序操作
        try:
            sorted_data = test_data.sort_values('trade_time').reset_index(drop=True)
            operations.append(("排序操作", not sorted_data.empty))
        except Exception as e:
            operations.append(("排序操作", False))
        
        # 2. 过滤操作
        try:
            filtered_data = test_data.dropna(subset=['open', 'close'])
            operations.append(("过滤操作", not filtered_data.empty))
        except Exception as e:
            operations.append(("过滤操作", False))
        
        # 3. 类型转换
        try:
            test_data.loc[:, 'volume'] = pd.to_numeric(test_data['volume'], errors='coerce')
            operations.append(("类型转换", pd.api.types.is_numeric_dtype(test_data['volume'])))
        except Exception as e:
            operations.append(("类型转换", False))
        
        # 4. 时间处理
        try:
            test_data.loc[:, 'trade_time'] = pd.to_datetime(test_data['trade_time'])
            operations.append(("时间处理", not test_data['trade_time'].isna().any()))
        except Exception as e:
            operations.append(("时间处理", False))
        
        success_count = 0
        for op_name, success in operations:
            status = "✅" if success else "❌"
            print(f"  {status} {op_name}")
            if success:
                success_count += 1
        
        print(f"📊 pandas 操作成功率: {success_count}/{len(operations)} ({success_count/len(operations)*100:.1f}%)")
        return success_count == len(operations)
        
    except Exception as e:
        print(f"  ❌ pandas 测试失败: {e}")
        return False

def test_third_party_libs():
    """测试第三方库"""
    print("\n🔍 测试第三方库...")
    
    libs = ['mootdx', 'akshare', 'adata', 'pandas', 'numpy']
    success_count = 0
    
    for lib in libs:
        try:
            importlib.import_module(lib)
            print(f"  ✅ {lib}")
            success_count += 1
        except ImportError:
            print(f"  ⚠️ {lib} (未安装)")
        except Exception as e:
            print(f"  ❌ {lib}: {e}")
    
    print(f"📊 第三方库可用率: {success_count}/{len(libs)} ({success_count/len(libs)*100:.1f}%)")
    return success_count >= len(libs) * 0.8  # 80% 可用即可

def test_config_loading():
    """测试配置加载"""
    print("\n🔍 测试配置加载...")
    
    try:
        from config.config import settings
        
        # 检查关键配置项
        config_items = ['DATABASE', 'MOOTDX', 'LOGGING']
        available_items = 0
        
        for item in config_items:
            if hasattr(settings, item):
                print(f"  ✅ {item} 配置可用")
                available_items += 1
            else:
                print(f"  ❌ {item} 配置缺失")
        
        print(f"📊 配置完整性: {available_items}/{len(config_items)} ({available_items/len(config_items)*100:.1f}%)")
        return available_items >= len(config_items) * 0.8
        
    except Exception as e:
        print(f"  ❌ 配置加载失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始简化类型安全验证...")
    print(f"📁 项目路径: {project_root}")
    print("="*60)
    
    # 运行测试
    tests = [
        ("核心模块导入", test_core_imports),
        ("类型注解检查", test_type_annotations),
        ("pandas类型安全", test_pandas_type_safety),
        ("第三方库检查", test_third_party_libs),
        ("配置加载检查", test_config_loading)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 生成总结报告
    print("\n" + "="*60)
    print("📊 类型安全验证总结")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n📈 总体结果:")
    print(f"  通过测试: {passed}/{total}")
    print(f"  成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！项目类型安全性优秀！")
        return True
    elif passed >= total * 0.8:
        print("\n✅ 大部分测试通过，项目类型安全性良好！")
        return True
    else:
        print("\n⚠️ 部分测试失败，建议进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
