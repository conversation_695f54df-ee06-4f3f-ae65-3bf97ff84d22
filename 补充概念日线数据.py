#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
补充概念日线数据 - 专门用于补充缺失的概念日线数据
"""

import sys
import os
from datetime import datetime, timedelta
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.logger import setup_logger
from data.database_manager import DatabaseManager
from data.market_fetcher import MarketDataFetcher
from utils.trade_calendar import trade_time_manager

logger = setup_logger(__name__)

def supplement_concept_daily_data(target_date: str = "2025-07-09"):
    """
    补充指定日期的概念日线数据
    
    Args:
        target_date: 目标日期，格式 YYYY-MM-DD
    """
    try:
        logger.info(f"🚀 开始补充概念日线数据: {target_date}")
        
        # 检查是否是交易日
        trade_date = target_date.replace('-', '')
        if not trade_time_manager.is_trade_date(trade_date):
            logger.warning(f"⚠️ {target_date} 不是交易日，跳过数据补充")
            return False
        
        # 初始化数据库和fetcher
        db = DatabaseManager()
        if not db.connect():
            logger.error("❌ 数据库连接失败")
            return False
            
        fetcher = MarketDataFetcher(db)
        
        # 获取所有概念代码
        logger.info("📊 获取概念代码列表...")
        concept_codes = db.execute_query("SELECT DISTINCT index_code FROM concept_info ORDER BY index_code")
        
        if not concept_codes:
            logger.error("❌ 数据库中没有概念代码")
            return False
            
        total_concepts = len(concept_codes)
        logger.info(f"📋 找到 {total_concepts} 个概念代码")
        
        # 统计信息
        success_count = 0
        error_count = 0
        no_data_count = 0
        
        # 逐个处理概念代码
        for i, (concept_code,) in enumerate(concept_codes, 1):
            try:
                logger.info(f"📈 [{i}/{total_concepts}] 处理概念 {concept_code}...")
                
                # 检查是否已存在该日期的数据
                existing_data = db.execute_query(
                    "SELECT COUNT(*) FROM concept_daily_data WHERE index_code = %s AND DATE(trade_time) = %s",
                    (concept_code, target_date)
                )
                
                if existing_data and existing_data[0][0] > 0:
                    logger.debug(f"✅ 概念 {concept_code} 在 {target_date} 的数据已存在，跳过")
                    continue
                
                # 获取概念日线数据
                try:
                    fetcher.get_concept_ths_daily_data_incremental(concept_code, target_date, target_date)
                    success_count += 1
                    logger.debug(f"✅ 概念 {concept_code} 数据补充成功")
                    
                except Exception as fetch_error:
                    if "无数据" in str(fetch_error) or "为空" in str(fetch_error):
                        no_data_count += 1
                        logger.debug(f"⚠️ 概念 {concept_code} 在 {target_date} 无数据")
                    else:
                        error_count += 1
                        logger.warning(f"❌ 概念 {concept_code} 数据获取失败: {fetch_error}")
                
                # 每处理50个概念输出一次进度
                if i % 50 == 0:
                    logger.info(f"📊 进度: {i}/{total_concepts} ({i/total_concepts*100:.1f}%) - 成功:{success_count}, 无数据:{no_data_count}, 错误:{error_count}")
                    
            except Exception as e:
                error_count += 1
                logger.error(f"❌ 处理概念 {concept_code} 时发生错误: {e}")
                continue
        
        # 输出最终统计
        logger.info("🎉 概念日线数据补充完成！")
        logger.info(f"📊 最终统计:")
        logger.info(f"   总概念数: {total_concepts}")
        logger.info(f"   成功补充: {success_count}")
        logger.info(f"   无数据: {no_data_count}")
        logger.info(f"   错误: {error_count}")
        logger.info(f"   成功率: {success_count/(total_concepts-no_data_count)*100:.1f}%")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 补充概念日线数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    finally:
        if 'db' in locals():
            db.disconnect()

def supplement_multiple_dates(start_date: str, end_date: str):
    """
    补充多个日期的概念日线数据
    
    Args:
        start_date: 开始日期，格式 YYYY-MM-DD
        end_date: 结束日期，格式 YYYY-MM-DD
    """
    try:
        logger.info(f"🚀 开始补充多日期概念日线数据: {start_date} 到 {end_date}")
        
        # 生成日期列表
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        current_dt = start_dt
        success_dates = []
        
        while current_dt <= end_dt:
            date_str = current_dt.strftime('%Y-%m-%d')
            logger.info(f"📅 处理日期: {date_str}")
            
            if supplement_concept_daily_data(date_str):
                success_dates.append(date_str)
            
            current_dt += timedelta(days=1)
        
        logger.info(f"🎉 多日期补充完成！成功处理 {len(success_dates)} 个日期")
        return True
        
    except Exception as e:
        logger.error(f"❌ 多日期补充失败: {e}")
        return False

def check_missing_dates():
    """
    检查最近几天缺失的概念日线数据
    """
    try:
        logger.info("🔍 检查最近缺失的概念日线数据...")
        
        db = DatabaseManager()
        if not db.connect():
            logger.error("❌ 数据库连接失败")
            return
            
        # 检查最近7天的数据完整性
        for i in range(7):
            check_date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
            trade_date = check_date.replace('-', '')
            
            if not trade_time_manager.is_trade_date(trade_date):
                continue
                
            # 统计该日期的概念数据条数
            data_count = db.execute_query(
                "SELECT COUNT(DISTINCT index_code) FROM concept_daily_data WHERE DATE(trade_time) = %s",
                (check_date,)
            )
            
            # 获取总概念数
            total_concepts = db.execute_query("SELECT COUNT(*) FROM concept_info")
            
            if data_count and total_concepts:
                actual_count = data_count[0][0]
                expected_count = total_concepts[0][0]
                completion_rate = actual_count / expected_count * 100 if expected_count > 0 else 0
                
                logger.info(f"📊 {check_date}: {actual_count}/{expected_count} ({completion_rate:.1f}%)")
                
                if completion_rate < 80:  # 如果完成率低于80%，标记为需要补充
                    logger.warning(f"⚠️ {check_date} 数据不完整，建议补充")
        
        db.disconnect()
        
    except Exception as e:
        logger.error(f"❌ 检查缺失数据失败: {e}")

if __name__ == "__main__":
    logger.info("🚀 概念日线数据补充工具启动")
    
    # 检查缺失数据
    check_missing_dates()
    
    # 补充2025-07-09的数据
    target_date = "2025-07-09"
    logger.info(f"🎯 开始补充 {target_date} 的概念日线数据...")
    
    success = supplement_concept_daily_data(target_date)
    
    if success:
        logger.info(f"🎉 {target_date} 概念日线数据补充完成！")
    else:
        logger.error(f"❌ {target_date} 概念日线数据补充失败！")
    
    # 如果需要补充多个日期，可以取消下面的注释
    # supplement_multiple_dates("2025-07-08", "2025-07-09")
