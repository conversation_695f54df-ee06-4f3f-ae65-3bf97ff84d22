#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试概念数据问题
"""

import pandas as pd
import sys
import os
import adata as ad

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.logger import setup_logger

logger = setup_logger(__name__)

def debug_concept_data_structure():
    """调试概念数据结构"""
    try:
        logger.info("🔍 开始调试概念数据结构...")
        
        # 测试获取概念数据
        test_index_code = "885311"
        logger.info(f"📊 获取概念 {test_index_code} 的原始数据...")
        
        # 获取日线数据
        logger.info("📈 获取日线数据...")
        con_daily_data = ad.stock.market.get_market_concept_ths(index_code=test_index_code)
        
        if con_daily_data is None or con_daily_data.empty:
            logger.error("❌ 概念日线数据为空")
            return
            
        logger.info(f"📊 日线数据形状: {con_daily_data.shape}")
        logger.info(f"📊 日线数据列: {list(con_daily_data.columns)}")
        logger.info(f"📊 日线数据类型:")
        for col in con_daily_data.columns:
            logger.info(f"   {col}: {con_daily_data[col].dtype}")
            
        # 检查 trade_time 列的具体内容
        if 'trade_time' in con_daily_data.columns:
            logger.info(f"📊 trade_time 前5个值:")
            for i, val in enumerate(con_daily_data['trade_time'].head()):
                logger.info(f"   [{i}]: {val} (类型: {type(val)})")
                
            # 尝试转换时间
            logger.info("🔄 尝试时间转换...")
            try:
                converted_time = pd.to_datetime(con_daily_data['trade_time'], errors='coerce')
                logger.info(f"✅ 时间转换成功，转换后类型: {converted_time.dtype}")
                logger.info(f"📊 转换后前5个值:")
                for i, val in enumerate(converted_time.head()):
                    logger.info(f"   [{i}]: {val}")
                    
                # 检查是否有 NaT 值
                nat_count = converted_time.isna().sum()
                logger.info(f"📊 NaT 值数量: {nat_count}/{len(converted_time)}")
                
                if nat_count == len(converted_time):
                    logger.error("❌ 所有时间值都转换失败")
                else:
                    # 尝试使用 .dt 访问器
                    try:
                        normalized_time = converted_time.dt.normalize()
                        logger.info("✅ .dt.normalize() 成功")
                    except Exception as dt_error:
                        logger.error(f"❌ .dt.normalize() 失败: {dt_error}")
                        
            except Exception as convert_error:
                logger.error(f"❌ 时间转换失败: {convert_error}")
        
        # 获取分钟数据
        logger.info("📊 获取分钟数据...")
        try:
            con_min_data = ad.stock.market.get_market_concept_min_ths(test_index_code)
            
            if con_min_data is None or con_min_data.empty:
                logger.warning("⚠️ 概念分钟数据为空")
            else:
                logger.info(f"📊 分钟数据形状: {con_min_data.shape}")
                logger.info(f"📊 分钟数据列: {list(con_min_data.columns)}")
                logger.info(f"📊 分钟数据类型:")
                for col in con_min_data.columns:
                    logger.info(f"   {col}: {con_min_data[col].dtype}")
                    
                # 检查数值列
                for col in ['volume', 'amount']:
                    if col in con_min_data.columns:
                        logger.info(f"📊 {col} 前5个值:")
                        for i, val in enumerate(con_min_data[col].head()):
                            logger.info(f"   [{i}]: {val} (类型: {type(val)})")
                            
                        # 尝试数值转换
                        try:
                            numeric_col = pd.to_numeric(con_min_data[col], errors='coerce')
                            logger.info(f"✅ {col} 数值转换成功")
                        except Exception as numeric_error:
                            logger.error(f"❌ {col} 数值转换失败: {numeric_error}")
                            
        except Exception as min_error:
            logger.error(f"❌ 获取分钟数据失败: {min_error}")
            
    except Exception as e:
        logger.error(f"❌ 调试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    debug_concept_data_structure()
