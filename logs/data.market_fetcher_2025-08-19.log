2025-08-19 17:54:31,024 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-08-19 17:54:31,024 - data.market_fetcher - INFO - 📊 数据源配置:
2025-08-19 17:54:31,024 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-08-19 17:54:31,024 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-08-19 17:54:31,024 - data.market_fetcher - INFO -    概念数据: adata
2025-08-19 17:54:31,024 - data.market_fetcher - INFO -    风险数据: adata
2025-08-19 17:54:31,024 - data.market_fetcher - INFO -    指数数据: adata
2025-08-19 17:54:31,024 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-08-19 17:54:31,024 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-08-19 17:54:31,024 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-08-19 17:54:31,024 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-08-19 17:54:31,252 - data.market_fetcher - WARNING - ⚠️ mootdx返回空数据：股票000001
2025-08-19 17:54:31,252 - data.market_fetcher - WARNING - ⚠️ mootdx获取股票000001日线数据为空
2025-08-19 17:54:31,252 - data.market_fetcher - ERROR - ❌ mootdx无法获取股票000001的日线数据
2025-08-19 17:54:31,294 - data.market_fetcher - WARNING - ⚠️ mootdx返回空数据：股票000001 5分钟
2025-08-19 17:54:31,294 - data.market_fetcher - WARNING - ⚠️ mootdx获取股票000001 5分钟数据为空
2025-08-19 17:54:31,294 - data.market_fetcher - ERROR - ❌ mootdx无法获取股票000001的5分钟数据
2025-08-19 17:54:31,294 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-08-19 17:54:31,295 - data.market_fetcher - INFO - 📊 数据源配置:
2025-08-19 17:54:31,295 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-08-19 17:54:31,295 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-08-19 17:54:31,295 - data.market_fetcher - INFO -    概念数据: adata
2025-08-19 17:54:31,295 - data.market_fetcher - INFO -    风险数据: adata
2025-08-19 17:54:31,295 - data.market_fetcher - INFO -    指数数据: adata
2025-08-19 17:54:31,295 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-08-19 17:54:31,295 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-08-19 17:54:31,295 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-08-19 17:54:31,295 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-08-19 17:54:31,335 - data.market_fetcher - WARNING - ⚠️ mootdx返回空数据：股票000001
2025-08-19 17:54:31,336 - data.market_fetcher - WARNING - ⚠️ mootdx获取股票000001日线数据为空
2025-08-19 17:54:31,336 - data.market_fetcher - ERROR - ❌ mootdx无法获取股票000001的日线数据
2025-08-19 17:54:31,376 - data.market_fetcher - WARNING - ⚠️ mootdx返回空数据：股票000001
2025-08-19 17:54:31,376 - data.market_fetcher - WARNING - ⚠️ mootdx获取股票000001日线数据为空
2025-08-19 17:54:31,376 - data.market_fetcher - ERROR - ❌ mootdx无法获取股票000001的日线数据
2025-08-19 18:27:07,150 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-08-19 18:27:07,150 - data.market_fetcher - INFO - 📊 数据源配置:
2025-08-19 18:27:07,150 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-08-19 18:27:07,151 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-08-19 18:27:07,151 - data.market_fetcher - INFO -    概念数据: adata
2025-08-19 18:27:07,151 - data.market_fetcher - INFO -    风险数据: adata
2025-08-19 18:27:07,151 - data.market_fetcher - INFO -    指数数据: adata
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000001最近2000条日线数据
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000001）
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:27:07,284 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 242条（股票000001）
2025-08-19 18:27:07,284 - data.market_fetcher - INFO - 📊 数据时间范围: 2024-01-02 00:00:00 到 2024-12-31 00:00:00
2025-08-19 18:27:07,507 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000001 5分钟数据2000条
2025-08-19 18:27:07,507 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000001）
2025-08-19 18:27:07,507 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:27:07,592 - data.market_fetcher - ERROR - ❌ adata返回空5分钟数据（股票000001），强制下载模式不允许回退
2025-08-19 18:27:07,640 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-08-19 18:27:07,640 - data.market_fetcher - INFO - 📊 数据源配置:
2025-08-19 18:27:07,640 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-08-19 18:27:07,640 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-08-19 18:27:07,640 - data.market_fetcher - INFO -    概念数据: adata
2025-08-19 18:27:07,640 - data.market_fetcher - INFO -    风险数据: adata
2025-08-19 18:27:07,640 - data.market_fetcher - INFO -    指数数据: adata
2025-08-19 18:27:07,641 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-08-19 18:27:07,641 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-08-19 18:27:07,641 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-08-19 18:27:07,641 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-08-19 18:41:18,051 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-08-19 18:41:18,051 - data.market_fetcher - INFO - 📊 数据源配置:
2025-08-19 18:41:18,052 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-08-19 18:41:18,052 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-08-19 18:41:18,052 - data.market_fetcher - INFO -    概念数据: adata
2025-08-19 18:41:18,052 - data.market_fetcher - INFO -    风险数据: adata
2025-08-19 18:41:18,052 - data.market_fetcher - INFO -    指数数据: adata
2025-08-19 18:41:18,052 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-08-19 18:41:18,052 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-08-19 18:41:18,052 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-08-19 18:41:18,052 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-08-19 18:41:18,052 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000001最近2000条日线数据
2025-08-19 18:41:18,052 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000001）
2025-08-19 18:41:18,052 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:41:18,283 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000001）
2025-08-19 18:41:18,283 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-31 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:41:18,284 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000001 5分钟数据2000条
2025-08-19 18:41:18,284 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000001）
2025-08-19 18:41:18,285 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:41:18,390 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000001）
2025-08-19 18:41:18,391 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:41:18,392 - data.market_fetcher - INFO - 📊 日线数据范围: 2015-01-01 到 2025-08-19
2025-08-19 18:41:18,392 - data.market_fetcher - INFO - 📊 分钟数据范围: 2025-01-01 到 2025-08-19
2025-08-19 18:41:18,392 - data.market_fetcher - INFO - 🔄 开始股票数据更新: 强制下载模式（自定义时间范围）
2025-08-19 18:41:18,392 - data.market_fetcher - INFO - 📊 股票数量: 1
2025-08-19 18:41:18,392 - data.market_fetcher - INFO - 💡 数据源策略: adata（前复权）
2025-08-19 18:41:18,396 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000001最近2000条日线数据
2025-08-19 18:41:18,396 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000001）
2025-08-19 18:41:18,396 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:41:18,549 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000001）
2025-08-19 18:41:18,550 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-31 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:41:19,746 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000001 5分钟数据2000条
2025-08-19 18:41:19,746 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000001）
2025-08-19 18:41:19,746 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:41:19,918 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000001）
2025-08-19 18:41:19,919 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:41:20,477 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000001 15分钟数据2000条
2025-08-19 18:41:20,477 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000001）
2025-08-19 18:41:20,477 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:41:20,601 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000001）
2025-08-19 18:41:20,602 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:41:20,947 - data.market_fetcher - INFO - ✅ 股票数据更新完成 (强制下载模式（自定义时间范围）)
2025-08-19 18:41:20,947 - data.market_fetcher - INFO - 📊 处理结果统计:
2025-08-19 18:41:20,947 - data.market_fetcher - INFO -    - 日线数据: 1/1 (100.0%)
2025-08-19 18:41:20,947 - data.market_fetcher - INFO -    - 5分钟数据: 1/1 (100.0%)
2025-08-19 18:41:20,948 - data.market_fetcher - INFO -    - 15分钟数据: 1/1 (100.0%)
2025-08-19 18:41:20,948 - data.market_fetcher - INFO -    - 评分数据: 1/1 (100.0%)
2025-08-19 18:44:09,285 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-08-19 18:44:09,285 - data.market_fetcher - INFO - 📊 数据源配置:
2025-08-19 18:44:09,285 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-08-19 18:44:09,285 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-08-19 18:44:09,285 - data.market_fetcher - INFO -    概念数据: adata
2025-08-19 18:44:09,285 - data.market_fetcher - INFO -    风险数据: adata
2025-08-19 18:44:09,285 - data.market_fetcher - INFO -    指数数据: adata
2025-08-19 18:44:09,285 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-08-19 18:44:09,285 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-08-19 18:44:09,285 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-08-19 18:44:09,285 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-08-19 18:44:09,286 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-08-19 18:44:09,286 - data.market_fetcher - INFO - 📊 数据源配置:
2025-08-19 18:44:09,286 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-08-19 18:44:09,286 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-08-19 18:44:09,286 - data.market_fetcher - INFO -    概念数据: adata
2025-08-19 18:44:09,286 - data.market_fetcher - INFO -    风险数据: adata
2025-08-19 18:44:09,286 - data.market_fetcher - INFO -    指数数据: adata
2025-08-19 18:44:09,286 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-08-19 18:44:09,286 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-08-19 18:44:09,286 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-08-19 18:44:09,286 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-08-19 18:44:20,792 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-08-19 18:44:20,793 - data.market_fetcher - INFO - 📊 数据源配置:
2025-08-19 18:44:20,793 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-08-19 18:44:20,793 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-08-19 18:44:20,793 - data.market_fetcher - INFO -    概念数据: adata
2025-08-19 18:44:20,793 - data.market_fetcher - INFO -    风险数据: adata
2025-08-19 18:44:20,793 - data.market_fetcher - INFO -    指数数据: adata
2025-08-19 18:44:20,793 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-08-19 18:44:20,793 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-08-19 18:44:20,793 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-08-19 18:44:20,793 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-08-19 18:44:20,793 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-08-19 18:44:20,793 - data.market_fetcher - INFO - 📊 数据源配置:
2025-08-19 18:44:20,793 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-08-19 18:44:20,793 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-08-19 18:44:20,793 - data.market_fetcher - INFO -    概念数据: adata
2025-08-19 18:44:20,793 - data.market_fetcher - INFO -    风险数据: adata
2025-08-19 18:44:20,793 - data.market_fetcher - INFO -    指数数据: adata
2025-08-19 18:44:20,793 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-08-19 18:44:20,793 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-08-19 18:44:20,793 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-08-19 18:44:20,794 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-08-19 18:44:20,806 - data.market_fetcher - INFO - ✅ 从数据库获取到 4392 只股票
2025-08-19 18:44:20,806 - data.market_fetcher - INFO - 📊 日线数据范围: 2015-01-01 到 2025-08-19
2025-08-19 18:44:20,806 - data.market_fetcher - INFO - 📊 分钟数据范围: 2025-01-01 到 2025-08-19
2025-08-19 18:44:20,806 - data.market_fetcher - INFO - 🔄 开始股票数据更新: 强制下载模式（自定义时间范围）
2025-08-19 18:44:20,806 - data.market_fetcher - INFO - 📊 股票数量: 4392
2025-08-19 18:44:20,806 - data.market_fetcher - INFO - 💡 数据源策略: adata（前复权）
2025-08-19 18:44:20,810 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000001最近2000条日线数据
2025-08-19 18:44:20,810 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000001）
2025-08-19 18:44:20,810 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:21,685 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000001）
2025-08-19 18:44:21,686 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-31 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:44:22,305 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000001 5分钟数据2000条
2025-08-19 18:44:22,305 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000001）
2025-08-19 18:44:22,305 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:23,684 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000001）
2025-08-19 18:44:23,684 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:44:24,127 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000001 15分钟数据2000条
2025-08-19 18:44:24,127 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000001）
2025-08-19 18:44:24,127 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:24,453 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000001）
2025-08-19 18:44:24,454 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:44:26,037 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000002最近2000条日线数据
2025-08-19 18:44:26,037 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000002）
2025-08-19 18:44:26,037 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:26,205 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000002）
2025-08-19 18:44:26,205 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-23 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:44:26,725 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000002 5分钟数据2000条
2025-08-19 18:44:26,725 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000002）
2025-08-19 18:44:26,725 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:28,575 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000002）
2025-08-19 18:44:28,575 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:44:28,965 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000002 15分钟数据2000条
2025-08-19 18:44:28,965 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000002）
2025-08-19 18:44:28,965 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:29,082 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000002）
2025-08-19 18:44:29,082 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:44:29,289 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000006最近2000条日线数据
2025-08-19 18:44:29,289 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000006）
2025-08-19 18:44:29,289 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:29,450 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000006）
2025-08-19 18:44:29,451 - data.market_fetcher - INFO - 📊 数据时间范围: 2016-12-02 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:44:30,012 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000006 5分钟数据2000条
2025-08-19 18:44:30,012 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000006）
2025-08-19 18:44:30,012 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:30,141 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000006）
2025-08-19 18:44:30,141 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:44:30,563 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000006 15分钟数据2000条
2025-08-19 18:44:30,563 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000006）
2025-08-19 18:44:30,563 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:30,662 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000006）
2025-08-19 18:44:30,663 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:44:30,863 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000007最近2000条日线数据
2025-08-19 18:44:30,864 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000007）
2025-08-19 18:44:30,864 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:31,029 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000007）
2025-08-19 18:44:31,030 - data.market_fetcher - INFO - 📊 数据时间范围: 2015-05-15 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:44:31,557 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000007 5分钟数据2000条
2025-08-19 18:44:31,557 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000007）
2025-08-19 18:44:31,557 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:31,944 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000007）
2025-08-19 18:44:31,945 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:44:32,337 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000007 15分钟数据2000条
2025-08-19 18:44:32,337 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000007）
2025-08-19 18:44:32,337 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:33,448 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000007）
2025-08-19 18:44:33,448 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:44:33,663 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000008最近2000条日线数据
2025-08-19 18:44:33,663 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000008）
2025-08-19 18:44:33,664 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:34,929 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000008）
2025-08-19 18:44:34,929 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-03-07 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:44:35,449 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000008 5分钟数据2000条
2025-08-19 18:44:35,449 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000008）
2025-08-19 18:44:35,450 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:35,575 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000008）
2025-08-19 18:44:35,576 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:44:35,965 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000008 15分钟数据2000条
2025-08-19 18:44:35,965 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000008）
2025-08-19 18:44:35,965 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:36,058 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000008）
2025-08-19 18:44:36,059 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:44:36,275 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000009最近2000条日线数据
2025-08-19 18:44:36,276 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000009）
2025-08-19 18:44:36,276 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:36,452 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000009）
2025-08-19 18:44:36,453 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-31 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:44:36,977 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000009 5分钟数据2000条
2025-08-19 18:44:36,978 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000009）
2025-08-19 18:44:36,978 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:37,127 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000009）
2025-08-19 18:44:37,128 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:44:37,531 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000009 15分钟数据2000条
2025-08-19 18:44:37,531 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000009）
2025-08-19 18:44:37,531 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:37,611 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000009）
2025-08-19 18:44:37,611 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:44:39,140 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000010最近2000条日线数据
2025-08-19 18:44:39,140 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000010）
2025-08-19 18:44:39,140 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:39,301 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000010）
2025-08-19 18:44:39,302 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-25 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:44:39,830 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000010 5分钟数据2000条
2025-08-19 18:44:39,830 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000010）
2025-08-19 18:44:39,830 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:40,006 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000010）
2025-08-19 18:44:40,006 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:44:40,407 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000010 15分钟数据2000条
2025-08-19 18:44:40,407 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000010）
2025-08-19 18:44:40,408 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:40,557 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000010）
2025-08-19 18:44:40,557 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:44:40,841 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000011最近2000条日线数据
2025-08-19 18:44:40,842 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000011）
2025-08-19 18:44:40,842 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:41,265 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000011）
2025-08-19 18:44:41,266 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-31 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:44:41,782 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000011 5分钟数据2000条
2025-08-19 18:44:41,782 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000011）
2025-08-19 18:44:41,782 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:42,984 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000011）
2025-08-19 18:44:42,985 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:44:43,384 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000011 15分钟数据2000条
2025-08-19 18:44:43,385 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000011）
2025-08-19 18:44:43,385 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:43,469 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000011）
2025-08-19 18:44:43,469 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:44:43,675 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000012最近2000条日线数据
2025-08-19 18:44:43,675 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000012）
2025-08-19 18:44:43,675 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:43,837 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000012）
2025-08-19 18:44:43,838 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-31 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:44:44,373 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000012 5分钟数据2000条
2025-08-19 18:44:44,373 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000012）
2025-08-19 18:44:44,373 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:54,619 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000012）
2025-08-19 18:44:54,620 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:44:55,046 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000012 15分钟数据2000条
2025-08-19 18:44:55,046 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000012）
2025-08-19 18:44:55,046 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:55,140 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000012）
2025-08-19 18:44:55,140 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:44:55,336 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000014最近2000条日线数据
2025-08-19 18:44:55,336 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000014）
2025-08-19 18:44:55,336 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:56,457 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000014）
2025-08-19 18:44:56,458 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-31 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:44:56,985 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000014 5分钟数据2000条
2025-08-19 18:44:56,985 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000014）
2025-08-19 18:44:56,985 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:57,106 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000014）
2025-08-19 18:44:57,107 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:44:57,503 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000014 15分钟数据2000条
2025-08-19 18:44:57,503 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000014）
2025-08-19 18:44:57,503 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:58,333 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000014）
2025-08-19 18:44:58,333 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:44:58,523 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000016最近2000条日线数据
2025-08-19 18:44:58,523 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000016）
2025-08-19 18:44:58,523 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:58,686 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000016）
2025-08-19 18:44:58,687 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-02 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:44:59,217 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000016 5分钟数据2000条
2025-08-19 18:44:59,217 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000016）
2025-08-19 18:44:59,217 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:59,494 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000016）
2025-08-19 18:44:59,495 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:44:59,899 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000016 15分钟数据2000条
2025-08-19 18:44:59,899 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000016）
2025-08-19 18:44:59,899 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:44:59,972 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000016）
2025-08-19 18:44:59,973 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:45:00,163 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000017最近2000条日线数据
2025-08-19 18:45:00,163 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000017）
2025-08-19 18:45:00,163 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:01,249 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000017）
2025-08-19 18:45:01,249 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-25 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:45:01,759 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000017 5分钟数据2000条
2025-08-19 18:45:01,759 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000017）
2025-08-19 18:45:01,759 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:02,123 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000017）
2025-08-19 18:45:02,124 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:45:02,517 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000017 15分钟数据2000条
2025-08-19 18:45:02,517 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000017）
2025-08-19 18:45:02,517 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:02,605 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000017）
2025-08-19 18:45:02,606 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:45:02,803 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000019最近2000条日线数据
2025-08-19 18:45:02,803 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000019）
2025-08-19 18:45:02,803 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:02,972 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000019）
2025-08-19 18:45:02,973 - data.market_fetcher - INFO - 📊 数据时间范围: 2016-09-30 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:45:03,495 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000019 5分钟数据2000条
2025-08-19 18:45:03,495 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000019）
2025-08-19 18:45:03,495 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:03,642 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000019）
2025-08-19 18:45:03,642 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:45:04,049 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000019 15分钟数据2000条
2025-08-19 18:45:04,049 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000019）
2025-08-19 18:45:04,049 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:04,169 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000019）
2025-08-19 18:45:04,170 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:45:04,371 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000020最近2000条日线数据
2025-08-19 18:45:04,371 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000020）
2025-08-19 18:45:04,371 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:04,535 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000020）
2025-08-19 18:45:04,535 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-02-20 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:45:05,041 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000020 5分钟数据2000条
2025-08-19 18:45:05,042 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000020）
2025-08-19 18:45:05,042 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:05,413 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000020）
2025-08-19 18:45:05,413 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:45:05,815 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000020 15分钟数据2000条
2025-08-19 18:45:05,815 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000020）
2025-08-19 18:45:05,815 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:06,149 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000020）
2025-08-19 18:45:06,150 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:45:06,381 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000021最近2000条日线数据
2025-08-19 18:45:06,381 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000021）
2025-08-19 18:45:06,382 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:06,537 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000021）
2025-08-19 18:45:06,538 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-31 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:45:07,039 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000021 5分钟数据2000条
2025-08-19 18:45:07,039 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000021）
2025-08-19 18:45:07,040 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:07,172 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000021）
2025-08-19 18:45:07,173 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:45:07,567 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000021 15分钟数据2000条
2025-08-19 18:45:07,567 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000021）
2025-08-19 18:45:07,567 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:08,686 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000021）
2025-08-19 18:45:08,687 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:45:08,925 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000025最近2000条日线数据
2025-08-19 18:45:08,926 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000025）
2025-08-19 18:45:08,926 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:11,082 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000025）
2025-08-19 18:45:11,082 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-19 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:45:11,577 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000025 5分钟数据2000条
2025-08-19 18:45:11,578 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000025）
2025-08-19 18:45:11,578 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:11,950 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000025）
2025-08-19 18:45:11,951 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:45:12,369 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000025 15分钟数据2000条
2025-08-19 18:45:12,370 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000025）
2025-08-19 18:45:12,370 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:12,454 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000025）
2025-08-19 18:45:12,455 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:45:12,728 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000026最近2000条日线数据
2025-08-19 18:45:12,728 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000026）
2025-08-19 18:45:12,728 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:12,883 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000026）
2025-08-19 18:45:12,884 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-31 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:45:13,394 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000026 5分钟数据2000条
2025-08-19 18:45:13,394 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000026）
2025-08-19 18:45:13,394 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:13,505 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000026）
2025-08-19 18:45:13,506 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:45:14,007 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000026 15分钟数据2000条
2025-08-19 18:45:14,008 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000026）
2025-08-19 18:45:14,008 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:14,106 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000026）
2025-08-19 18:45:14,106 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:45:14,338 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000027最近2000条日线数据
2025-08-19 18:45:14,338 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000027）
2025-08-19 18:45:14,338 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:14,504 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000027）
2025-08-19 18:45:14,505 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-31 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:45:15,002 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000027 5分钟数据2000条
2025-08-19 18:45:15,002 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000027）
2025-08-19 18:45:15,002 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:15,100 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000027）
2025-08-19 18:45:15,100 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:45:15,513 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000027 15分钟数据2000条
2025-08-19 18:45:15,514 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000027）
2025-08-19 18:45:15,514 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:15,585 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000027）
2025-08-19 18:45:15,585 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:45:17,130 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000028最近2000条日线数据
2025-08-19 18:45:17,130 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000028）
2025-08-19 18:45:17,130 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:17,998 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000028）
2025-08-19 18:45:17,998 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-31 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:45:18,548 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000028 5分钟数据2000条
2025-08-19 18:45:18,548 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000028）
2025-08-19 18:45:18,548 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:18,883 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000028）
2025-08-19 18:45:18,883 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:45:19,278 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000028 15分钟数据2000条
2025-08-19 18:45:19,278 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000028）
2025-08-19 18:45:19,278 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:19,639 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000028）
2025-08-19 18:45:19,640 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:45:19,912 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000029最近2000条日线数据
2025-08-19 18:45:19,912 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000029）
2025-08-19 18:45:19,912 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:20,540 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 1578条（股票000029）
2025-08-19 18:45:20,540 - data.market_fetcher - INFO - 📊 数据时间范围: 2015-01-05 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:45:20,970 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000029 5分钟数据2000条
2025-08-19 18:45:20,970 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000029）
2025-08-19 18:45:20,970 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:21,081 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000029）
2025-08-19 18:45:21,082 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:45:21,538 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000029 15分钟数据2000条
2025-08-19 18:45:21,538 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000029）
2025-08-19 18:45:21,538 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:21,869 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000029）
2025-08-19 18:45:21,870 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:45:22,096 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000030最近2000条日线数据
2025-08-19 18:45:22,096 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000030）
2025-08-19 18:45:22,096 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:22,230 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000030）
2025-08-19 18:45:22,230 - data.market_fetcher - INFO - 📊 数据时间范围: 2017-05-31 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:45:22,728 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000030 5分钟数据2000条
2025-08-19 18:45:22,728 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000030）
2025-08-19 18:45:22,728 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:22,825 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000030）
2025-08-19 18:45:22,825 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:45:23,226 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000030 15分钟数据2000条
2025-08-19 18:45:23,226 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000030）
2025-08-19 18:45:23,226 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:23,324 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000030）
2025-08-19 18:45:23,324 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:45:23,550 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000031最近2000条日线数据
2025-08-19 18:45:23,550 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000031）
2025-08-19 18:45:23,550 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:25,176 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000031）
2025-08-19 18:45:25,176 - data.market_fetcher - INFO - 📊 数据时间范围: 2016-08-19 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:45:25,710 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000031 5分钟数据2000条
2025-08-19 18:45:25,710 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000031）
2025-08-19 18:45:25,710 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:26,906 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000031）
2025-08-19 18:45:26,906 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:45:27,304 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000031 15分钟数据2000条
2025-08-19 18:45:27,304 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000031）
2025-08-19 18:45:27,304 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:27,370 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000031）
2025-08-19 18:45:27,370 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:45:27,568 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000032最近2000条日线数据
2025-08-19 18:45:27,568 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000032）
2025-08-19 18:45:27,568 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:27,698 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000032）
2025-08-19 18:45:27,698 - data.market_fetcher - INFO - 📊 数据时间范围: 2016-10-19 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:45:28,220 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000032 5分钟数据2000条
2025-08-19 18:45:28,220 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000032）
2025-08-19 18:45:28,220 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:28,353 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000032）
2025-08-19 18:45:28,354 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:45:28,748 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000032 15分钟数据2000条
2025-08-19 18:45:28,748 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000032）
2025-08-19 18:45:28,748 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:28,826 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000032）
2025-08-19 18:45:28,826 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
2025-08-19 18:45:29,088 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000034最近2000条日线数据
2025-08-19 18:45:29,088 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000034）
2025-08-19 18:45:29,088 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:29,845 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 2000条（股票000034）
2025-08-19 18:45:29,846 - data.market_fetcher - INFO - 📊 数据时间范围: 2016-05-27 00:00:00 到 2025-08-19 00:00:00
2025-08-19 18:45:30,372 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000034 5分钟数据2000条
2025-08-19 18:45:30,372 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000034）
2025-08-19 18:45:30,372 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:30,481 - data.market_fetcher - INFO - ✅ adata获取前复权5分钟数据成功: 1536条（股票000034）
2025-08-19 18:45:30,482 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:35:00 到 2025-08-19 15:00:00
2025-08-19 18:45:30,894 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000034 15分钟数据2000条
2025-08-19 18:45:30,894 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权15分钟数据（股票000034）
2025-08-19 18:45:30,894 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:45:30,982 - data.market_fetcher - INFO - ✅ adata获取前复权15分钟数据成功: 512条（股票000034）
2025-08-19 18:45:30,983 - data.market_fetcher - INFO - 📊 数据时间范围: 2025-07-07 09:45:00 到 2025-08-19 15:00:00
