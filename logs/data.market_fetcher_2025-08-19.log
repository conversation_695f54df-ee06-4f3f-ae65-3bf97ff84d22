2025-08-19 17:54:31,024 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-08-19 17:54:31,024 - data.market_fetcher - INFO - 📊 数据源配置:
2025-08-19 17:54:31,024 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-08-19 17:54:31,024 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-08-19 17:54:31,024 - data.market_fetcher - INFO -    概念数据: adata
2025-08-19 17:54:31,024 - data.market_fetcher - INFO -    风险数据: adata
2025-08-19 17:54:31,024 - data.market_fetcher - INFO -    指数数据: adata
2025-08-19 17:54:31,024 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-08-19 17:54:31,024 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-08-19 17:54:31,024 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-08-19 17:54:31,024 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-08-19 17:54:31,252 - data.market_fetcher - WARNING - ⚠️ mootdx返回空数据：股票000001
2025-08-19 17:54:31,252 - data.market_fetcher - WARNING - ⚠️ mootdx获取股票000001日线数据为空
2025-08-19 17:54:31,252 - data.market_fetcher - ERROR - ❌ mootdx无法获取股票000001的日线数据
2025-08-19 17:54:31,294 - data.market_fetcher - WARNING - ⚠️ mootdx返回空数据：股票000001 5分钟
2025-08-19 17:54:31,294 - data.market_fetcher - WARNING - ⚠️ mootdx获取股票000001 5分钟数据为空
2025-08-19 17:54:31,294 - data.market_fetcher - ERROR - ❌ mootdx无法获取股票000001的5分钟数据
2025-08-19 17:54:31,294 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-08-19 17:54:31,295 - data.market_fetcher - INFO - 📊 数据源配置:
2025-08-19 17:54:31,295 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-08-19 17:54:31,295 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-08-19 17:54:31,295 - data.market_fetcher - INFO -    概念数据: adata
2025-08-19 17:54:31,295 - data.market_fetcher - INFO -    风险数据: adata
2025-08-19 17:54:31,295 - data.market_fetcher - INFO -    指数数据: adata
2025-08-19 17:54:31,295 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-08-19 17:54:31,295 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-08-19 17:54:31,295 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-08-19 17:54:31,295 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-08-19 17:54:31,335 - data.market_fetcher - WARNING - ⚠️ mootdx返回空数据：股票000001
2025-08-19 17:54:31,336 - data.market_fetcher - WARNING - ⚠️ mootdx获取股票000001日线数据为空
2025-08-19 17:54:31,336 - data.market_fetcher - ERROR - ❌ mootdx无法获取股票000001的日线数据
2025-08-19 17:54:31,376 - data.market_fetcher - WARNING - ⚠️ mootdx返回空数据：股票000001
2025-08-19 17:54:31,376 - data.market_fetcher - WARNING - ⚠️ mootdx获取股票000001日线数据为空
2025-08-19 17:54:31,376 - data.market_fetcher - ERROR - ❌ mootdx无法获取股票000001的日线数据
2025-08-19 18:27:07,150 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-08-19 18:27:07,150 - data.market_fetcher - INFO - 📊 数据源配置:
2025-08-19 18:27:07,150 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-08-19 18:27:07,151 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-08-19 18:27:07,151 - data.market_fetcher - INFO -    概念数据: adata
2025-08-19 18:27:07,151 - data.market_fetcher - INFO -    风险数据: adata
2025-08-19 18:27:07,151 - data.market_fetcher - INFO -    指数数据: adata
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000001最近2000条日线数据
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权数据（股票000001）
2025-08-19 18:27:07,151 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:27:07,284 - data.market_fetcher - INFO - ✅ adata获取前复权日线数据成功: 242条（股票000001）
2025-08-19 18:27:07,284 - data.market_fetcher - INFO - 📊 数据时间范围: 2024-01-02 00:00:00 到 2024-12-31 00:00:00
2025-08-19 18:27:07,507 - data.market_fetcher - INFO - 🔄 强制下载模式：获取股票000001 5分钟数据2000条
2025-08-19 18:27:07,507 - data.market_fetcher - INFO - 📊 强制下载模式：强制使用adata获取前复权5分钟数据（股票000001）
2025-08-19 18:27:07,507 - data.market_fetcher - INFO - 💡 数据源策略：adata（前复权）- 确保数据质量，不回退到mootdx
2025-08-19 18:27:07,592 - data.market_fetcher - ERROR - ❌ adata返回空5分钟数据（股票000001），强制下载模式不允许回退
2025-08-19 18:27:07,640 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-08-19 18:27:07,640 - data.market_fetcher - INFO - 📊 数据源配置:
2025-08-19 18:27:07,640 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-08-19 18:27:07,640 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-08-19 18:27:07,640 - data.market_fetcher - INFO -    概念数据: adata
2025-08-19 18:27:07,640 - data.market_fetcher - INFO -    风险数据: adata
2025-08-19 18:27:07,640 - data.market_fetcher - INFO -    指数数据: adata
2025-08-19 18:27:07,641 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-08-19 18:27:07,641 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-08-19 18:27:07,641 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-08-19 18:27:07,641 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
