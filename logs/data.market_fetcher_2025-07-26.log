2025-07-26 22:18:42,619 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-07-26 22:18:42,619 - data.market_fetcher - INFO - 📊 数据源配置:
2025-07-26 22:18:42,619 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-07-26 22:18:42,619 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-07-26 22:18:42,619 - data.market_fetcher - INFO -    概念数据: adata
2025-07-26 22:18:42,620 - data.market_fetcher - INFO -    风险数据: adata
2025-07-26 22:18:42,620 - data.market_fetcher - INFO -    指数数据: adata
2025-07-26 22:18:42,620 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-07-26 22:18:42,621 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-07-26 22:18:42,621 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-07-26 22:18:42,621 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-07-26 22:18:42,621 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-07-26 22:18:42,622 - data.market_fetcher - INFO - 📊 数据源配置:
2025-07-26 22:18:42,622 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-07-26 22:18:42,622 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-07-26 22:18:42,622 - data.market_fetcher - INFO -    概念数据: adata
2025-07-26 22:18:42,622 - data.market_fetcher - INFO -    风险数据: adata
2025-07-26 22:18:42,622 - data.market_fetcher - INFO -    指数数据: adata
2025-07-26 22:18:42,622 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-07-26 22:18:42,623 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-07-26 22:18:42,623 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-07-26 22:18:42,623 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
