2025-07-08 16:11:44,792 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-08 16:11:44,813 - __main__ - INFO - ✅ 数据库连接成功
2025-07-08 16:11:44,816 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-08 16:11:44,816 - __main__ - INFO - ⏰ 分时段采集定时任务设置完成
2025-07-08 16:11:44,816 - __main__ - INFO - 📋 任务计划：
2025-07-08 16:11:44,817 - __main__ - INFO -    01:00 - 刷新交易日历（每日）
2025-07-08 16:11:44,817 - __main__ - INFO -    15:30 - 采集当日股票数据（每日，含智能完整性检查）
2025-07-08 16:11:44,817 - __main__ - INFO -    20:00 - 数据完整性检查和补充（每日，针对日线数据延迟）
2025-07-08 16:11:44,817 - __main__ - INFO -    02:00 - 更新基础信息（每周三）
2025-07-08 16:11:44,817 - __main__ - INFO - 💡 优化特性：智能数据完整性检查、自动补充机制、延迟重试策略
2025-07-08 16:11:44,817 - __main__ - INFO - 🔄 进入主循环
2025-07-08 16:12:01,028 - __main__ - INFO - 📅 前一天 20250708 是交易日，开始盘后数据采集
2025-07-08 16:37:44,354 - __main__ - INFO - ⏹️ 收到停止信号
2025-07-08 16:37:44,356 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-08 17:42:59,459 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-08 17:42:59,481 - __main__ - INFO - ✅ 数据库连接成功
2025-07-08 17:42:59,484 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-08 17:42:59,484 - __main__ - INFO - ⏰ 分时段采集定时任务设置完成
2025-07-08 17:42:59,484 - __main__ - INFO - 📋 任务计划：
2025-07-08 17:42:59,484 - __main__ - INFO -    01:00 - 刷新交易日历（每日）
2025-07-08 17:42:59,484 - __main__ - INFO -    15:30 - 采集当日股票数据（每日，含智能完整性检查）
2025-07-08 17:42:59,484 - __main__ - INFO -    20:00 - 数据完整性检查和补充（每日，针对日线数据延迟）
2025-07-08 17:42:59,484 - __main__ - INFO -    02:00 - 更新基础信息（每周三）
2025-07-08 17:42:59,484 - __main__ - INFO - 💡 优化特性：智能数据完整性检查、自动补充机制、延迟重试策略
2025-07-08 17:42:59,484 - __main__ - INFO - 🔄 进入主循环
2025-07-08 17:44:00,746 - __main__ - INFO - 📅 前一天 20250708 是交易日，开始盘后数据采集
2025-07-08 18:17:18,667 - __main__ - INFO - ⏹️ 收到停止信号
2025-07-08 18:17:18,669 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-08 18:29:19,917 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-08 18:29:19,940 - __main__ - INFO - ✅ 数据库连接成功
2025-07-08 18:29:19,944 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-08 18:29:19,945 - __main__ - INFO - ⏰ 分时段采集定时任务设置完成
2025-07-08 18:29:19,945 - __main__ - INFO - 📋 任务计划：
2025-07-08 18:29:19,945 - __main__ - INFO -    01:00 - 刷新交易日历（每日）
2025-07-08 18:29:19,945 - __main__ - INFO -    15:30 - 采集当日股票数据（每日，含智能完整性检查）
2025-07-08 18:29:19,945 - __main__ - INFO -    20:00 - 数据完整性检查和补充（每日，针对日线数据延迟）
2025-07-08 18:29:19,945 - __main__ - INFO -    02:00 - 更新基础信息（每周三）
2025-07-08 18:29:19,945 - __main__ - INFO - 💡 优化特性：智能数据完整性检查、自动补充机制、延迟重试策略
2025-07-08 18:29:19,945 - __main__ - INFO - 🔄 进入主循环
2025-07-08 18:29:25,019 - __main__ - INFO - ⏹️ 收到停止信号
2025-07-08 18:29:25,019 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-08 18:29:38,893 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-08 18:29:38,916 - __main__ - INFO - ✅ 数据库连接成功
2025-07-08 18:29:38,919 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-08 18:29:38,920 - __main__ - INFO - ⏰ 分时段采集定时任务设置完成
2025-07-08 18:29:38,920 - __main__ - INFO - 📋 任务计划：
2025-07-08 18:29:38,920 - __main__ - INFO -    01:00 - 刷新交易日历（每日）
2025-07-08 18:29:38,920 - __main__ - INFO -    15:30 - 采集当日股票数据（每日，含智能完整性检查）
2025-07-08 18:29:38,920 - __main__ - INFO -    20:00 - 数据完整性检查和补充（每日，针对日线数据延迟）
2025-07-08 18:29:38,920 - __main__ - INFO -    02:00 - 更新基础信息（每周三）
2025-07-08 18:29:38,920 - __main__ - INFO - 💡 优化特性：智能数据完整性检查、自动补充机制、延迟重试策略
2025-07-08 18:29:38,920 - __main__ - INFO - 🔄 进入主循环
2025-07-08 18:30:01,173 - __main__ - INFO - 📅 前一天 20250708 是交易日，开始盘后数据采集
2025-07-08 19:17:01,101 - __main__ - INFO - 📰 开始采集新闻数据
2025-07-08 19:17:01,137 - __main__ - ERROR - ❌ 新闻数据采集失败: 'MarketDataFetcher' object has no attribute 'collect_cx_news'
2025-07-08 19:17:01,137 - __main__ - INFO - ✅ 盘后数据采集完成
2025-07-08 20:00:00,194 - __main__ - INFO - 📅 今天 20250708 是交易日，开始数据补充检查
2025-07-08 20:00:01,186 - __main__ - INFO - ✅ 数据补充检查完成
2025-07-08 21:16:24,802 - __main__ - INFO - ⏹️ 收到停止信号
2025-07-08 21:16:24,804 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-08 21:16:27,058 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-08 21:16:27,078 - __main__ - INFO - ✅ 数据库连接成功
2025-07-08 21:16:27,081 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-08 21:16:27,082 - __main__ - INFO - ⏰ 分时段采集定时任务设置完成
2025-07-08 21:16:27,082 - __main__ - INFO - 📋 任务计划：
2025-07-08 21:16:27,082 - __main__ - INFO -    01:00 - 刷新交易日历（每日）
2025-07-08 21:16:27,082 - __main__ - INFO -    15:30 - 采集当日股票数据（每日，含智能完整性检查）
2025-07-08 21:16:27,082 - __main__ - INFO -    20:00 - 数据完整性检查和补充（每日，针对日线数据延迟）
2025-07-08 21:16:27,082 - __main__ - INFO -    02:00 - 更新基础信息（每周三）
2025-07-08 21:16:27,082 - __main__ - INFO - 💡 优化特性：智能数据完整性检查、自动补充机制、延迟重试策略
2025-07-08 21:16:27,082 - __main__ - INFO - 🔄 进入主循环
2025-07-08 21:17:14,044 - __main__ - INFO - ⏹️ 收到停止信号
2025-07-08 21:17:14,045 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-08 21:17:24,163 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-08 21:17:24,183 - __main__ - INFO - ✅ 数据库连接成功
2025-07-08 21:17:24,186 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-08 21:17:24,187 - __main__ - INFO - ⏰ 分时段采集定时任务设置完成
2025-07-08 21:17:24,187 - __main__ - INFO - 📋 任务计划：
2025-07-08 21:17:24,187 - __main__ - INFO -    01:00 - 刷新交易日历（每日）
2025-07-08 21:17:24,187 - __main__ - INFO -    15:30 - 采集当日股票数据（每日，含智能完整性检查）
2025-07-08 21:17:24,187 - __main__ - INFO -    20:00 - 数据完整性检查和补充（每日，针对日线数据延迟）
2025-07-08 21:17:24,187 - __main__ - INFO -    02:00 - 更新基础信息（每周三）
2025-07-08 21:17:24,187 - __main__ - INFO - 💡 优化特性：智能数据完整性检查、自动补充机制、延迟重试策略
2025-07-08 21:17:24,187 - __main__ - INFO - 🔄 进入主循环
2025-07-08 21:18:00,459 - __main__ - INFO - 📅 前一天 20250708 是交易日，开始盘后数据采集
2025-07-08 21:28:46,093 - __main__ - INFO - ⏹️ 收到停止信号
2025-07-08 21:28:46,094 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-08 21:28:51,132 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-08 21:28:51,154 - __main__ - INFO - ✅ 数据库连接成功
2025-07-08 21:28:51,157 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-08 21:28:51,157 - __main__ - INFO - ⏰ 分时段采集定时任务设置完成
2025-07-08 21:28:51,157 - __main__ - INFO - 📋 任务计划：
2025-07-08 21:28:51,157 - __main__ - INFO -    01:00 - 刷新交易日历（每日）
2025-07-08 21:28:51,157 - __main__ - INFO -    15:30 - 采集当日股票数据（每日，含智能完整性检查）
2025-07-08 21:28:51,157 - __main__ - INFO -    20:00 - 数据完整性检查和补充（每日，针对日线数据延迟）
2025-07-08 21:28:51,157 - __main__ - INFO -    02:00 - 更新基础信息（每周三）
2025-07-08 21:28:51,157 - __main__ - INFO - 💡 优化特性：智能数据完整性检查、自动补充机制、延迟重试策略
2025-07-08 21:28:51,158 - __main__ - INFO - 🔄 进入主循环
2025-07-08 21:29:00,369 - __main__ - INFO - 📅 前一天 20250708 是交易日，开始盘后数据采集
2025-07-08 21:55:42,857 - __main__ - INFO - 📰 开始采集新闻数据
2025-07-08 21:55:42,880 - __main__ - ERROR - ❌ 新闻数据采集失败: 'MarketDataFetcher' object has no attribute 'collect_cx_news'
2025-07-08 21:55:42,880 - __main__ - INFO - ✅ 盘后数据采集完成
