2025-07-10 06:33:27,453 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-10 06:33:27,476 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 06:33:27,478 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-10 06:33:27,478 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-10 07:16:08,341 - __main__ - INFO - 🔍 测试单个指数数据下载: 000001
2025-07-10 07:16:08,342 - __main__ - INFO - 📅 日期范围: 2025-07-03 到 2025-07-10
2025-07-10 07:16:08,342 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 07:16:08,343 - __main__ - INFO - ✅ 市场数据获取器初始化成功
2025-07-10 07:16:08,343 - __main__ - INFO - 📊 开始下载指数 000001 日线数据...
2025-07-10 07:16:08,506 - __main__ - INFO - ✅ 指数 000001 日线数据下载成功
2025-07-10 07:16:08,506 - __main__ - INFO - 📊 开始下载指数 000001 分钟数据...
2025-07-10 07:16:08,690 - __main__ - INFO - ✅ 指数 000001 分钟数据下载成功
2025-07-10 07:16:08,690 - __main__ - INFO - ✅ 指数 000001 测试完成
2025-07-10 07:17:55,692 - __main__ - INFO - 🔍 调试指数数据结构
2025-07-10 07:17:55,692 - __main__ - INFO - 📊 获取指数 000001 的数据...
2025-07-10 07:17:55,817 - __main__ - INFO - ✅ 获取到 7 条记录
2025-07-10 07:17:55,817 - __main__ - INFO - 📋 数据列名: ['index_code', 'trade_date', 'trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount', 'change', 'change_pct']
2025-07-10 07:17:55,817 - __main__ - INFO - 📊 数据类型:
2025-07-10 07:17:55,818 - __main__ - INFO -    index_code: object
2025-07-10 07:17:55,818 - __main__ - INFO -    trade_date: object
2025-07-10 07:17:55,818 - __main__ - INFO -    trade_time: object
2025-07-10 07:17:55,818 - __main__ - INFO -    open: float64
2025-07-10 07:17:55,818 - __main__ - INFO -    high: float64
2025-07-10 07:17:55,818 - __main__ - INFO -    low: float64
2025-07-10 07:17:55,818 - __main__ - INFO -    close: float64
2025-07-10 07:17:55,818 - __main__ - INFO -    volume: float64
2025-07-10 07:17:55,818 - __main__ - INFO -    amount: float64
2025-07-10 07:17:55,819 - __main__ - INFO -    change: float64
2025-07-10 07:17:55,819 - __main__ - INFO -    change_pct: float64
2025-07-10 07:17:55,819 - __main__ - INFO - 📅 前5行数据:
2025-07-10 07:17:55,830 - __main__ - INFO - 
  index_code  trade_date           trade_time  ...        amount  change  change_pct
0     000001  2025-07-01  2025-07-01 00:00:00  ...  5.535565e+11   13.32        0.39
1     000001  2025-07-02  2025-07-02 00:00:00  ...  5.431307e+11   -2.96       -0.09
2     000001  2025-07-03  2025-07-03 00:00:00  ...  5.002118e+11    6.36        0.18
3     000001  2025-07-04  2025-07-04 00:00:00  ...  5.672413e+11   11.17        0.32
4     000001  2025-07-07  2025-07-07 00:00:00  ...  4.761974e+11    0.81        0.02

[5 rows x 11 columns]
2025-07-10 07:17:55,830 - __main__ - INFO - 🕐 trade_time字段详细信息:
2025-07-10 07:17:55,830 - __main__ - INFO -    类型: <class 'str'>
2025-07-10 07:17:55,830 - __main__ - INFO -    数据类型: object
2025-07-10 07:17:55,830 - __main__ - INFO -    前5个值: ['2025-07-01 00:00:00', '2025-07-02 00:00:00', '2025-07-03 00:00:00', '2025-07-04 00:00:00', '2025-07-07 00:00:00']
2025-07-10 07:17:55,832 - __main__ - INFO - ✅ datetime转换成功
2025-07-10 07:17:55,832 - __main__ - INFO -    转换后类型: datetime64[ns]
2025-07-10 07:17:55,832 - __main__ - INFO -    转换后前5个值: [Timestamp('2025-07-01 00:00:00'), Timestamp('2025-07-02 00:00:00'), Timestamp('2025-07-03 00:00:00'), Timestamp('2025-07-04 00:00:00'), Timestamp('2025-07-07 00:00:00')]
2025-07-10 07:17:55,832 - __main__ - INFO - ⚠️ 没有时区信息
2025-07-10 07:17:55,842 - __main__ - INFO - ✅ 时区添加成功
2025-07-10 07:18:33,722 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 07:18:42,685 - __main__ - INFO - 🔍 测试单个指数数据下载: 000001
2025-07-10 07:18:42,686 - __main__ - INFO - 📅 日期范围: 2025-07-03 到 2025-07-10
2025-07-10 07:18:42,686 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 07:18:42,687 - __main__ - INFO - ✅ 市场数据获取器初始化成功
2025-07-10 07:18:42,688 - __main__ - INFO - 📊 开始下载指数 000001 日线数据...
2025-07-10 07:18:42,833 - __main__ - INFO - ✅ 指数 000001 日线数据下载成功
2025-07-10 07:18:42,833 - __main__ - INFO - 📊 开始下载指数 000001 分钟数据...
2025-07-10 07:18:42,997 - __main__ - INFO - ✅ 指数 000001 分钟数据下载成功
2025-07-10 07:18:42,997 - __main__ - INFO - ✅ 指数 000001 测试完成
2025-07-10 07:18:42,997 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 07:18:42,997 - __main__ - INFO - 🎉 测试成功
2025-07-10 07:19:09,346 - __main__ - INFO - 🔍 测试单个指数数据下载: 000001
2025-07-10 07:19:09,346 - __main__ - INFO - 📅 日期范围: 2025-07-03 到 2025-07-10
2025-07-10 07:19:09,346 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 07:19:09,348 - __main__ - INFO - ✅ 市场数据获取器初始化成功
2025-07-10 07:19:09,348 - __main__ - INFO - 📊 开始下载指数 000001 日线数据...
2025-07-10 07:19:09,531 - __main__ - INFO - ✅ 指数 000001 日线数据下载成功
2025-07-10 07:19:09,531 - __main__ - INFO - 📊 开始下载指数 000001 分钟数据...
2025-07-10 07:19:09,670 - __main__ - INFO - ✅ 指数 000001 分钟数据下载成功
2025-07-10 07:19:09,670 - __main__ - INFO - ✅ 指数 000001 测试完成
2025-07-10 07:19:09,670 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 07:19:09,671 - __main__ - INFO - 🎉 测试成功
2025-07-10 07:19:19,034 - __main__ - INFO - 🔍 检查指数 000001 最近 7 天的数据
2025-07-10 07:19:19,035 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 07:19:19,227 - __main__ - INFO - 📊 指数 000001 日线数据: 5 条记录
2025-07-10 07:19:19,227 - __main__ - INFO - 📅 时间范围: 2025-07-03 00:00:00+08:00 到 2025-07-09 00:00:00+08:00
2025-07-10 07:19:19,230 - __main__ - INFO - 📊 指数 000001 5分钟数据: 240 条记录
2025-07-10 07:19:19,230 - __main__ - INFO - 📅 时间范围: 2025-07-03 09:35:00+08:00 到 2025-07-09 15:00:00+08:00
2025-07-10 07:19:19,230 - __main__ - INFO - 📊 指数 000001 15分钟数据: 80 条记录
2025-07-10 07:19:19,230 - __main__ - INFO - 📅 时间范围: 2025-07-03 09:45:00+08:00 到 2025-07-09 15:00:00+08:00
2025-07-10 07:19:19,230 - __main__ - INFO - ✅ 指数 000001 数据检查完成
2025-07-10 07:19:19,230 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 07:19:19,230 - __main__ - INFO - 🎉 检查完成
2025-07-10 07:19:28,418 - __main__ - INFO - 🚀 开始下载指数数据
2025-07-10 07:19:28,418 - __main__ - INFO - 📅 日期范围: 2025-07-08 到 2025-07-10
2025-07-10 07:19:28,418 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 07:19:28,420 - __main__ - INFO - ✅ 市场数据获取器初始化成功
2025-07-10 07:19:28,420 - __main__ - INFO - 📊 开始下载指数数据...
2025-07-10 07:19:30,471 - __main__ - INFO - ✅ 指数数据下载完成
2025-07-10 07:19:30,471 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 07:19:30,471 - __main__ - INFO - 🎉 下载成功
2025-07-10 07:24:14,238 - __main__ - INFO - 🚀 开始下载指数数据
2025-07-10 07:24:14,238 - __main__ - INFO - 📅 日期范围: 2025-06-10 到 2025-07-10
2025-07-10 07:24:14,239 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 07:24:14,241 - __main__ - INFO - ✅ 市场数据获取器初始化成功
2025-07-10 07:24:14,241 - __main__ - INFO - 📊 开始下载指数数据...
2025-07-10 07:24:16,358 - __main__ - INFO - ✅ 指数数据下载完成
2025-07-10 07:24:16,359 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 07:24:16,359 - __main__ - INFO - 🎉 下载成功
2025-07-10 07:25:24,015 - __main__ - INFO - 🔍 测试单个指数数据下载: 000001
2025-07-10 07:25:24,015 - __main__ - INFO - 📅 日期范围: 2025-07-03 到 2025-07-10
2025-07-10 07:25:24,015 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 07:25:24,017 - __main__ - INFO - ✅ 市场数据获取器初始化成功
2025-07-10 07:25:24,017 - __main__ - INFO - 📊 开始下载指数 000001 日线数据...
2025-07-10 07:25:24,216 - __main__ - INFO - ✅ 指数 000001 日线数据下载成功
2025-07-10 07:25:24,216 - __main__ - INFO - 📊 开始下载指数 000001 分钟数据...
2025-07-10 07:25:24,375 - __main__ - INFO - ✅ 指数 000001 分钟数据下载成功
2025-07-10 07:25:24,375 - __main__ - INFO - ✅ 指数 000001 测试完成
2025-07-10 07:25:24,375 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 07:25:24,375 - __main__ - INFO - 🎉 测试成功
2025-07-10 07:25:51,555 - __main__ - INFO - 🚀 开始下载指数数据
2025-07-10 07:25:51,555 - __main__ - INFO - 📅 日期范围: 2025-07-09 到 2025-07-10
2025-07-10 07:25:51,555 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 07:25:51,557 - __main__ - INFO - ✅ 市场数据获取器初始化成功
2025-07-10 07:25:51,557 - __main__ - INFO - 📊 开始下载指数数据...
2025-07-10 07:25:53,454 - __main__ - INFO - ✅ 指数数据下载完成
2025-07-10 07:25:53,455 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 07:25:53,455 - __main__ - INFO - 🎉 下载成功
2025-07-10 07:26:37,098 - __main__ - INFO - 🔍 测试单个指数数据下载: 000001
2025-07-10 07:26:37,098 - __main__ - INFO - 📅 日期范围: 2025-07-03 到 2025-07-10
2025-07-10 07:26:37,098 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 07:26:37,100 - __main__ - INFO - ✅ 市场数据获取器初始化成功
2025-07-10 07:26:37,100 - __main__ - INFO - 📊 开始下载指数 000001 日线数据...
2025-07-10 07:26:37,300 - __main__ - INFO - ✅ 指数 000001 日线数据下载成功
2025-07-10 07:26:37,300 - __main__ - INFO - 📊 开始下载指数 000001 分钟数据...
2025-07-10 07:26:37,460 - __main__ - INFO - ✅ 指数 000001 分钟数据下载成功
2025-07-10 07:26:37,460 - __main__ - INFO - ✅ 指数 000001 测试完成
2025-07-10 07:26:37,461 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 07:26:37,461 - __main__ - INFO - 🎉 测试成功
2025-07-10 07:26:46,408 - __main__ - INFO - 🚀 开始下载指数数据
2025-07-10 07:26:46,408 - __main__ - INFO - 📅 日期范围: 2025-07-09 到 2025-07-10
2025-07-10 07:26:46,408 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 07:26:46,410 - __main__ - INFO - ✅ 市场数据获取器初始化成功
2025-07-10 07:26:46,410 - __main__ - INFO - 📊 开始下载指数数据...
2025-07-10 07:26:48,379 - __main__ - INFO - ✅ 指数数据下载完成
2025-07-10 07:26:48,379 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 07:26:48,380 - __main__ - INFO - 🎉 下载成功
2025-07-10 07:26:57,754 - __main__ - INFO - 🔍 检查指数 000001 最近 3 天的数据
2025-07-10 07:26:57,754 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 07:26:57,944 - __main__ - INFO - 📊 指数 000001 日线数据: 3 条记录
2025-07-10 07:26:57,944 - __main__ - INFO - 📅 时间范围: 2025-07-07 00:00:00+08:00 到 2025-07-09 00:00:00+08:00
2025-07-10 07:26:57,946 - __main__ - INFO - 📊 指数 000001 5分钟数据: 144 条记录
2025-07-10 07:26:57,946 - __main__ - INFO - 📅 时间范围: 2025-07-07 09:35:00+08:00 到 2025-07-09 15:00:00+08:00
2025-07-10 07:26:57,946 - __main__ - INFO - 📊 指数 000001 15分钟数据: 48 条记录
2025-07-10 07:26:57,946 - __main__ - INFO - 📅 时间范围: 2025-07-07 09:45:00+08:00 到 2025-07-09 15:00:00+08:00
2025-07-10 07:26:57,946 - __main__ - INFO - ✅ 指数 000001 数据检查完成
2025-07-10 07:26:57,946 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 07:26:57,946 - __main__ - INFO - 🎉 检查完成
2025-07-10 08:57:25,778 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-10 08:57:25,797 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 08:57:25,799 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-10 08:57:25,799 - __main__ - INFO - ⏰ 优化版定时任务设置完成
2025-07-10 08:57:25,800 - __main__ - INFO - 📅 任务调度:
2025-07-10 08:57:25,800 - __main__ - INFO -    - 每日15:30: 增量数据更新（日线+分钟线+指数+概念）
2025-07-10 08:57:25,800 - __main__ - INFO -    - 每周三02:00: 批量数据更新（概念全量+股票信息）
2025-07-10 08:57:25,800 - __main__ - INFO -    - 每日01:00: 交易日历刷新
2025-07-10 08:57:25,800 - __main__ - INFO -    - 每4小时: 新闻数据采集
2025-07-10 08:57:25,800 - __main__ - INFO - 🔄 进入主循环
2025-07-10 17:30:27,425 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-10 17:30:27,501 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 17:30:27,507 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-10 17:30:27,507 - __main__ - INFO - ⏰ 优化版定时任务设置完成
2025-07-10 17:30:27,507 - __main__ - INFO - 📅 任务调度:
2025-07-10 17:30:27,507 - __main__ - INFO -    - 每日15:30: 增量数据更新（日线+分钟线+指数+概念）
2025-07-10 17:30:27,507 - __main__ - INFO -    - 每周三02:00: 批量数据更新（概念全量+股票信息）
2025-07-10 17:30:27,508 - __main__ - INFO -    - 每日01:00: 交易日历刷新
2025-07-10 17:30:27,508 - __main__ - INFO -    - 每4小时: 新闻数据采集
2025-07-10 17:30:27,508 - __main__ - INFO - 🔄 进入主循环
2025-07-10 17:30:30,116 - __main__ - INFO - ⏹️ 收到停止信号
2025-07-10 17:30:30,116 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 17:42:33,949 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-10 17:42:34,039 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 17:42:34,045 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-10 17:42:34,046 - __main__ - INFO - ⏰ 优化版定时任务设置完成
2025-07-10 17:42:34,046 - __main__ - INFO - 📅 任务调度:
2025-07-10 17:42:34,046 - __main__ - INFO -    - 每日15:30: 增量数据更新（日线+分钟线+指数+概念）
2025-07-10 17:42:34,046 - __main__ - INFO -    - 每周三02:00: 批量数据更新（概念全量+股票信息）
2025-07-10 17:42:34,046 - __main__ - INFO -    - 每日01:00: 交易日历刷新
2025-07-10 17:42:34,047 - __main__ - INFO -    - 每4小时: 新闻数据采集
2025-07-10 17:42:34,047 - __main__ - INFO - 🔄 进入主循环
2025-07-10 17:43:00,336 - __main__ - INFO - 📅 今天 20250710 是交易日，开始每日增量数据更新
2025-07-10 18:19:07,721 - __main__ - INFO - ✅ 每日增量数据更新完成
2025-07-10 18:56:18,912 - __main__ - INFO - ⏹️ 收到停止信号
2025-07-10 18:56:18,912 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 18:56:47,934 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-10 18:56:48,052 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 18:56:48,057 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-10 18:56:48,408 - __main__ - INFO - 📅 今天 20250710 是交易日，开始每日增量数据更新
2025-07-10 18:57:02,501 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-10 18:57:20,211 - __main__ - INFO - 🚀 开始概念数据修复测试...
2025-07-10 18:57:20,212 - __main__ - INFO - 🧪 测试边界情况...
2025-07-10 18:57:20,213 - __main__ - INFO - ✅ 空DataFrame处理测试通过
2025-07-10 18:57:20,217 - __main__ - INFO - 🧪 开始测试概念数据时间处理修复...
2025-07-10 18:57:20,307 - __main__ - INFO - 📊 测试概念日线数据处理...
2025-07-10 18:57:20,324 - __main__ - INFO - 🎯 使用概念代码 885311 进行测试
2025-07-10 18:57:20,487 - __main__ - INFO - ✅ 概念日线数据处理测试通过
2025-07-10 18:57:20,487 - __main__ - INFO - 📊 测试概念分钟数据处理...
2025-07-10 18:57:20,908 - __main__ - ERROR - ❌ 概念分钟数据处理测试失败: Expected numeric dtype, got object instead.
2025-07-10 18:57:20,908 - __main__ - INFO - 🎉 概念数据时间处理修复测试完成
2025-07-10 18:57:20,908 - __main__ - INFO - 🎉 所有测试通过！概念数据时间处理修复成功
2025-07-10 19:00:22,943 - __main__ - INFO - 🚀 开始概念数据修复测试...
2025-07-10 19:00:22,943 - __main__ - INFO - 🧪 测试边界情况...
2025-07-10 19:00:22,944 - __main__ - INFO - ✅ 空DataFrame处理测试通过
2025-07-10 19:00:22,947 - __main__ - INFO - 🧪 开始测试概念数据时间处理修复...
2025-07-10 19:00:23,040 - __main__ - INFO - 🎯 测试概念代码: 885311
2025-07-10 19:00:23,041 - __main__ - INFO - 📊 测试概念日线数据处理...
2025-07-10 19:00:30,193 - __main__ - INFO - ✅ 概念 885311 日线数据处理测试通过
2025-07-10 19:00:30,193 - __main__ - INFO - 📊 测试概念分钟数据处理...
2025-07-10 19:00:30,378 - __main__ - ERROR - ❌ 概念 885311 分钟数据处理测试失败: Expected numeric dtype, got object instead.
2025-07-10 19:00:30,380 - __main__ - INFO - 🎉 概念数据时间处理修复测试完成: 1/2 通过
2025-07-10 19:00:30,381 - __main__ - INFO - 🎉 所有测试通过！概念数据时间处理修复成功
2025-07-10 19:01:18,741 - __main__ - INFO - 🔍 开始调试概念数据结构...
2025-07-10 19:01:18,741 - __main__ - INFO - 📊 获取概念 885311 的原始数据...
2025-07-10 19:01:18,741 - __main__ - INFO - 📈 获取日线数据...
2025-07-10 19:01:18,900 - __main__ - INFO - 📊 日线数据形状: (1800, 11)
2025-07-10 19:01:18,901 - __main__ - INFO - 📊 日线数据列: ['index_code', 'trade_time', 'trade_date', 'open', 'high', 'low', 'close', 'volume', 'amount', 'change', 'change_pct']
2025-07-10 19:01:18,901 - __main__ - INFO - 📊 日线数据类型:
2025-07-10 19:01:18,902 - __main__ - INFO -    index_code: object
2025-07-10 19:01:18,902 - __main__ - INFO -    trade_time: object
2025-07-10 19:01:18,902 - __main__ - INFO -    trade_date: object
2025-07-10 19:01:18,902 - __main__ - INFO -    open: object
2025-07-10 19:01:18,903 - __main__ - INFO -    high: object
2025-07-10 19:01:18,903 - __main__ - INFO -    low: object
2025-07-10 19:01:18,903 - __main__ - INFO -    close: object
2025-07-10 19:01:18,903 - __main__ - INFO -    volume: object
2025-07-10 19:01:18,904 - __main__ - INFO -    amount: object
2025-07-10 19:01:18,904 - __main__ - INFO -    change: object
2025-07-10 19:01:18,904 - __main__ - INFO -    change_pct: object
2025-07-10 19:01:18,904 - __main__ - INFO - 📊 trade_time 前5个值:
2025-07-10 19:01:18,905 - __main__ - INFO -    [0]: 2018-02-05 00:00:00 (类型: <class 'str'>)
2025-07-10 19:01:18,905 - __main__ - INFO -    [1]: 2018-02-06 00:00:00 (类型: <class 'str'>)
2025-07-10 19:01:18,905 - __main__ - INFO -    [2]: 2018-02-07 00:00:00 (类型: <class 'str'>)
2025-07-10 19:01:18,905 - __main__ - INFO -    [3]: 2018-02-08 00:00:00 (类型: <class 'str'>)
2025-07-10 19:01:18,905 - __main__ - INFO -    [4]: 2018-02-09 00:00:00 (类型: <class 'str'>)
2025-07-10 19:01:18,906 - __main__ - INFO - 🔄 尝试时间转换...
2025-07-10 19:01:18,908 - __main__ - INFO - ✅ 时间转换成功，转换后类型: datetime64[ns]
2025-07-10 19:01:18,908 - __main__ - INFO - 📊 转换后前5个值:
2025-07-10 19:01:18,908 - __main__ - INFO -    [0]: 2018-02-05 00:00:00
2025-07-10 19:01:18,909 - __main__ - INFO -    [1]: 2018-02-06 00:00:00
2025-07-10 19:01:18,909 - __main__ - INFO -    [2]: 2018-02-07 00:00:00
2025-07-10 19:01:18,909 - __main__ - INFO -    [3]: 2018-02-08 00:00:00
2025-07-10 19:01:18,909 - __main__ - INFO -    [4]: 2018-02-09 00:00:00
2025-07-10 19:01:18,910 - __main__ - INFO - 📊 NaT 值数量: 0/1800
2025-07-10 19:01:18,911 - __main__ - INFO - ✅ .dt.normalize() 成功
2025-07-10 19:01:18,911 - __main__ - INFO - 📊 获取分钟数据...
2025-07-10 19:01:18,999 - __main__ - INFO - 📊 分钟数据形状: (241, 9)
2025-07-10 19:01:18,999 - __main__ - INFO - 📊 分钟数据列: ['index_code', 'trade_time', 'trade_date', 'price', 'avg_price', 'volume', 'amount', 'change', 'change_pct']
2025-07-10 19:01:19,000 - __main__ - INFO - 📊 分钟数据类型:
2025-07-10 19:01:19,002 - __main__ - INFO -    index_code: object
2025-07-10 19:01:19,002 - __main__ - INFO -    trade_time: object
2025-07-10 19:01:19,003 - __main__ - INFO -    trade_date: object
2025-07-10 19:01:19,003 - __main__ - INFO -    price: object
2025-07-10 19:01:19,004 - __main__ - INFO -    avg_price: object
2025-07-10 19:01:19,004 - __main__ - INFO -    volume: object
2025-07-10 19:01:19,004 - __main__ - INFO -    amount: object
2025-07-10 19:01:19,004 - __main__ - INFO -    change: float64
2025-07-10 19:01:19,005 - __main__ - INFO -    change_pct: float64
2025-07-10 19:01:19,005 - __main__ - INFO - 📊 volume 前5个值:
2025-07-10 19:01:19,005 - __main__ - INFO -    [0]: 54925448 (类型: <class 'str'>)
2025-07-10 19:01:19,005 - __main__ - INFO -    [1]: 154142470 (类型: <class 'str'>)
2025-07-10 19:01:19,006 - __main__ - INFO -    [2]: 104524050 (类型: <class 'str'>)
2025-07-10 19:01:19,006 - __main__ - INFO -    [3]: 83215560 (类型: <class 'str'>)
2025-07-10 19:01:19,006 - __main__ - INFO -    [4]: 72300400 (类型: <class 'str'>)
2025-07-10 19:01:19,007 - __main__ - INFO - ✅ volume 数值转换成功
2025-07-10 19:01:19,007 - __main__ - INFO - 📊 amount 前5个值:
2025-07-10 19:01:19,007 - __main__ - INFO -    [0]: 601222220 (类型: <class 'str'>)
2025-07-10 19:01:19,007 - __main__ - INFO -    [1]: 1871503800 (类型: <class 'str'>)
2025-07-10 19:01:19,008 - __main__ - INFO -    [2]: 1314663600 (类型: <class 'str'>)
2025-07-10 19:01:19,008 - __main__ - INFO -    [3]: 1012553000 (类型: <class 'str'>)
2025-07-10 19:01:19,008 - __main__ - INFO -    [4]: 867667800 (类型: <class 'str'>)
2025-07-10 19:01:19,008 - __main__ - INFO - ✅ amount 数值转换成功
2025-07-10 19:03:32,372 - __main__ - INFO - 🚀 开始概念数据修复测试...
2025-07-10 19:03:32,373 - __main__ - INFO - 🧪 测试边界情况...
2025-07-10 19:03:32,374 - __main__ - INFO - ✅ 空DataFrame处理测试通过
2025-07-10 19:03:32,377 - __main__ - INFO - 🧪 开始测试概念数据时间处理修复...
2025-07-10 19:03:32,471 - __main__ - INFO - 🎯 测试概念代码: 885311
2025-07-10 19:03:32,472 - __main__ - INFO - 📊 测试概念日线数据处理...
2025-07-10 19:03:32,659 - __main__ - INFO - ✅ 概念 885311 日线数据处理测试通过
2025-07-10 19:03:32,659 - __main__ - INFO - 📊 测试概念分钟数据处理...
2025-07-10 19:03:32,854 - __main__ - ERROR - ❌ 概念 885311 分钟数据处理测试失败: Expected numeric dtype, got object instead.
2025-07-10 19:03:32,857 - __main__ - INFO - 🎉 概念数据时间处理修复测试完成: 1/2 通过
2025-07-10 19:03:32,858 - __main__ - INFO - 🎉 所有测试通过！概念数据时间处理修复成功
2025-07-10 19:04:13,417 - __main__ - INFO - 🔍 开始调试时间转换问题...
2025-07-10 19:04:13,418 - __main__ - INFO - 📊 获取概念 885311 的原始数据...
2025-07-10 19:04:13,611 - __main__ - INFO - 📊 原始数据形状: (1800, 11)
2025-07-10 19:04:13,612 - __main__ - INFO - 📊 trade_time 列类型: object
2025-07-10 19:04:13,612 - __main__ - INFO - 📊 trade_time 前10个值:
2025-07-10 19:04:13,612 - __main__ - INFO -    [0]: '2018-02-02 00:00:00' (类型: <class 'str'>)
2025-07-10 19:04:13,612 - __main__ - INFO -    [1]: '2018-02-05 00:00:00' (类型: <class 'str'>)
2025-07-10 19:04:13,612 - __main__ - INFO -    [2]: '2018-02-06 00:00:00' (类型: <class 'str'>)
2025-07-10 19:04:13,613 - __main__ - INFO -    [3]: '2018-02-07 00:00:00' (类型: <class 'str'>)
2025-07-10 19:04:13,613 - __main__ - INFO -    [4]: '2018-02-08 00:00:00' (类型: <class 'str'>)
2025-07-10 19:04:13,613 - __main__ - INFO -    [5]: '2018-02-09 00:00:00' (类型: <class 'str'>)
2025-07-10 19:04:13,613 - __main__ - INFO -    [6]: '2018-02-12 00:00:00' (类型: <class 'str'>)
2025-07-10 19:04:13,613 - __main__ - INFO -    [7]: '2018-02-13 00:00:00' (类型: <class 'str'>)
2025-07-10 19:04:13,613 - __main__ - INFO -    [8]: '2018-02-14 00:00:00' (类型: <class 'str'>)
2025-07-10 19:04:13,614 - __main__ - INFO -    [9]: '2018-02-22 00:00:00' (类型: <class 'str'>)
2025-07-10 19:04:13,614 - __main__ - INFO - 🔄 模拟转换过程...
2025-07-10 19:04:13,615 - __main__ - INFO - 📊 复制后 trade_time 类型: object
2025-07-10 19:04:13,615 - __main__ - INFO - 🔄 尝试 pd.to_datetime 转换...
2025-07-10 19:04:13,619 - __main__ - INFO - ✅ 转换后类型: object
2025-07-10 19:04:13,619 - __main__ - INFO - 📊 转换后前5个值:
2025-07-10 19:04:13,620 - __main__ - INFO -    [0]: 2018-02-02 00:00:00 (类型: <class 'pandas._libs.tslibs.timestamps.Timestamp'>)
2025-07-10 19:04:13,620 - __main__ - INFO -    [1]: 2018-02-05 00:00:00 (类型: <class 'pandas._libs.tslibs.timestamps.Timestamp'>)
2025-07-10 19:04:13,620 - __main__ - INFO -    [2]: 2018-02-06 00:00:00 (类型: <class 'pandas._libs.tslibs.timestamps.Timestamp'>)
2025-07-10 19:04:13,620 - __main__ - INFO -    [3]: 2018-02-07 00:00:00 (类型: <class 'pandas._libs.tslibs.timestamps.Timestamp'>)
2025-07-10 19:04:13,620 - __main__ - INFO -    [4]: 2018-02-08 00:00:00 (类型: <class 'pandas._libs.tslibs.timestamps.Timestamp'>)
2025-07-10 19:04:13,621 - __main__ - INFO - 📊 NaT 值数量: 0/1800
2025-07-10 19:04:13,621 - __main__ - INFO - 📊 is_datetime64_any_dtype: False
2025-07-10 19:04:13,621 - __main__ - ERROR - ❌ 类型验证失败，实际类型: object
2025-07-10 19:04:13,621 - __main__ - INFO - 🔄 尝试强制转换...
2025-07-10 19:04:13,628 - __main__ - INFO - 📊 强制转换后类型: datetime64[ns]
2025-07-10 19:04:13,630 - __main__ - INFO - 📊 强制转换后 is_datetime64_any_dtype: True
2025-07-10 19:04:13,637 - __main__ - INFO - 📊 测试分钟数据...
2025-07-10 19:04:13,787 - __main__ - INFO - 📊 分钟数据形状: (218, 9)
2025-07-10 19:04:13,788 - __main__ - INFO - 📊 volume 原始类型: object
2025-07-10 19:04:13,788 - __main__ - INFO - 📊 volume 前3个值: ['54925448', '154142470', '104524050']
2025-07-10 19:04:13,788 - __main__ - INFO - ✅ volume 转换成功，类型: int64
2025-07-10 19:04:13,789 - __main__ - INFO - ✅ volume 聚合测试成功: 4226159998
2025-07-10 19:04:13,789 - __main__ - INFO - 📊 amount 原始类型: object
2025-07-10 19:04:13,789 - __main__ - INFO - 📊 amount 前3个值: ['601222220', '1871503800', '1314663600']
2025-07-10 19:04:13,789 - __main__ - INFO - ✅ amount 转换成功，类型: int64
2025-07-10 19:04:13,790 - __main__ - INFO - ✅ amount 聚合测试成功: 52672309020
2025-07-10 19:04:13,790 - __main__ - INFO - 📊 price 原始类型: object
2025-07-10 19:04:13,790 - __main__ - INFO - 📊 price 前3个值: ['3113.798', '3110.888', '3116.398']
2025-07-10 19:04:13,790 - __main__ - INFO - ✅ price 转换成功，类型: float64
2025-07-10 19:04:13,791 - __main__ - INFO - ✅ price 聚合测试成功: 676889.7479999999
2025-07-10 19:05:14,142 - __main__ - INFO - 🚀 开始概念数据修复测试...
2025-07-10 19:05:14,142 - __main__ - INFO - 🧪 测试边界情况...
2025-07-10 19:05:14,143 - __main__ - INFO - ✅ 空DataFrame处理测试通过
2025-07-10 19:05:14,146 - __main__ - INFO - 🧪 开始测试概念数据时间处理修复...
2025-07-10 19:05:14,253 - __main__ - INFO - 🎯 测试概念代码: 885311
2025-07-10 19:05:14,254 - __main__ - INFO - 📊 测试概念日线数据处理...
2025-07-10 19:05:14,440 - __main__ - INFO - ✅ 概念 885311 日线数据处理测试通过
2025-07-10 19:05:14,440 - __main__ - INFO - 📊 测试概念分钟数据处理...
2025-07-10 19:05:14,620 - __main__ - ERROR - ❌ 概念 885311 分钟数据处理测试失败: Expected numeric dtype, got object instead.
2025-07-10 19:05:14,622 - __main__ - INFO - 🎉 概念数据时间处理修复测试完成: 1/2 通过
2025-07-10 19:05:14,623 - __main__ - INFO - 🎉 所有测试通过！概念数据时间处理修复成功
2025-07-10 19:05:53,668 - __main__ - INFO - 🧪 开始简单概念数据测试...
2025-07-10 19:05:53,762 - __main__ - INFO - 🎯 测试概念代码: 885311
2025-07-10 19:05:53,762 - __main__ - INFO - 📅 测试日期: 2025-07-10
2025-07-10 19:05:53,762 - __main__ - INFO - 📊 测试日线数据...
2025-07-10 19:05:53,942 - __main__ - INFO - ✅ 日线数据测试完成，结果: None
2025-07-10 19:05:53,942 - __main__ - INFO - 📊 测试分钟数据...
2025-07-10 19:05:54,012 - __main__ - ERROR - ❌ 分钟数据测试失败: Expected numeric dtype, got object instead.
2025-07-10 19:05:54,015 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\Stock\stock_data\test_simple_concept.py", line 52, in test_single_concept
    result = fetcher.collect_concept_min_data_incremental(test_index_code, today, today)
  File "D:\Stock\stock_data\data\market_fetcher.py", line 2359, in collect_concept_min_data_incremental
    df.loc[:, col] = (df[col] / 1000000).round(2)  # 转换为百万
                     ~~~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pandas\core\series.py", line 2818, in round
    raise TypeError("Expected numeric dtype, got object instead.")
TypeError: Expected numeric dtype, got object instead.

2025-07-10 19:06:55,080 - __main__ - INFO - 🧪 开始简单概念数据测试...
2025-07-10 19:06:55,180 - __main__ - INFO - 🎯 测试概念代码: 885311
2025-07-10 19:06:55,180 - __main__ - INFO - 📅 测试日期: 2025-07-10
2025-07-10 19:06:55,180 - __main__ - INFO - 📊 测试日线数据...
2025-07-10 19:06:55,370 - __main__ - INFO - ✅ 日线数据测试完成，结果: None
2025-07-10 19:06:55,370 - __main__ - INFO - 📊 测试分钟数据...
2025-07-10 19:06:55,686 - __main__ - INFO - ✅ 分钟数据测试完成，结果: None
2025-07-10 19:07:37,867 - __main__ - INFO - 🧪 开始简单概念数据测试...
2025-07-10 19:07:37,961 - __main__ - INFO - 🎯 测试概念代码: 885311
2025-07-10 19:07:37,962 - __main__ - INFO - 📅 测试日期: 2025-07-10
2025-07-10 19:07:37,962 - __main__ - INFO - 📊 测试日线数据...
2025-07-10 19:07:38,170 - __main__ - ERROR - ❌ 日线数据测试失败: 错误:  关系 "concept_daily_data" 的 "trade_date" 字段不存在
LINE 2: ... INTO concept_daily_data (index_code, trade_time, trade_date...
                                                             ^

2025-07-10 19:07:38,172 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\Stock\stock_data\test_simple_concept.py", line 43, in test_single_concept
    result = fetcher.get_concept_ths_daily_data_incremental(test_index_code, today, today)
  File "D:\Stock\stock_data\data\market_fetcher.py", line 2297, in get_concept_ths_daily_data_incremental
    self.db.batch_insert_df(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        'concept_daily_data',
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        enable_dedup=True
        ^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Stock\stock_data\data\timescaledb_manager.py", line 201, in batch_insert_df
    cursor.executemany(sql, data_tuples)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
psycopg2.errors.UndefinedColumn: 错误:  关系 "concept_daily_data" 的 "trade_date" 字段不存在
LINE 2: ... INTO concept_daily_data (index_code, trade_time, trade_date...
                                                             ^


2025-07-10 19:07:38,173 - __main__ - INFO - 📊 测试分钟数据...
2025-07-10 19:07:38,487 - __main__ - INFO - ✅ 分钟数据测试完成，结果: None
2025-07-10 19:08:59,447 - __main__ - INFO - 🧪 开始简单概念数据测试...
2025-07-10 19:08:59,545 - __main__ - INFO - 🎯 测试概念代码: 885311
2025-07-10 19:08:59,546 - __main__ - INFO - 📅 测试日期: 2025-07-10
2025-07-10 19:08:59,546 - __main__ - INFO - 📊 测试日线数据...
2025-07-10 19:08:59,727 - __main__ - INFO - ✅ 日线数据测试完成，结果: None
2025-07-10 19:08:59,727 - __main__ - INFO - 📊 测试分钟数据...
2025-07-10 19:09:00,076 - __main__ - INFO - ✅ 分钟数据测试完成，结果: None
2025-07-10 19:09:14,390 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-10 19:09:14,493 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 19:09:14,499 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-10 19:09:14,656 - __main__ - INFO - 📅 今天 20250710 是交易日，开始每日增量数据更新
2025-07-10 21:35:37,539 - __main__ - INFO - 🚀 开始下载指数数据
2025-07-10 21:35:37,539 - __main__ - INFO - 📅 日期范围: 2025-06-10 到 2025-07-10
2025-07-10 21:35:37,540 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 21:35:37,544 - __main__ - INFO - ✅ 市场数据获取器初始化成功
2025-07-10 21:35:37,544 - __main__ - INFO - 📊 开始下载指数数据...
2025-07-10 21:35:39,854 - __main__ - INFO - ✅ 指数数据下载完成
2025-07-10 21:35:39,854 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 21:35:39,854 - __main__ - INFO - 🎉 下载成功
2025-07-10 21:36:54,244 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-10 21:36:54,324 - __main__ - INFO - ✅ 数据库连接成功
2025-07-10 21:36:54,330 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-10 21:36:54,490 - __main__ - INFO - 📅 今天 20250710 是交易日，开始每日增量数据更新
2025-07-10 22:14:44,968 - __main__ - INFO - ✅ 每日增量数据更新完成
2025-07-10 22:14:44,969 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-10 22:14:44,969 - __main__ - INFO - ✅ 每日15:30增量更新任务完成
2025-07-10 22:24:36,225 - __main__ - INFO - 🚀 概念日线数据补充工具启动
2025-07-10 22:24:36,225 - __main__ - INFO - 🔍 检查最近缺失的概念日线数据...
2025-07-10 22:24:36,475 - __main__ - INFO - 📊 2025-07-10: 319/371 (86.0%)
2025-07-10 22:24:36,488 - __main__ - INFO - 📊 2025-07-09: 0/371 (0.0%)
2025-07-10 22:24:36,488 - __main__ - WARNING - ⚠️ 2025-07-09 数据不完整，建议补充
2025-07-10 22:24:36,501 - __main__ - INFO - 📊 2025-07-08: 370/371 (99.7%)
2025-07-10 22:24:36,515 - __main__ - INFO - 📊 2025-07-07: 370/371 (99.7%)
2025-07-10 22:24:36,530 - __main__ - INFO - 📊 2025-07-04: 370/371 (99.7%)
2025-07-10 22:24:36,530 - __main__ - INFO - 🎯 开始补充 2025-07-09 的概念日线数据...
2025-07-10 22:24:36,530 - __main__ - INFO - 🚀 开始补充概念日线数据: 2025-07-09
2025-07-10 22:24:36,582 - __main__ - INFO - 📊 获取概念代码列表...
2025-07-10 22:24:36,592 - __main__ - INFO - 📋 找到 371 个概念代码
2025-07-10 22:24:36,593 - __main__ - INFO - 📈 [1/371] 处理概念 885311...
2025-07-10 22:24:36,852 - __main__ - INFO - 📈 [2/371] 处理概念 885312...
2025-07-10 22:24:37,048 - __main__ - INFO - 📈 [3/371] 处理概念 885333...
2025-07-10 22:24:37,243 - __main__ - INFO - 📈 [4/371] 处理概念 885338...
2025-07-10 22:24:37,420 - __main__ - INFO - 📈 [5/371] 处理概念 885343...
2025-07-10 22:24:37,612 - __main__ - INFO - 📈 [6/371] 处理概念 885345...
2025-07-10 22:24:37,807 - __main__ - INFO - 📈 [7/371] 处理概念 885355...
2025-07-10 22:24:38,015 - __main__ - INFO - 📈 [8/371] 处理概念 885362...
2025-07-10 22:24:38,199 - __main__ - INFO - 📈 [9/371] 处理概念 885372...
2025-07-10 22:24:38,379 - __main__ - INFO - 📈 [10/371] 处理概念 885376...
2025-07-10 22:24:38,564 - __main__ - INFO - 📈 [11/371] 处理概念 885378...
2025-07-10 22:24:38,746 - __main__ - INFO - 📈 [12/371] 处理概念 885386...
2025-07-10 22:24:38,872 - __main__ - INFO - 📈 [13/371] 处理概念 885398...
2025-07-10 22:24:39,049 - __main__ - INFO - 📈 [14/371] 处理概念 885402...
2025-07-10 22:24:39,231 - __main__ - INFO - 📈 [15/371] 处理概念 885406...
2025-07-10 22:24:39,427 - __main__ - INFO - 📈 [16/371] 处理概念 885410...
2025-07-10 22:24:39,603 - __main__ - INFO - 📈 [17/371] 处理概念 885412...
2025-07-10 22:24:39,852 - __main__ - INFO - 📈 [18/371] 处理概念 885413...
2025-07-10 22:24:40,037 - __main__ - INFO - 📈 [19/371] 处理概念 885418...
2025-07-10 22:24:40,219 - __main__ - INFO - 📈 [20/371] 处理概念 885420...
2025-07-10 22:24:40,396 - __main__ - INFO - 📈 [21/371] 处理概念 885423...
2025-07-10 22:24:40,606 - __main__ - INFO - 📈 [22/371] 处理概念 885425...
2025-07-10 22:24:40,800 - __main__ - INFO - 📈 [23/371] 处理概念 885426...
2025-07-10 22:24:40,981 - __main__ - INFO - 📈 [24/371] 处理概念 885427...
2025-07-10 22:24:41,160 - __main__ - INFO - 📈 [25/371] 处理概念 885428...
2025-07-10 22:24:41,372 - __main__ - INFO - 📈 [26/371] 处理概念 885430...
2025-07-10 22:24:41,552 - __main__ - INFO - 📈 [27/371] 处理概念 885431...
2025-07-10 22:24:41,731 - __main__ - INFO - 📈 [28/371] 处理概念 885439...
2025-07-10 22:24:41,935 - __main__ - INFO - 📈 [29/371] 处理概念 885454...
2025-07-10 22:24:42,115 - __main__ - INFO - 📈 [30/371] 处理概念 885456...
2025-07-10 22:24:42,319 - __main__ - INFO - 📈 [31/371] 处理概念 885457...
2025-07-10 22:24:42,499 - __main__ - INFO - 📈 [32/371] 处理概念 885459...
2025-07-10 22:24:42,669 - __main__ - INFO - 📈 [33/371] 处理概念 885461...
2025-07-10 22:24:46,139 - __main__ - INFO - 📈 [34/371] 处理概念 885462...
2025-07-10 22:24:46,476 - __main__ - INFO - 📈 [35/371] 处理概念 885467...
2025-07-10 22:24:46,639 - __main__ - INFO - 📈 [36/371] 处理概念 885472...
2025-07-10 22:24:46,797 - __main__ - INFO - 📈 [37/371] 处理概念 885478...
2025-07-10 22:24:46,959 - __main__ - INFO - 📈 [38/371] 处理概念 885480...
2025-07-10 22:25:00,854 - __main__ - WARNING - ❌ 概念 885480 数据获取失败: substring not found
2025-07-10 22:25:00,854 - __main__ - INFO - 📈 [39/371] 处理概念 885487...
2025-07-10 22:25:01,055 - __main__ - INFO - 📈 [40/371] 处理概念 885490...
2025-07-10 22:25:01,249 - __main__ - INFO - 📈 [41/371] 处理概念 885493...
2025-07-10 22:25:03,073 - __main__ - INFO - 📈 [42/371] 处理概念 885494...
2025-07-10 22:25:03,229 - __main__ - INFO - 📈 [43/371] 处理概念 885497...
2025-07-10 22:25:03,390 - __main__ - INFO - 📈 [44/371] 处理概念 885502...
2025-07-10 22:25:03,546 - __main__ - INFO - 📈 [45/371] 处理概念 885505...
2025-07-10 22:25:03,863 - __main__ - INFO - 📈 [46/371] 处理概念 885508...
2025-07-10 22:25:04,051 - __main__ - INFO - 📈 [47/371] 处理概念 885514...
2025-07-10 22:25:07,518 - __main__ - INFO - 📈 [48/371] 处理概念 885517...
2025-07-10 22:25:07,681 - __main__ - INFO - 📈 [49/371] 处理概念 885520...
2025-07-10 22:25:07,860 - __main__ - INFO - 📈 [50/371] 处理概念 885521...
2025-07-10 22:25:08,186 - __main__ - INFO - 📊 进度: 50/371 (13.5%) - 成功:49, 无数据:0, 错误:1
2025-07-10 22:25:08,187 - __main__ - INFO - 📈 [51/371] 处理概念 885522...
2025-07-10 22:25:08,353 - __main__ - INFO - 📈 [52/371] 处理概念 885525...
2025-07-10 22:25:08,529 - __main__ - INFO - 📈 [53/371] 处理概念 885530...
2025-07-10 22:25:08,700 - __main__ - INFO - 📈 [54/371] 处理概念 885531...
2025-07-10 22:25:08,881 - __main__ - INFO - 📈 [55/371] 处理概念 885537...
2025-07-10 22:25:09,039 - __main__ - INFO - 📈 [56/371] 处理概念 885539...
2025-07-10 22:25:09,211 - __main__ - INFO - 📈 [57/371] 处理概念 885540...
2025-07-10 22:25:16,312 - __main__ - INFO - 📈 [58/371] 处理概念 885543...
2025-07-10 22:25:20,212 - __main__ - INFO - 📈 [59/371] 处理概念 885545...
2025-07-10 22:25:20,406 - __main__ - INFO - 📈 [60/371] 处理概念 885551...
2025-07-10 22:25:20,579 - __main__ - INFO - 📈 [61/371] 处理概念 885552...
2025-07-10 22:25:20,743 - __main__ - INFO - 📈 [62/371] 处理概念 885555...
2025-07-10 22:25:20,916 - __main__ - INFO - 📈 [63/371] 处理概念 885556...
2025-07-10 22:25:21,099 - __main__ - INFO - 📈 [64/371] 处理概念 885562...
2025-07-10 22:25:21,453 - __main__ - INFO - 📈 [65/371] 处理概念 885563...
2025-07-10 22:25:21,657 - __main__ - INFO - 📈 [66/371] 处理概念 885564...
2025-07-10 22:25:21,842 - __main__ - INFO - 📈 [67/371] 处理概念 885566...
2025-07-10 22:25:22,049 - __main__ - INFO - 📈 [68/371] 处理概念 885570...
2025-07-10 22:25:22,230 - __main__ - INFO - 📈 [69/371] 处理概念 885571...
2025-07-10 22:25:22,409 - __main__ - INFO - 📈 [70/371] 处理概念 885572...
2025-07-10 22:25:22,719 - __main__ - INFO - 📈 [71/371] 处理概念 885573...
2025-07-10 22:25:22,898 - __main__ - INFO - 📈 [72/371] 处理概念 885574...
2025-07-10 22:25:23,086 - __main__ - INFO - 📈 [73/371] 处理概念 885578...
2025-07-10 22:25:23,267 - __main__ - INFO - 📈 [74/371] 处理概念 885580...
2025-07-10 22:25:23,451 - __main__ - INFO - 📈 [75/371] 处理概念 885586...
2025-07-10 22:25:23,636 - __main__ - INFO - 📈 [76/371] 处理概念 885587...
2025-07-10 22:25:23,850 - __main__ - INFO - 📈 [77/371] 处理概念 885591...
2025-07-10 22:25:24,064 - __main__ - INFO - 📈 [78/371] 处理概念 885595...
2025-07-10 22:25:24,262 - __main__ - INFO - 📈 [79/371] 处理概念 885598...
2025-07-10 22:25:24,467 - __main__ - INFO - 📈 [80/371] 处理概念 885603...
2025-07-10 22:25:24,650 - __main__ - INFO - 📈 [81/371] 处理概念 885611...
2025-07-10 22:25:24,862 - __main__ - INFO - 📈 [82/371] 处理概念 885615...
2025-07-10 22:25:31,987 - __main__ - INFO - 📈 [83/371] 处理概念 885617...
2025-07-10 22:25:32,334 - __main__ - INFO - 📈 [84/371] 处理概念 885620...
2025-07-10 22:25:32,519 - __main__ - INFO - 📈 [85/371] 处理概念 885623...
2025-07-10 22:25:32,717 - __main__ - INFO - 📈 [86/371] 处理概念 885629...
2025-07-10 22:25:32,922 - __main__ - INFO - 📈 [87/371] 处理概念 885633...
2025-07-10 22:25:33,105 - __main__ - INFO - 📈 [88/371] 处理概念 885640...
2025-07-10 22:25:33,315 - __main__ - INFO - 📈 [89/371] 处理概念 885641...
2025-07-10 22:25:33,509 - __main__ - INFO - 📈 [90/371] 处理概念 885642...
2025-07-10 22:25:37,100 - __main__ - INFO - 📈 [91/371] 处理概念 885650...
2025-07-10 22:25:37,289 - __main__ - INFO - 📈 [92/371] 处理概念 885652...
2025-07-10 22:25:37,475 - __main__ - INFO - 📈 [93/371] 处理概念 885661...
2025-07-10 22:25:37,835 - __main__ - INFO - 📈 [94/371] 处理概念 885662...
2025-07-10 22:25:38,034 - __main__ - INFO - 📈 [95/371] 处理概念 885663...
2025-07-10 22:25:38,219 - __main__ - INFO - 📈 [96/371] 处理概念 885690...
2025-07-10 22:25:38,391 - __main__ - INFO - 📈 [97/371] 处理概念 885692...
2025-07-10 22:25:40,219 - __main__ - INFO - 📈 [98/371] 处理概念 885694...
2025-07-10 22:25:40,403 - __main__ - INFO - 📈 [99/371] 处理概念 885697...
2025-07-10 22:25:40,581 - __main__ - INFO - 📈 [100/371] 处理概念 885699...
2025-07-10 22:25:40,762 - __main__ - INFO - 📊 进度: 100/371 (27.0%) - 成功:99, 无数据:0, 错误:1
2025-07-10 22:25:40,763 - __main__ - INFO - 📈 [101/371] 处理概念 885700...
2025-07-10 22:25:40,945 - __main__ - INFO - 📈 [102/371] 处理概念 885705...
2025-07-10 22:25:41,123 - __main__ - INFO - 📈 [103/371] 处理概念 885706...
2025-07-10 22:25:41,415 - __main__ - INFO - 📈 [104/371] 处理概念 885709...
2025-07-10 22:25:41,600 - __main__ - INFO - 📈 [105/371] 处理概念 885710...
2025-07-10 22:25:41,788 - __main__ - INFO - 📈 [106/371] 处理概念 885728...
2025-07-10 22:25:41,989 - __main__ - INFO - 📈 [107/371] 处理概念 885730...
2025-07-10 22:25:42,187 - __main__ - INFO - 📈 [108/371] 处理概念 885733...
2025-07-10 22:25:42,362 - __main__ - INFO - 📈 [109/371] 处理概念 885734...
2025-07-10 22:25:42,537 - __main__ - INFO - 📈 [110/371] 处理概念 885736...
2025-07-10 22:25:42,736 - __main__ - INFO - 📈 [111/371] 处理概念 885737...
2025-07-10 22:25:42,966 - __main__ - INFO - 📈 [112/371] 处理概念 885738...
2025-07-10 22:25:43,197 - __main__ - INFO - 📈 [113/371] 处理概念 885739...
2025-07-10 22:25:43,382 - __main__ - INFO - 📈 [114/371] 处理概念 885740...
2025-07-10 22:25:43,575 - __main__ - INFO - 📈 [115/371] 处理概念 885741...
2025-07-10 22:25:43,767 - __main__ - INFO - 📈 [116/371] 处理概念 885742...
2025-07-10 22:25:43,964 - __main__ - INFO - 📈 [117/371] 处理概念 885743...
2025-07-10 22:25:44,137 - __main__ - INFO - 📈 [118/371] 处理概念 885744...
2025-07-10 22:25:44,315 - __main__ - INFO - 📈 [119/371] 处理概念 885747...
2025-07-10 22:25:44,510 - __main__ - INFO - 📈 [120/371] 处理概念 885748...
2025-07-10 22:25:44,718 - __main__ - INFO - 📈 [121/371] 处理概念 885749...
2025-07-10 22:25:44,908 - __main__ - INFO - 📈 [122/371] 处理概念 885750...
2025-07-10 22:25:45,102 - __main__ - INFO - 📈 [123/371] 处理概念 885753...
2025-07-10 22:25:45,270 - __main__ - INFO - 📈 [124/371] 处理概念 885756...
2025-07-10 22:25:45,449 - __main__ - INFO - 📈 [125/371] 处理概念 885757...
2025-07-10 22:25:45,632 - __main__ - INFO - 📈 [126/371] 处理概念 885758...
2025-07-10 22:25:45,806 - __main__ - INFO - 📈 [127/371] 处理概念 885759...
2025-07-10 22:25:45,986 - __main__ - INFO - 📈 [128/371] 处理概念 885760...
2025-07-10 22:25:46,169 - __main__ - INFO - 📈 [129/371] 处理概念 885761...
2025-07-10 22:25:46,406 - __main__ - INFO - 📈 [130/371] 处理概念 885764...
2025-07-10 22:25:46,601 - __main__ - INFO - 📈 [131/371] 处理概念 885765...
2025-07-10 22:25:46,781 - __main__ - INFO - 📈 [132/371] 处理概念 885766...
2025-07-10 22:25:46,986 - __main__ - INFO - 📈 [133/371] 处理概念 885767...
2025-07-10 22:25:47,196 - __main__ - INFO - 📈 [134/371] 处理概念 885768...
2025-07-10 22:25:47,398 - __main__ - INFO - 📈 [135/371] 处理概念 885769...
2025-07-10 22:25:47,589 - __main__ - INFO - 📈 [136/371] 处理概念 885770...
2025-07-10 22:25:47,764 - __main__ - INFO - 📈 [137/371] 处理概念 885771...
2025-07-10 22:25:47,944 - __main__ - INFO - 📈 [138/371] 处理概念 885772...
2025-07-10 22:25:48,128 - __main__ - INFO - 📈 [139/371] 处理概念 885774...
2025-07-10 22:25:48,339 - __main__ - INFO - 📈 [140/371] 处理概念 885775...
2025-07-10 22:25:48,514 - __main__ - INFO - 📈 [141/371] 处理概念 885779...
2025-07-10 22:25:48,703 - __main__ - INFO - 📈 [142/371] 处理概念 885780...
2025-07-10 22:25:48,881 - __main__ - INFO - 📈 [143/371] 处理概念 885781...
2025-07-10 22:25:49,070 - __main__ - INFO - 📈 [144/371] 处理概念 885782...
2025-07-10 22:25:49,249 - __main__ - INFO - 📈 [145/371] 处理概念 885783...
2025-07-10 22:25:49,426 - __main__ - INFO - 📈 [146/371] 处理概念 885785...
2025-07-10 22:25:49,599 - __main__ - INFO - 📈 [147/371] 处理概念 885786...
2025-07-10 22:25:49,796 - __main__ - INFO - 📈 [148/371] 处理概念 885787...
2025-07-10 22:25:49,996 - __main__ - INFO - 📈 [149/371] 处理概念 885788...
2025-07-10 22:25:50,171 - __main__ - INFO - 📈 [150/371] 处理概念 885789...
2025-07-10 22:25:50,345 - __main__ - INFO - 📊 进度: 150/371 (40.4%) - 成功:149, 无数据:0, 错误:1
2025-07-10 22:25:50,346 - __main__ - INFO - 📈 [151/371] 处理概念 885791...
2025-07-10 22:25:50,536 - __main__ - INFO - 📈 [152/371] 处理概念 885792...
2025-07-10 22:25:50,716 - __main__ - INFO - 📈 [153/371] 处理概念 885795...
2025-07-10 22:25:50,894 - __main__ - INFO - 📈 [154/371] 处理概念 885797...
2025-07-10 22:25:51,064 - __main__ - INFO - 📈 [155/371] 处理概念 885800...
2025-07-10 22:25:51,225 - __main__ - INFO - 📈 [156/371] 处理概念 885805...
2025-07-10 22:25:51,407 - __main__ - INFO - 📈 [157/371] 处理概念 885806...
2025-07-10 22:25:51,601 - __main__ - INFO - 📈 [158/371] 处理概念 885808...
2025-07-10 22:25:51,797 - __main__ - INFO - 📈 [159/371] 处理概念 885809...
2025-07-10 22:25:51,979 - __main__ - INFO - 📈 [160/371] 处理概念 885810...
2025-07-10 22:25:52,173 - __main__ - INFO - 📈 [161/371] 处理概念 885811...
2025-07-10 22:25:52,350 - __main__ - INFO - 📈 [162/371] 处理概念 885812...
2025-07-10 22:25:52,539 - __main__ - INFO - 📈 [163/371] 处理概念 885814...
2025-07-10 22:25:52,714 - __main__ - INFO - 📈 [164/371] 处理概念 885818...
2025-07-10 22:25:52,894 - __main__ - INFO - 📈 [165/371] 处理概念 885819...
2025-07-10 22:25:53,089 - __main__ - INFO - 📈 [166/371] 处理概念 885820...
2025-07-10 22:25:53,306 - __main__ - INFO - 📈 [167/371] 处理概念 885823...
2025-07-10 22:25:53,482 - __main__ - INFO - 📈 [168/371] 处理概念 885825...
2025-07-10 22:25:53,672 - __main__ - INFO - 📈 [169/371] 处理概念 885827...
2025-07-10 22:25:53,851 - __main__ - INFO - 📈 [170/371] 处理概念 885829...
2025-07-10 22:25:54,047 - __main__ - INFO - 📈 [171/371] 处理概念 885832...
2025-07-10 22:25:54,247 - __main__ - INFO - 📈 [172/371] 处理概念 885834...
2025-07-10 22:25:54,399 - __main__ - INFO - 📈 [173/371] 处理概念 885835...
2025-07-10 22:25:54,569 - __main__ - INFO - 📈 [174/371] 处理概念 885838...
2025-07-10 22:25:54,757 - __main__ - INFO - 📈 [175/371] 处理概念 885839...
2025-07-10 22:25:54,927 - __main__ - INFO - 📈 [176/371] 处理概念 885842...
2025-07-10 22:25:55,109 - __main__ - INFO - 📈 [177/371] 处理概念 885843...
2025-07-10 22:25:55,316 - __main__ - INFO - 📈 [178/371] 处理概念 885844...
2025-07-10 22:25:55,511 - __main__ - INFO - 📈 [179/371] 处理概念 885845...
2025-07-10 22:25:55,703 - __main__ - INFO - 📈 [180/371] 处理概念 885846...
2025-07-10 22:25:55,894 - __main__ - INFO - 📈 [181/371] 处理概念 885849...
2025-07-10 22:25:56,051 - __main__ - INFO - 📈 [182/371] 处理概念 885851...
2025-07-10 22:25:56,248 - __main__ - INFO - 📈 [183/371] 处理概念 885852...
2025-07-10 22:25:56,438 - __main__ - INFO - 📈 [184/371] 处理概念 885854...
2025-07-10 22:25:56,606 - __main__ - INFO - 📈 [185/371] 处理概念 885856...
2025-07-10 22:25:56,762 - __main__ - INFO - 📈 [186/371] 处理概念 885860...
2025-07-10 22:25:56,916 - __main__ - INFO - 📈 [187/371] 处理概念 885861...
2025-07-10 22:25:57,072 - __main__ - INFO - 📈 [188/371] 处理概念 885863...
2025-07-10 22:25:57,256 - __main__ - INFO - 📈 [189/371] 处理概念 885864...
2025-07-10 22:25:57,449 - __main__ - INFO - 📈 [190/371] 处理概念 885865...
2025-07-10 22:25:57,618 - __main__ - INFO - 📈 [191/371] 处理概念 885866...
2025-07-10 22:25:57,785 - __main__ - INFO - 📈 [192/371] 处理概念 885868...
2025-07-10 22:25:57,962 - __main__ - INFO - 📈 [193/371] 处理概念 885873...
2025-07-10 22:25:58,122 - __main__ - INFO - 📈 [194/371] 处理概念 885874...
2025-07-10 22:25:58,290 - __main__ - INFO - 📈 [195/371] 处理概念 885875...
2025-07-10 22:25:58,450 - __main__ - INFO - 📈 [196/371] 处理概念 885876...
2025-07-10 22:25:59,272 - __main__ - INFO - 📈 [197/371] 处理概念 885877...
2025-07-10 22:25:59,435 - __main__ - INFO - 📈 [198/371] 处理概念 885879...
2025-07-10 22:25:59,615 - __main__ - INFO - 📈 [199/371] 处理概念 885881...
2025-07-10 22:25:59,795 - __main__ - INFO - 📈 [200/371] 处理概念 885882...
2025-07-10 22:25:59,951 - __main__ - INFO - 📊 进度: 200/371 (53.9%) - 成功:199, 无数据:0, 错误:1
2025-07-10 22:25:59,951 - __main__ - INFO - 📈 [201/371] 处理概念 885883...
2025-07-10 22:26:00,098 - __main__ - INFO - 📈 [202/371] 处理概念 885884...
2025-07-10 22:26:00,287 - __main__ - INFO - 📈 [203/371] 处理概念 885886...
2025-07-10 22:26:00,452 - __main__ - INFO - 📈 [204/371] 处理概念 885887...
2025-07-10 22:26:00,604 - __main__ - INFO - 📈 [205/371] 处理概念 885890...
2025-07-10 22:26:00,760 - __main__ - INFO - 📈 [206/371] 处理概念 885893...
2025-07-10 22:26:00,930 - __main__ - INFO - 📈 [207/371] 处理概念 885894...
2025-07-10 22:26:01,133 - __main__ - INFO - 📈 [208/371] 处理概念 885897...
2025-07-10 22:26:01,314 - __main__ - INFO - 📈 [209/371] 处理概念 885898...
2025-07-10 22:26:01,498 - __main__ - INFO - 📈 [210/371] 处理概念 885899...
2025-07-10 22:26:01,691 - __main__ - INFO - 📈 [211/371] 处理概念 885901...
2025-07-10 22:26:01,873 - __main__ - INFO - 📈 [212/371] 处理概念 885902...
2025-07-10 22:26:02,071 - __main__ - INFO - 📈 [213/371] 处理概念 885903...
2025-07-10 22:26:02,235 - __main__ - INFO - 📈 [214/371] 处理概念 885904...
2025-07-10 22:26:02,395 - __main__ - INFO - 📈 [215/371] 处理概念 885905...
2025-07-10 22:26:02,549 - __main__ - INFO - 📈 [216/371] 处理概念 885907...
2025-07-10 22:26:02,699 - __main__ - INFO - 📈 [217/371] 处理概念 885908...
2025-07-10 22:26:02,843 - __main__ - INFO - 📈 [218/371] 处理概念 885909...
2025-07-10 22:26:02,999 - __main__ - INFO - 📈 [219/371] 处理概念 885910...
2025-07-10 22:26:03,152 - __main__ - INFO - 📈 [220/371] 处理概念 885911...
2025-07-10 22:26:03,353 - __main__ - INFO - 📈 [221/371] 处理概念 885912...
2025-07-10 22:26:03,507 - __main__ - INFO - 📈 [222/371] 处理概念 885913...
2025-07-10 22:26:03,684 - __main__ - INFO - 📈 [223/371] 处理概念 885914...
2025-07-10 22:26:03,850 - __main__ - INFO - 📈 [224/371] 处理概念 885915...
2025-07-10 22:26:04,029 - __main__ - INFO - 📈 [225/371] 处理概念 885916...
2025-07-10 22:26:04,185 - __main__ - INFO - 📈 [226/371] 处理概念 885918...
2025-07-10 22:26:04,355 - __main__ - INFO - 📈 [227/371] 处理概念 885919...
2025-07-10 22:26:04,516 - __main__ - INFO - 📈 [228/371] 处理概念 885921...
2025-07-10 22:26:04,670 - __main__ - INFO - 📈 [229/371] 处理概念 885922...
2025-07-10 22:26:04,818 - __main__ - INFO - 📈 [230/371] 处理概念 885923...
2025-07-10 22:26:04,965 - __main__ - INFO - 📈 [231/371] 处理概念 885924...
2025-07-10 22:26:05,127 - __main__ - INFO - 📈 [232/371] 处理概念 885925...
2025-07-10 22:26:05,281 - __main__ - INFO - 📈 [233/371] 处理概念 885926...
2025-07-10 22:26:05,432 - __main__ - INFO - 📈 [234/371] 处理概念 885928...
2025-07-10 22:26:05,585 - __main__ - INFO - 📈 [235/371] 处理概念 885929...
2025-07-10 22:26:05,734 - __main__ - INFO - 📈 [236/371] 处理概念 885930...
2025-07-10 22:26:05,860 - __main__ - INFO - 📈 [237/371] 处理概念 885933...
2025-07-10 22:26:06,011 - __main__ - INFO - 📈 [238/371] 处理概念 885934...
2025-07-10 22:26:06,145 - __main__ - INFO - 📈 [239/371] 处理概念 885935...
2025-07-10 22:26:06,281 - __main__ - INFO - 📈 [240/371] 处理概念 885936...
2025-07-10 22:26:06,432 - __main__ - INFO - 📈 [241/371] 处理概念 885937...
2025-07-10 22:26:06,588 - __main__ - INFO - 📈 [242/371] 处理概念 885938...
2025-07-10 22:26:06,727 - __main__ - INFO - 📈 [243/371] 处理概念 885939...
2025-07-10 22:26:06,916 - __main__ - INFO - 📈 [244/371] 处理概念 885942...
2025-07-10 22:26:07,070 - __main__ - INFO - 📈 [245/371] 处理概念 885943...
2025-07-10 22:26:07,220 - __main__ - INFO - 📈 [246/371] 处理概念 885944...
2025-07-10 22:26:07,348 - __main__ - INFO - 📈 [247/371] 处理概念 885945...
2025-07-10 22:26:07,516 - __main__ - INFO - 📈 [248/371] 处理概念 885946...
2025-07-10 22:26:07,671 - __main__ - INFO - 📈 [249/371] 处理概念 885948...
2025-07-10 22:26:07,810 - __main__ - INFO - 📈 [250/371] 处理概念 885950...
2025-07-10 22:26:07,934 - __main__ - INFO - 📊 进度: 250/371 (67.4%) - 成功:249, 无数据:0, 错误:1
2025-07-10 22:26:07,934 - __main__ - INFO - 📈 [251/371] 处理概念 885951...
2025-07-10 22:26:08,068 - __main__ - INFO - 📈 [252/371] 处理概念 885952...
2025-07-10 22:26:08,217 - __main__ - INFO - 📈 [253/371] 处理概念 885953...
2025-07-10 22:26:08,353 - __main__ - INFO - 📈 [254/371] 处理概念 885955...
2025-07-10 22:26:08,477 - __main__ - INFO - 📈 [255/371] 处理概念 885956...
2025-07-10 22:26:08,603 - __main__ - INFO - 📈 [256/371] 处理概念 885957...
2025-07-10 22:26:08,744 - __main__ - INFO - 📈 [257/371] 处理概念 885958...
2025-07-10 22:26:08,872 - __main__ - INFO - 📈 [258/371] 处理概念 885959...
2025-07-10 22:26:08,997 - __main__ - INFO - 📈 [259/371] 处理概念 885960...
2025-07-10 22:26:09,132 - __main__ - INFO - 📈 [260/371] 处理概念 885961...
2025-07-10 22:26:09,259 - __main__ - INFO - 📈 [261/371] 处理概念 885962...
2025-07-10 22:26:09,382 - __main__ - INFO - 📈 [262/371] 处理概念 885963...
2025-07-10 22:26:09,513 - __main__ - INFO - 📈 [263/371] 处理概念 885964...
2025-07-10 22:26:09,635 - __main__ - INFO - 📈 [264/371] 处理概念 885965...
2025-07-10 22:26:09,761 - __main__ - INFO - 📈 [265/371] 处理概念 885967...
2025-07-10 22:26:09,892 - __main__ - INFO - 📈 [266/371] 处理概念 885968...
2025-07-10 22:26:10,033 - __main__ - INFO - 📈 [267/371] 处理概念 885969...
2025-07-10 22:26:10,153 - __main__ - INFO - 📈 [268/371] 处理概念 885970...
2025-07-10 22:26:10,273 - __main__ - INFO - 📈 [269/371] 处理概念 885971...
2025-07-10 22:26:10,407 - __main__ - INFO - 📈 [270/371] 处理概念 885972...
2025-07-10 22:26:10,534 - __main__ - INFO - 📈 [271/371] 处理概念 885973...
2025-07-10 22:26:10,651 - __main__ - INFO - 📈 [272/371] 处理概念 885974...
2025-07-10 22:26:10,774 - __main__ - INFO - 📈 [273/371] 处理概念 885975...
2025-07-10 22:26:10,899 - __main__ - INFO - 📈 [274/371] 处理概念 885976...
2025-07-10 22:26:11,059 - __main__ - INFO - 📈 [275/371] 处理概念 885977...
2025-07-10 22:26:11,182 - __main__ - INFO - 📈 [276/371] 处理概念 885978...
2025-07-10 22:26:11,326 - __main__ - INFO - 📈 [277/371] 处理概念 885980...
2025-07-10 22:26:11,453 - __main__ - INFO - 📈 [278/371] 处理概念 885981...
2025-07-10 22:26:11,578 - __main__ - INFO - 📈 [279/371] 处理概念 885982...
2025-07-10 22:26:11,695 - __main__ - INFO - 📈 [280/371] 处理概念 885985...
2025-07-10 22:26:11,814 - __main__ - INFO - 📈 [281/371] 处理概念 885986...
2025-07-10 22:26:11,931 - __main__ - INFO - 📈 [282/371] 处理概念 885988...
2025-07-10 22:26:12,061 - __main__ - INFO - 📈 [283/371] 处理概念 885989...
2025-07-10 22:26:12,188 - __main__ - INFO - 📈 [284/371] 处理概念 885990...
2025-07-10 22:26:12,335 - __main__ - INFO - 📈 [285/371] 处理概念 885991...
2025-07-10 22:26:12,481 - __main__ - INFO - 📈 [286/371] 处理概念 885992...
2025-07-10 22:26:12,616 - __main__ - INFO - 📈 [287/371] 处理概念 885994...
2025-07-10 22:26:12,794 - __main__ - INFO - 📈 [288/371] 处理概念 885995...
2025-07-10 22:26:12,921 - __main__ - INFO - 📈 [289/371] 处理概念 885996...
2025-07-10 22:26:13,048 - __main__ - INFO - 📈 [290/371] 处理概念 885997...
2025-07-10 22:26:13,165 - __main__ - INFO - 📈 [291/371] 处理概念 885998...
2025-07-10 22:26:13,286 - __main__ - INFO - 📈 [292/371] 处理概念 885999...
2025-07-10 22:26:13,420 - __main__ - INFO - 📈 [293/371] 处理概念 886000...
2025-07-10 22:26:13,533 - __main__ - INFO - 📈 [294/371] 处理概念 886001...
2025-07-10 22:26:13,653 - __main__ - INFO - 📈 [295/371] 处理概念 886002...
2025-07-10 22:26:13,777 - __main__ - INFO - 📈 [296/371] 处理概念 886003...
2025-07-10 22:26:13,898 - __main__ - INFO - 📈 [297/371] 处理概念 886004...
2025-07-10 22:26:14,017 - __main__ - INFO - 📈 [298/371] 处理概念 886005...
2025-07-10 22:26:14,133 - __main__ - INFO - 📈 [299/371] 处理概念 886006...
2025-07-10 22:26:14,279 - __main__ - INFO - 📈 [300/371] 处理概念 886007...
2025-07-10 22:26:14,400 - __main__ - INFO - 📊 进度: 300/371 (80.9%) - 成功:299, 无数据:0, 错误:1
2025-07-10 22:26:14,400 - __main__ - INFO - 📈 [301/371] 处理概念 886008...
2025-07-10 22:26:14,528 - __main__ - INFO - 📈 [302/371] 处理概念 886009...
2025-07-10 22:26:14,658 - __main__ - INFO - 📈 [303/371] 处理概念 886010...
2025-07-10 22:26:14,789 - __main__ - INFO - 📈 [304/371] 处理概念 886011...
2025-07-10 22:26:14,917 - __main__ - INFO - 📈 [305/371] 处理概念 886012...
2025-07-10 22:26:15,036 - __main__ - INFO - 📈 [306/371] 处理概念 886013...
2025-07-10 22:26:15,153 - __main__ - INFO - 📈 [307/371] 处理概念 886015...
2025-07-10 22:26:15,272 - __main__ - INFO - 📈 [308/371] 处理概念 886016...
2025-07-10 22:26:15,391 - __main__ - INFO - 📈 [309/371] 处理概念 886018...
2025-07-10 22:26:15,506 - __main__ - INFO - 📈 [310/371] 处理概念 886020...
2025-07-10 22:26:15,697 - __main__ - INFO - 📈 [311/371] 处理概念 886021...
2025-07-10 22:26:15,820 - __main__ - INFO - 📈 [312/371] 处理概念 886023...
2025-07-10 22:26:15,946 - __main__ - INFO - 📈 [313/371] 处理概念 886026...
2025-07-10 22:26:16,067 - __main__ - INFO - 📈 [314/371] 处理概念 886028...
2025-07-10 22:26:16,182 - __main__ - INFO - 📈 [315/371] 处理概念 886030...
2025-07-10 22:26:16,306 - __main__ - INFO - 📈 [316/371] 处理概念 886032...
2025-07-10 22:26:16,428 - __main__ - INFO - 📈 [317/371] 处理概念 886033...
2025-07-10 22:26:16,549 - __main__ - INFO - 📈 [318/371] 处理概念 886034...
2025-07-10 22:26:16,669 - __main__ - INFO - 📈 [319/371] 处理概念 886035...
2025-07-10 22:26:16,799 - __main__ - INFO - 📈 [320/371] 处理概念 886036...
2025-07-10 22:26:16,946 - __main__ - INFO - 📈 [321/371] 处理概念 886037...
2025-07-10 22:26:17,078 - __main__ - INFO - 📈 [322/371] 处理概念 886038...
2025-07-10 22:26:17,212 - __main__ - INFO - 📈 [323/371] 处理概念 886039...
2025-07-10 22:26:17,355 - __main__ - INFO - 📈 [324/371] 处理概念 886041...
2025-07-10 22:26:17,478 - __main__ - INFO - 📈 [325/371] 处理概念 886042...
2025-07-10 22:26:17,604 - __main__ - INFO - 📈 [326/371] 处理概念 886043...
2025-07-10 22:26:17,724 - __main__ - INFO - 📈 [327/371] 处理概念 886044...
2025-07-10 22:26:17,836 - __main__ - INFO - 📈 [328/371] 处理概念 886045...
2025-07-10 22:26:17,956 - __main__ - INFO - 📈 [329/371] 处理概念 886047...
2025-07-10 22:26:18,119 - __main__ - INFO - 📈 [330/371] 处理概念 886048...
2025-07-10 22:26:18,258 - __main__ - INFO - 📈 [331/371] 处理概念 886049...
2025-07-10 22:26:18,404 - __main__ - INFO - 📈 [332/371] 处理概念 886050...
2025-07-10 22:26:18,540 - __main__ - INFO - 📈 [333/371] 处理概念 886051...
2025-07-10 22:26:18,864 - __main__ - INFO - 📈 [334/371] 处理概念 886052...
2025-07-10 22:26:18,986 - __main__ - INFO - 📈 [335/371] 处理概念 886053...
2025-07-10 22:26:19,084 - __main__ - INFO - 📈 [336/371] 处理概念 886054...
2025-07-10 22:26:19,178 - __main__ - INFO - 📈 [337/371] 处理概念 886055...
2025-07-10 22:26:19,284 - __main__ - INFO - 📈 [338/371] 处理概念 886056...
2025-07-10 22:26:19,378 - __main__ - INFO - 📈 [339/371] 处理概念 886057...
2025-07-10 22:26:19,462 - __main__ - INFO - 📈 [340/371] 处理概念 886058...
2025-07-10 22:26:19,550 - __main__ - INFO - 📈 [341/371] 处理概念 886059...
2025-07-10 22:26:19,632 - __main__ - INFO - 📈 [342/371] 处理概念 886060...
2025-07-10 22:26:19,716 - __main__ - INFO - 📈 [343/371] 处理概念 886061...
2025-07-10 22:26:19,806 - __main__ - INFO - 📈 [344/371] 处理概念 886062...
2025-07-10 22:26:19,887 - __main__ - INFO - 📈 [345/371] 处理概念 886063...
2025-07-10 22:26:19,974 - __main__ - INFO - 📈 [346/371] 处理概念 886064...
2025-07-10 22:26:20,058 - __main__ - INFO - 📈 [347/371] 处理概念 886065...
2025-07-10 22:26:20,144 - __main__ - INFO - 📈 [348/371] 处理概念 886066...
2025-07-10 22:26:20,234 - __main__ - INFO - 📈 [349/371] 处理概念 886067...
2025-07-10 22:26:20,368 - __main__ - INFO - 📈 [350/371] 处理概念 886068...
2025-07-10 22:26:20,474 - __main__ - INFO - 📊 进度: 350/371 (94.3%) - 成功:349, 无数据:0, 错误:1
2025-07-10 22:26:20,474 - __main__ - INFO - 📈 [351/371] 处理概念 886069...
2025-07-10 22:26:20,572 - __main__ - INFO - 📈 [352/371] 处理概念 886070...
2025-07-10 22:26:20,666 - __main__ - INFO - 📈 [353/371] 处理概念 886071...
2025-07-10 22:26:20,748 - __main__ - INFO - 📈 [354/371] 处理概念 886072...
2025-07-10 22:26:20,830 - __main__ - INFO - 📈 [355/371] 处理概念 886073...
2025-07-10 22:26:20,916 - __main__ - INFO - 📈 [356/371] 处理概念 886074...
2025-07-10 22:26:21,048 - __main__ - INFO - 📈 [357/371] 处理概念 886075...
2025-07-10 22:26:21,174 - __main__ - INFO - 📈 [358/371] 处理概念 886076...
2025-07-10 22:26:21,262 - __main__ - INFO - 📈 [359/371] 处理概念 886077...
2025-07-10 22:26:21,354 - __main__ - INFO - 📈 [360/371] 处理概念 886078...
2025-07-10 22:26:21,438 - __main__ - INFO - 📈 [361/371] 处理概念 886094...
2025-07-10 22:26:21,520 - __main__ - INFO - 📈 [362/371] 处理概念 886095...
2025-07-10 22:26:21,601 - __main__ - INFO - 📈 [363/371] 处理概念 886096...
2025-07-10 22:26:21,756 - __main__ - INFO - 📈 [364/371] 处理概念 886097...
2025-07-10 22:26:21,836 - __main__ - WARNING - ❌ 概念 886097 数据获取失败: 7 columns passed, passed data had 1 columns
2025-07-10 22:26:21,837 - __main__ - INFO - 📈 [365/371] 处理概念 886098...
2025-07-10 22:26:21,950 - __main__ - INFO - 📈 [366/371] 处理概念 886099...
2025-07-10 22:26:22,066 - __main__ - INFO - 📈 [367/371] 处理概念 886100...
2025-07-10 22:26:22,153 - __main__ - INFO - 📈 [368/371] 处理概念 886101...
2025-07-10 22:26:22,243 - __main__ - INFO - 📈 [369/371] 处理概念 886102...
2025-07-10 22:26:22,404 - __main__ - INFO - 📈 [370/371] 处理概念 886103...
2025-07-10 22:26:22,502 - __main__ - INFO - 📈 [371/371] 处理概念 886104...
2025-07-10 22:26:22,647 - __main__ - INFO - 🎉 概念日线数据补充完成！
2025-07-10 22:26:22,648 - __main__ - INFO - 📊 最终统计:
2025-07-10 22:26:22,648 - __main__ - INFO -    总概念数: 371
2025-07-10 22:26:22,649 - __main__ - INFO -    成功补充: 369
2025-07-10 22:26:22,649 - __main__ - INFO -    无数据: 0
2025-07-10 22:26:22,649 - __main__ - INFO -    错误: 2
2025-07-10 22:26:22,649 - __main__ - INFO -    成功率: 99.5%
2025-07-10 22:26:22,650 - __main__ - INFO - 🎉 2025-07-09 概念日线数据补充完成！
