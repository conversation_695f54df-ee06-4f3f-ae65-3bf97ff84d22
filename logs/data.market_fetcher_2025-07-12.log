2025-07-12 22:22:17,796 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-07-12 22:22:17,797 - data.market_fetcher - INFO - 📊 数据源配置:
2025-07-12 22:22:17,797 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-07-12 22:22:17,797 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-07-12 22:22:17,798 - data.market_fetcher - INFO -    概念数据: adata
2025-07-12 22:22:17,798 - data.market_fetcher - INFO -    风险数据: adata
2025-07-12 22:22:17,798 - data.market_fetcher - INFO -    指数数据: adata
2025-07-12 22:22:17,804 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-07-12 22:22:17,804 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-07-12 22:22:17,805 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-07-12 22:22:17,805 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-07-12 22:22:17,805 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-07-12 22:22:17,805 - data.market_fetcher - INFO - 📊 数据源配置:
2025-07-12 22:22:17,805 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-07-12 22:22:17,806 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-07-12 22:22:17,806 - data.market_fetcher - INFO -    概念数据: adata
2025-07-12 22:22:17,806 - data.market_fetcher - INFO -    风险数据: adata
2025-07-12 22:22:17,806 - data.market_fetcher - INFO -    指数数据: adata
2025-07-12 22:22:17,806 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-07-12 22:22:17,806 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-07-12 22:22:17,806 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-07-12 22:22:17,807 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-07-12 22:32:21,166 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-07-12 22:32:21,166 - data.market_fetcher - INFO - 📊 数据源配置:
2025-07-12 22:32:21,167 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-07-12 22:32:21,167 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-07-12 22:32:21,167 - data.market_fetcher - INFO -    概念数据: adata
2025-07-12 22:32:21,167 - data.market_fetcher - INFO -    风险数据: adata
2025-07-12 22:32:21,167 - data.market_fetcher - INFO -    指数数据: adata
2025-07-12 22:32:21,167 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-07-12 22:32:21,168 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-07-12 22:32:21,168 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-07-12 22:32:21,168 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-07-12 22:32:21,168 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-07-12 22:32:21,169 - data.market_fetcher - INFO - 📊 数据源配置:
2025-07-12 22:32:21,169 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-07-12 22:32:21,169 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-07-12 22:32:21,169 - data.market_fetcher - INFO -    概念数据: adata
2025-07-12 22:32:21,169 - data.market_fetcher - INFO -    风险数据: adata
2025-07-12 22:32:21,169 - data.market_fetcher - INFO -    指数数据: adata
2025-07-12 22:32:21,170 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-07-12 22:32:21,170 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-07-12 22:32:21,170 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-07-12 22:32:21,170 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-07-12 22:33:37,551 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-07-12 22:33:37,551 - data.market_fetcher - INFO - 📊 数据源配置:
2025-07-12 22:33:37,551 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-07-12 22:33:37,552 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-07-12 22:33:37,552 - data.market_fetcher - INFO -    概念数据: adata
2025-07-12 22:33:37,552 - data.market_fetcher - INFO -    风险数据: adata
2025-07-12 22:33:37,552 - data.market_fetcher - INFO -    指数数据: adata
2025-07-12 22:33:37,553 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-07-12 22:33:37,553 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-07-12 22:33:37,553 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-07-12 22:33:37,554 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-07-12 22:33:37,554 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-07-12 22:33:37,554 - data.market_fetcher - INFO - 📊 数据源配置:
2025-07-12 22:33:37,554 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-07-12 22:33:37,555 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-07-12 22:33:37,555 - data.market_fetcher - INFO -    概念数据: adata
2025-07-12 22:33:37,555 - data.market_fetcher - INFO -    风险数据: adata
2025-07-12 22:33:37,555 - data.market_fetcher - INFO -    指数数据: adata
2025-07-12 22:33:37,555 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-07-12 22:33:37,555 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-07-12 22:33:37,556 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-07-12 22:33:37,556 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-07-12 22:33:37,574 - data.market_fetcher - WARNING - ⚠️ 数据库中没有股票信息，调用collect_stock_info更新...
2025-07-12 22:33:37,574 - data.market_fetcher - INFO - 📊 股票基础信息采集功能已优化为使用mootdx数据源
2025-07-12 22:33:37,577 - data.market_fetcher - INFO - ✅ 更新后从数据库获取到 0 只股票
2025-07-12 22:33:37,577 - data.market_fetcher - ERROR - ❌ 没有可采集的股票代码
2025-07-12 22:33:39,584 - data.market_fetcher - INFO - ✅ 指数 上证指数(000001) 日线数据采集完成: 1825 条记录
2025-07-12 22:33:39,738 - data.market_fetcher - INFO - ✅ 指数 000001 分钟数据采集完成: 64 条记录
2025-07-12 22:33:41,425 - data.market_fetcher - INFO - ✅ 指数 深证成指(399001) 日线数据采集完成: 1825 条记录
2025-07-12 22:33:41,573 - data.market_fetcher - INFO - ✅ 指数 399001 分钟数据采集完成: 64 条记录
2025-07-12 22:33:43,421 - data.market_fetcher - INFO - ✅ 指数 创业板指(399006) 日线数据采集完成: 1825 条记录
2025-07-12 22:33:43,567 - data.market_fetcher - INFO - ✅ 指数 399006 分钟数据采集完成: 64 条记录
2025-07-12 22:33:45,401 - data.market_fetcher - INFO - ✅ 指数 中证1000(000852) 日线数据采集完成: 1825 条记录
2025-07-12 22:39:43,603 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-07-12 22:39:43,604 - data.market_fetcher - INFO - 📊 数据源配置:
2025-07-12 22:39:43,604 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-07-12 22:39:43,604 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-07-12 22:39:43,604 - data.market_fetcher - INFO -    概念数据: adata
2025-07-12 22:39:43,605 - data.market_fetcher - INFO -    风险数据: adata
2025-07-12 22:39:43,605 - data.market_fetcher - INFO -    指数数据: adata
2025-07-12 22:39:43,605 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-07-12 22:39:43,605 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-07-12 22:39:43,606 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-07-12 22:39:43,606 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-07-12 22:39:43,606 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-07-12 22:39:43,607 - data.market_fetcher - INFO - 📊 数据源配置:
2025-07-12 22:39:43,607 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-07-12 22:39:43,607 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-07-12 22:39:43,608 - data.market_fetcher - INFO -    概念数据: adata
2025-07-12 22:39:43,608 - data.market_fetcher - INFO -    风险数据: adata
2025-07-12 22:39:43,608 - data.market_fetcher - INFO -    指数数据: adata
2025-07-12 22:39:43,608 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-07-12 22:39:43,608 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-07-12 22:39:43,609 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-07-12 22:39:43,609 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-07-12 22:39:43,622 - data.market_fetcher - WARNING - ⚠️ 数据库中没有股票信息，调用collect_stock_info更新...
2025-07-12 22:39:43,622 - data.market_fetcher - ERROR - ❌ 股票基础信息采集失败: name 'ak' is not defined
2025-07-12 22:39:43,625 - data.market_fetcher - INFO - ✅ 更新后从数据库获取到 0 只股票
2025-07-12 22:39:43,625 - data.market_fetcher - ERROR - ❌ 没有可采集的股票代码
2025-07-12 22:39:45,472 - data.market_fetcher - INFO - ✅ 指数 上证指数(000001) 日线数据采集完成: 1825 条记录
2025-07-12 22:39:45,611 - data.market_fetcher - INFO - ✅ 指数 000001 分钟数据采集完成: 64 条记录
2025-07-12 22:41:02,471 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-07-12 22:41:02,471 - data.market_fetcher - INFO - 📊 数据源配置:
2025-07-12 22:41:02,472 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-07-12 22:41:02,472 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-07-12 22:41:02,472 - data.market_fetcher - INFO -    概念数据: adata
2025-07-12 22:41:02,472 - data.market_fetcher - INFO -    风险数据: adata
2025-07-12 22:41:02,473 - data.market_fetcher - INFO -    指数数据: adata
2025-07-12 22:41:02,473 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-07-12 22:41:02,473 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-07-12 22:41:02,473 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-07-12 22:41:02,473 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-07-12 22:41:02,474 - data.market_fetcher - INFO - ✅ 市场数据采集器初始化完成
2025-07-12 22:41:02,474 - data.market_fetcher - INFO - 📊 数据源配置:
2025-07-12 22:41:02,474 - data.market_fetcher - INFO -    股票日线数据: mootdx（通达信接口）
2025-07-12 22:41:02,475 - data.market_fetcher - INFO -    股票分钟数据: mootdx（通达信接口）
2025-07-12 22:41:02,475 - data.market_fetcher - INFO -    概念数据: adata
2025-07-12 22:41:02,475 - data.market_fetcher - INFO -    风险数据: adata
2025-07-12 22:41:02,475 - data.market_fetcher - INFO -    指数数据: adata
2025-07-12 22:41:02,475 - data.market_fetcher - INFO - 📋 mootdx配置: 默认240条，强制下载2000条
2025-07-12 22:41:02,476 - data.market_fetcher - INFO - 🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取
2025-07-12 22:41:02,476 - data.market_fetcher - INFO - ⚙️ 分段配置: 每段6个月，最多16段，回溯8年
2025-07-12 22:41:02,476 - data.market_fetcher - INFO - ⚙️ 高级功能: 去重启用，时间排序启用
2025-07-12 22:41:02,488 - data.market_fetcher - WARNING - ⚠️ 数据库中没有股票信息，调用collect_stock_info更新...
2025-07-12 22:41:17,507 - data.market_fetcher - INFO - ✅ 股票基础信息采集完成: 4382 条记录
2025-07-12 22:41:17,515 - data.market_fetcher - INFO - ✅ 更新后从数据库获取到 4382 只股票
2025-07-12 22:41:17,516 - data.market_fetcher - INFO - 🚀 开始股票数据更新（单循环高效版本）：4382 只股票
2025-07-12 22:41:17,516 - data.market_fetcher - INFO - 📊 数据模式：每日增量模式（日线1条+5分钟48条+15分钟16条）
2025-07-12 22:41:17,516 - data.market_fetcher - INFO - 📅 更新日期：2018-01-01 到 2025-07-12
2025-07-12 22:41:26,802 - data.market_fetcher - ERROR - 获取通达信风险评估得分时出错: 'NoneType' object is not iterable
2025-07-12 22:41:28,366 - data.market_fetcher - ERROR - 获取通达信风险评估得分时出错: 'NoneType' object is not iterable
2025-07-12 22:41:28,626 - data.market_fetcher - ERROR - 获取通达信风险评估得分时出错: 'NoneType' object is not iterable
2025-07-12 22:41:31,201 - data.market_fetcher - ERROR - 获取通达信风险评估得分时出错: 'NoneType' object is not iterable
