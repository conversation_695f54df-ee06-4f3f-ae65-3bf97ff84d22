2025-07-09 08:30:42,913 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 08:30:42,934 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 08:30:42,938 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 08:30:42,938 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 08:30:57,688 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 08:30:57,690 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 08:36:13,818 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 08:36:13,839 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 08:36:13,843 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 08:36:13,843 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 08:44:04,077 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 08:44:04,078 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 11:53:14,838 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 11:53:14,860 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 11:53:14,863 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 11:53:14,863 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 11:54:04,474 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 11:54:04,477 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 11:57:46,857 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 11:57:46,887 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 11:57:46,892 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 11:57:46,892 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 11:58:13,755 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 11:58:13,756 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 11:59:17,937 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 11:59:17,958 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 11:59:17,961 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 11:59:17,961 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 11:59:44,833 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 11:59:44,833 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 12:07:44,268 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 12:07:44,289 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 12:07:44,292 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 12:07:44,292 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 12:08:12,841 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 12:08:12,841 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 12:15:59,874 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 12:15:59,895 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 12:15:59,898 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 12:15:59,898 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 12:16:49,786 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 12:16:49,787 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 12:18:50,212 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 12:18:50,233 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 12:18:50,236 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 12:18:50,236 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 12:19:23,225 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 12:19:23,230 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 12:24:27,930 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 12:24:27,951 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 12:24:27,954 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 12:24:27,954 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 12:25:27,534 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 12:25:27,539 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 12:25:51,337 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 12:25:51,357 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 12:25:51,360 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 12:25:51,360 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 12:26:23,832 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 12:26:23,833 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 12:31:20,355 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 12:31:20,376 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 12:31:20,380 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 12:31:20,380 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 12:31:56,551 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 12:31:56,551 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 12:32:32,298 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 12:32:32,320 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 12:32:32,323 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 12:32:32,323 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 12:32:57,546 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 12:32:57,553 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 12:54:11,088 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 12:54:11,110 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 12:54:11,113 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 12:54:11,113 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 12:54:32,240 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 12:54:32,241 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 13:01:37,334 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 13:01:37,354 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 13:01:37,357 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 13:01:37,357 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 13:03:54,503 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 13:03:54,507 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 13:11:10,169 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 13:11:10,191 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 13:11:10,194 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 13:11:10,194 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 13:11:30,447 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 13:11:30,450 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 13:24:09,336 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 13:24:09,359 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 13:24:09,362 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 13:24:09,362 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 13:26:52,557 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 13:26:52,557 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 14:58:47,574 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 14:58:47,597 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 14:58:47,601 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 14:58:47,602 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 14:59:02,357 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 14:59:02,358 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 15:06:30,870 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 15:06:30,890 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 15:06:30,893 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 15:06:30,893 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 15:06:42,436 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 15:06:42,437 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 15:08:58,271 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 15:08:58,292 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 15:08:58,295 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 15:08:58,295 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 15:09:10,591 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 15:09:10,591 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 15:36:43,479 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 15:36:43,499 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 15:36:43,502 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 15:36:43,502 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 15:37:31,294 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 15:37:31,297 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 15:39:25,926 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 15:39:25,947 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 15:39:25,950 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 15:39:25,950 - __main__ - INFO - 🔄 强制执行历史数据初始化
2025-07-09 15:39:41,516 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 15:39:41,517 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 15:40:23,273 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 15:40:23,295 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 15:40:23,297 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 15:40:23,297 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 15:41:37,534 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 15:41:37,535 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 15:42:58,866 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 15:42:58,890 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 15:42:58,892 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 15:42:58,892 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 15:43:29,717 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 15:43:29,718 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 15:52:44,475 - __main__ - INFO - 🎯 开始分钟数据2000条获取功能测试
2025-07-09 15:52:44,475 - __main__ - INFO - 🚀 开始测试分钟数据2000条获取功能
2025-07-09 15:55:27,428 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 15:55:27,448 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 15:55:27,450 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 15:55:27,450 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 15:56:06,582 - __main__ - INFO - 🎯 开始分钟数据2000条获取功能测试
2025-07-09 15:56:06,582 - __main__ - INFO - 🚀 开始测试分钟数据2000条获取功能
2025-07-09 15:56:13,158 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 15:56:13,158 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 15:57:33,379 - __main__ - INFO - 🎯 开始分钟数据2000条获取功能测试
2025-07-09 15:57:33,379 - __main__ - INFO - 🚀 开始测试分钟数据2000条获取功能
2025-07-09 15:58:49,931 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 15:58:49,953 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 15:58:49,955 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 15:58:49,955 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 15:59:18,308 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 15:59:18,309 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 16:01:16,462 - __main__ - INFO - 🎯 开始偏移量策略测试
2025-07-09 16:01:16,462 - __main__ - INFO - 🚀 开始测试偏移量策略获取分钟数据
2025-07-09 16:02:16,868 - __main__ - INFO - 🎯 开始偏移量策略测试
2025-07-09 16:02:16,868 - __main__ - INFO - 🚀 开始测试偏移量策略获取分钟数据
2025-07-09 16:02:17,026 - __main__ - INFO - ✅ MarketDataFetcher 初始化成功
2025-07-09 16:02:17,026 - __main__ - INFO - 📋 当前mootdx配置:
2025-07-09 16:02:17,026 - __main__ - INFO -    强制下载限制: 2000 条
2025-07-09 16:02:17,026 - __main__ - INFO -    复权类型: qfq
2025-07-09 16:02:17,026 - __main__ - INFO -    启用分钟数据时间分段: True
2025-07-09 16:02:17,026 - __main__ - INFO - 
📊 测试股票: 000001
2025-07-09 16:02:17,026 - __main__ - INFO - 
🔍 测试 5 分钟数据获取（偏移量策略）...
2025-07-09 16:02:17,026 - __main__ - INFO - 📈 强制下载模式测试 (5分钟)...
2025-07-09 16:02:17,419 - __main__ - ERROR - ❌ 偏移量策略获取失败: 无数据
2025-07-09 16:02:17,420 - __main__ - INFO - 
🔍 测试 15 分钟数据获取（偏移量策略）...
2025-07-09 16:02:17,420 - __main__ - INFO - 📈 强制下载模式测试 (15分钟)...
2025-07-09 16:02:17,735 - __main__ - ERROR - ❌ 偏移量策略获取失败: 无数据
2025-07-09 16:02:17,735 - __main__ - INFO - 
📊 测试股票: 000002
2025-07-09 16:02:17,735 - __main__ - INFO - 
🔍 测试 5 分钟数据获取（偏移量策略）...
2025-07-09 16:02:17,735 - __main__ - INFO - 📈 强制下载模式测试 (5分钟)...
2025-07-09 16:02:18,044 - __main__ - ERROR - ❌ 偏移量策略获取失败: 无数据
2025-07-09 16:02:18,044 - __main__ - INFO - 
🔍 测试 15 分钟数据获取（偏移量策略）...
2025-07-09 16:02:18,044 - __main__ - INFO - 📈 强制下载模式测试 (15分钟)...
2025-07-09 16:02:18,353 - __main__ - ERROR - ❌ 偏移量策略获取失败: 无数据
2025-07-09 16:02:18,414 - __main__ - INFO - 
🔍 比较偏移量策略和原始策略的效果
2025-07-09 16:02:18,829 - __main__ - INFO - 📈 测试偏移量策略...
2025-07-09 16:02:19,137 - __main__ - INFO - 📊 策略对比结果:
2025-07-09 16:02:19,137 - __main__ - INFO -    目标数据量: 2000 条
2025-07-09 16:02:19,138 - __main__ - INFO -    偏移量策略: 0 条
2025-07-09 16:02:19,138 - __main__ - ERROR - ❌ 偏移量策略获取失败
2025-07-09 16:02:19,391 - __main__ - INFO - 🏁 测试完成
2025-07-09 16:08:13,604 - __main__ - INFO - 🎯 开始偏移量策略测试
2025-07-09 16:08:13,604 - __main__ - INFO - 🚀 开始测试偏移量策略获取分钟数据
2025-07-09 16:08:13,722 - __main__ - INFO - ✅ MarketDataFetcher 初始化成功
2025-07-09 16:08:13,722 - __main__ - INFO - 📋 当前mootdx配置:
2025-07-09 16:08:13,722 - __main__ - INFO -    强制下载限制: 2000 条
2025-07-09 16:08:13,722 - __main__ - INFO -    复权类型: qfq
2025-07-09 16:08:13,722 - __main__ - INFO -    启用分钟数据时间分段: True
2025-07-09 16:08:13,723 - __main__ - INFO - 
📊 测试股票: 000001
2025-07-09 16:08:13,723 - __main__ - INFO - 
🔍 测试 5 分钟数据获取（偏移量策略）...
2025-07-09 16:08:13,723 - __main__ - INFO - 📈 强制下载模式测试 (5分钟)...
2025-07-09 16:08:13,951 - __main__ - INFO - ✅ 偏移量策略成功: 1600 条数据
2025-07-09 16:08:13,952 - __main__ - INFO -    时间范围: 2025-06-17 10:55:00 到 2025-07-09 15:00:00
2025-07-09 16:08:13,952 - __main__ - INFO - 🎯 数据量达标: 1600/2000 (80.0%)
2025-07-09 16:08:13,952 - __main__ - INFO -    收盘价范围: 11.66 - 12.89
2025-07-09 16:08:13,960 - __main__ - INFO -    时间连续性: 47.9% (766/1599)
2025-07-09 16:08:13,960 - __main__ - WARNING - ⚠️ 发现重复时间: 800 条
2025-07-09 16:08:13,961 - __main__ - INFO - 📋 数据样本（前5条）:
2025-07-09 16:08:13,961 - __main__ - INFO -    2025-06-17 10:55:00 | 开:11.77 高:11.78 低:11.75 收:11.75
2025-07-09 16:08:13,961 - __main__ - INFO -    2025-06-17 10:55:00 | 开:11.77 高:11.78 低:11.75 收:11.75
2025-07-09 16:08:13,961 - __main__ - INFO -    2025-06-17 11:00:00 | 开:11.76 高:11.76 低:11.74 收:11.75
2025-07-09 16:08:13,961 - __main__ - INFO -    2025-06-17 11:00:00 | 开:11.76 高:11.76 低:11.74 收:11.75
2025-07-09 16:08:13,962 - __main__ - INFO -    2025-06-17 11:05:00 | 开:11.76 高:11.77 低:11.75 收:11.76
2025-07-09 16:08:13,962 - __main__ - INFO - 
🔍 测试 15 分钟数据获取（偏移量策略）...
2025-07-09 16:08:13,962 - __main__ - INFO - 📈 强制下载模式测试 (15分钟)...
2025-07-09 16:08:14,101 - __main__ - INFO - ✅ 偏移量策略成功: 1600 条数据
2025-07-09 16:08:14,102 - __main__ - INFO -    时间范围: 2025-04-25 09:45:00 到 2025-07-09 15:00:00
2025-07-09 16:08:14,102 - __main__ - INFO - 🎯 数据量达标: 1600/2000 (80.0%)
2025-07-09 16:08:14,102 - __main__ - INFO -    收盘价范围: 10.90 - 12.84
2025-07-09 16:08:14,108 - __main__ - INFO -    时间连续性: 43.8% (700/1599)
2025-07-09 16:08:14,109 - __main__ - WARNING - ⚠️ 发现重复时间: 800 条
2025-07-09 16:08:14,109 - __main__ - INFO - 📋 数据样本（前5条）:
2025-07-09 16:08:14,109 - __main__ - INFO -    2025-04-25 09:45:00 | 开:11.04 高:11.05 低:11.01 收:11.02
2025-07-09 16:08:14,110 - __main__ - INFO -    2025-04-25 09:45:00 | 开:11.04 高:11.05 低:11.01 收:11.02
2025-07-09 16:08:14,110 - __main__ - INFO -    2025-04-25 10:00:00 | 开:11.02 高:11.04 低:11.01 收:11.02
2025-07-09 16:08:14,110 - __main__ - INFO -    2025-04-25 10:00:00 | 开:11.02 高:11.04 低:11.01 收:11.02
2025-07-09 16:08:14,110 - __main__ - INFO -    2025-04-25 10:15:00 | 开:11.01 高:11.02 低:11.00 收:11.01
2025-07-09 16:08:14,110 - __main__ - INFO - 
📊 测试股票: 000002
2025-07-09 16:08:14,110 - __main__ - INFO - 
🔍 测试 5 分钟数据获取（偏移量策略）...
2025-07-09 16:08:14,110 - __main__ - INFO - 📈 强制下载模式测试 (5分钟)...
2025-07-09 16:08:14,246 - __main__ - INFO - ✅ 偏移量策略成功: 1600 条数据
2025-07-09 16:08:14,247 - __main__ - INFO -    时间范围: 2025-06-17 10:55:00 到 2025-07-09 15:00:00
2025-07-09 16:08:14,247 - __main__ - INFO - 🎯 数据量达标: 1600/2000 (80.0%)
2025-07-09 16:08:14,247 - __main__ - INFO -    收盘价范围: 6.21 - 6.58
2025-07-09 16:08:14,254 - __main__ - INFO -    时间连续性: 47.9% (766/1599)
2025-07-09 16:08:14,254 - __main__ - WARNING - ⚠️ 发现重复时间: 800 条
2025-07-09 16:08:14,254 - __main__ - INFO - 📋 数据样本（前5条）:
2025-07-09 16:08:14,254 - __main__ - INFO -    2025-06-17 10:55:00 | 开:6.52 高:6.53 低:6.52 收:6.52
2025-07-09 16:08:14,255 - __main__ - INFO -    2025-06-17 10:55:00 | 开:6.52 高:6.53 低:6.52 收:6.52
2025-07-09 16:08:14,255 - __main__ - INFO -    2025-06-17 11:00:00 | 开:6.53 高:6.53 低:6.52 收:6.52
2025-07-09 16:08:14,255 - __main__ - INFO -    2025-06-17 11:00:00 | 开:6.53 高:6.53 低:6.52 收:6.52
2025-07-09 16:08:14,255 - __main__ - INFO -    2025-06-17 11:05:00 | 开:6.52 高:6.53 低:6.52 收:6.53
2025-07-09 16:08:14,255 - __main__ - INFO - 
🔍 测试 15 分钟数据获取（偏移量策略）...
2025-07-09 16:08:14,255 - __main__ - INFO - 📈 强制下载模式测试 (15分钟)...
2025-07-09 16:08:14,390 - __main__ - INFO - ✅ 偏移量策略成功: 1600 条数据
2025-07-09 16:08:14,391 - __main__ - INFO -    时间范围: 2025-04-25 09:45:00 到 2025-07-09 15:00:00
2025-07-09 16:08:14,391 - __main__ - INFO - 🎯 数据量达标: 1600/2000 (80.0%)
2025-07-09 16:08:14,391 - __main__ - INFO -    收盘价范围: 6.22 - 7.23
2025-07-09 16:08:14,398 - __main__ - INFO -    时间连续性: 43.8% (700/1599)
2025-07-09 16:08:14,398 - __main__ - WARNING - ⚠️ 发现重复时间: 800 条
2025-07-09 16:08:14,398 - __main__ - INFO - 📋 数据样本（前5条）:
2025-07-09 16:08:14,399 - __main__ - INFO -    2025-04-25 09:45:00 | 开:7.09 高:7.14 低:7.06 收:7.12
2025-07-09 16:08:14,399 - __main__ - INFO -    2025-04-25 09:45:00 | 开:7.09 高:7.14 低:7.06 收:7.12
2025-07-09 16:08:14,399 - __main__ - INFO -    2025-04-25 10:00:00 | 开:7.12 高:7.17 低:7.11 收:7.16
2025-07-09 16:08:14,399 - __main__ - INFO -    2025-04-25 10:00:00 | 开:7.12 高:7.17 低:7.11 收:7.16
2025-07-09 16:08:14,399 - __main__ - INFO -    2025-04-25 10:15:00 | 开:7.16 高:7.27 低:7.16 收:7.23
2025-07-09 16:08:14,517 - __main__ - INFO - 
🔍 比较偏移量策略和原始策略的效果
2025-07-09 16:08:14,685 - __main__ - INFO - 📈 测试偏移量策略...
2025-07-09 16:08:14,823 - __main__ - INFO - 📊 策略对比结果:
2025-07-09 16:08:14,823 - __main__ - INFO -    目标数据量: 2000 条
2025-07-09 16:08:14,823 - __main__ - INFO -    偏移量策略: 1600 条
2025-07-09 16:08:14,823 - __main__ - INFO - ✅ 偏移量策略成功: 达成率 80.0%
2025-07-09 16:08:14,824 - __main__ - INFO -    时间覆盖范围: 22 天
2025-07-09 16:08:14,860 - __main__ - INFO - 🏁 测试完成
2025-07-09 16:12:20,639 - __main__ - INFO - 🚀 开始简单偏移量策略测试
2025-07-09 16:12:20,770 - __main__ - INFO - ✅ MarketDataFetcher 初始化成功
2025-07-09 16:12:20,770 - __main__ - INFO - 📋 当前mootdx配置:
2025-07-09 16:12:20,770 - __main__ - INFO -    强制下载限制: 2000 条
2025-07-09 16:12:20,770 - __main__ - INFO -    复权类型: qfq
2025-07-09 16:12:20,771 - __main__ - INFO - 
📊 测试股票: 000001
2025-07-09 16:12:20,771 - __main__ - INFO - 🔍 测试 5 分钟数据获取（偏移量策略）...
2025-07-09 16:12:20,771 - __main__ - INFO - 📈 强制下载模式测试 (5分钟)...
2025-07-09 16:12:21,002 - __main__ - INFO - ✅ 偏移量策略成功: 1600 条数据
2025-07-09 16:12:21,003 - __main__ - INFO -    时间范围: 2025-06-17 10:55:00 到 2025-07-09 15:00:00
2025-07-09 16:12:21,003 - __main__ - INFO - 🎯 数据量达标: 1600/2000 (80.0%)
2025-07-09 16:12:21,003 - __main__ - INFO -    收盘价范围: 11.66 - 12.89
2025-07-09 16:12:21,003 - __main__ - INFO - 📋 数据样本（前5条）:
2025-07-09 16:12:21,003 - __main__ - INFO -    2025-06-17 10:55:00 | 开:11.77 高:11.78 低:11.75 收:11.75
2025-07-09 16:12:21,004 - __main__ - INFO -    2025-06-17 10:55:00 | 开:11.77 高:11.78 低:11.75 收:11.75
2025-07-09 16:12:21,004 - __main__ - INFO -    2025-06-17 11:00:00 | 开:11.76 高:11.76 低:11.74 收:11.75
2025-07-09 16:12:21,004 - __main__ - INFO -    2025-06-17 11:00:00 | 开:11.76 高:11.76 低:11.74 收:11.75
2025-07-09 16:12:21,004 - __main__ - INFO -    2025-06-17 11:05:00 | 开:11.76 高:11.77 低:11.75 收:11.76
2025-07-09 16:12:21,044 - __main__ - INFO - 🏁 测试完成
2025-07-09 16:21:52,073 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 16:21:52,094 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 16:21:52,097 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 16:21:52,097 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 16:22:22,914 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 16:22:22,914 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 16:38:17,681 - __main__ - INFO - 🎯 开始最终修复验证测试
2025-07-09 16:38:17,681 - __main__ - INFO - 🚀 开始最终修复验证测试
2025-07-09 16:38:18,383 - __main__ - INFO - ✅ MarketDataFetcher 初始化成功
2025-07-09 16:38:18,383 - __main__ - INFO - 📋 当前mootdx配置:
2025-07-09 16:38:18,383 - __main__ - INFO -    强制下载限制: 2000 条
2025-07-09 16:38:18,383 - __main__ - INFO -    复权类型: qfq
2025-07-09 16:38:18,383 - __main__ - INFO -    启用分钟数据时间分段: True
2025-07-09 16:38:18,383 - __main__ - INFO - 
📊 测试股票: 000001
2025-07-09 16:38:18,384 - __main__ - INFO - 
🔍 测试 5 分钟数据强制下载...
2025-07-09 16:38:18,608 - __main__ - INFO - ✅ 5分钟数据获取成功: 1600 条数据
2025-07-09 16:38:18,608 - __main__ - INFO -    达成率: 80.0% (1600/2000)
2025-07-09 16:38:18,609 - __main__ - INFO -    时间范围: 2025-06-17 10:55:00 到 2025-07-09 15:00:00
2025-07-09 16:38:18,609 - __main__ - INFO -    收盘价范围: 11.66 - 12.89
2025-07-09 16:38:18,609 - __main__ - INFO - 🎯 5分钟数据效果优秀: 80.0%
2025-07-09 16:38:18,609 - __main__ - INFO - 
🔍 测试 15 分钟数据强制下载...
2025-07-09 16:38:18,752 - __main__ - INFO - ✅ 15分钟数据获取成功: 1600 条数据
2025-07-09 16:38:18,752 - __main__ - INFO -    达成率: 80.0% (1600/2000)
2025-07-09 16:38:18,752 - __main__ - INFO -    时间范围: 2025-04-25 09:45:00 到 2025-07-09 15:00:00
2025-07-09 16:38:18,752 - __main__ - INFO -    收盘价范围: 10.90 - 12.84
2025-07-09 16:38:18,752 - __main__ - INFO - 🎯 15分钟数据效果优秀: 80.0%
2025-07-09 16:38:18,753 - __main__ - INFO - 
🔍 测试 collect_minute_data 方法（强制下载模式）...
2025-07-09 16:38:18,753 - __main__ - INFO - 📊 测试 collect_minute_data 方法，股票: ['000001']
2025-07-09 16:38:20,554 - __main__ - INFO - ✅ collect_minute_data 方法测试完成
2025-07-09 16:38:20,601 - __main__ - INFO - 
🔍 对比测试：默认模式 vs 强制下载模式
2025-07-09 16:38:20,930 - __main__ - INFO - 📈 测试默认模式 (5分钟)...
2025-07-09 16:38:20,995 - __main__ - INFO - 📈 测试强制下载模式 (5分钟)...
2025-07-09 16:38:21,133 - __main__ - INFO - 📊 对比结果:
2025-07-09 16:38:21,133 - __main__ - INFO -    默认模式: 240 条
2025-07-09 16:38:21,133 - __main__ - INFO -    强制下载模式: 1600 条
2025-07-09 16:38:21,133 - __main__ - INFO - 🚀 强制下载模式效果更好: 提升 6.7 倍
2025-07-09 16:38:21,239 - __main__ - INFO - 🏁 测试完成
2025-07-09 16:41:34,823 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 16:41:34,844 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 16:41:34,846 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 16:41:34,847 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 16:42:47,562 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 16:42:47,562 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 16:45:43,176 - __main__ - INFO - 🎯 开始数据重复问题测试
2025-07-09 16:45:43,176 - __main__ - INFO - 🚀 开始测试数据重复问题
2025-07-09 16:45:43,702 - __main__ - INFO - 📊 测试股票: 000001, 周期: 5分钟
2025-07-09 16:45:43,703 - __main__ - INFO - 📈 获取强制下载数据...
2025-07-09 16:45:43,925 - __main__ - INFO - ✅ 获取数据成功: 1600 条
2025-07-09 16:45:43,926 - __main__ - INFO - 📊 时间重复分析:
2025-07-09 16:45:43,926 - __main__ - INFO -    总数据量: 1600 条
2025-07-09 16:45:43,926 - __main__ - INFO -    唯一时间点: 800 个
2025-07-09 16:45:43,926 - __main__ - INFO -    重复时间点: 800 个
2025-07-09 16:45:43,926 - __main__ - WARNING - ⚠️ 发现 800 个重复时间点！
2025-07-09 16:45:43,927 - __main__ - INFO - 📋 重复时间点示例（前10个）:
2025-07-09 16:45:43,927 - __main__ - INFO -    2025-06-17 10:55:00: 2 次
2025-07-09 16:45:43,928 - __main__ - INFO -    2025-06-17 11:00:00: 2 次
2025-07-09 16:45:43,928 - __main__ - INFO -    2025-06-17 11:05:00: 2 次
2025-07-09 16:45:43,928 - __main__ - INFO -    2025-06-17 11:10:00: 2 次
2025-07-09 16:45:43,929 - __main__ - INFO -    2025-06-17 11:15:00: 2 次
2025-07-09 16:45:43,929 - __main__ - INFO -    2025-06-17 11:20:00: 2 次
2025-07-09 16:45:43,929 - __main__ - INFO -    2025-06-17 11:25:00: 2 次
2025-07-09 16:45:43,930 - __main__ - INFO -    2025-06-17 11:30:00: 2 次
2025-07-09 16:45:43,930 - __main__ - INFO -    2025-06-17 13:05:00: 2 次
2025-07-09 16:45:43,930 - __main__ - INFO -    2025-06-17 13:10:00: 2 次
2025-07-09 16:45:43,930 - __main__ - INFO - 📊 检查数据库中已有的数据...
2025-07-09 16:45:44,156 - __main__ - INFO - 📊 数据库中已有数据:
2025-07-09 16:45:44,157 - __main__ - INFO -    数据量: 800 条
2025-07-09 16:45:44,157 - __main__ - INFO -    时间范围: 2025-06-17 10:55:00+08:00 到 2025-07-09 15:00:00+08:00
2025-07-09 16:45:44,157 - __main__ - INFO - 📊 新获取数据:
2025-07-09 16:45:44,157 - __main__ - INFO -    时间范围: 2025-06-17 10:55:00 到 2025-07-09 15:00:00
2025-07-09 16:45:44,157 - __main__ - ERROR - ❌ 查询数据库失败: Cannot compare tz-naive and tz-aware timestamps
2025-07-09 16:45:44,157 - __main__ - INFO - 📊 数据时间分布分析:
2025-07-09 16:45:44,161 - __main__ - INFO - 📋 每日数据量分布:
2025-07-09 16:45:44,161 - __main__ - INFO -    2025-06-17: 64 条
2025-07-09 16:45:44,161 - __main__ - INFO -    2025-06-18: 96 条
2025-07-09 16:45:44,161 - __main__ - INFO -    2025-06-19: 96 条
2025-07-09 16:45:44,161 - __main__ - INFO -    2025-06-20: 96 条
2025-07-09 16:45:44,161 - __main__ - INFO -    2025-06-23: 96 条
2025-07-09 16:45:44,161 - __main__ - INFO -    2025-06-24: 96 条
2025-07-09 16:45:44,161 - __main__ - INFO -    2025-06-25: 96 条
2025-07-09 16:45:44,161 - __main__ - INFO -    2025-06-26: 96 条
2025-07-09 16:45:44,161 - __main__ - INFO -    2025-06-27: 96 条
2025-07-09 16:45:44,161 - __main__ - INFO -    2025-06-30: 96 条
2025-07-09 16:45:44,161 - __main__ - INFO -    ... 还有 7 天的数据
2025-07-09 16:45:44,360 - __main__ - INFO - 
🔍 测试数据库插入行为
2025-07-09 16:45:44,665 - __main__ - INFO - 🧹 已清空股票 000002 的测试数据
2025-07-09 16:45:44,665 - __main__ - INFO - 📈 获取测试数据...
2025-07-09 16:45:44,806 - __main__ - INFO - ✅ 获取测试数据成功: 1600 条
2025-07-09 16:45:44,806 - __main__ - INFO - 📊 手动插入数据到数据库...
2025-07-09 16:45:45,140 - __main__ - INFO - 📊 数据库返回插入数量: 1600
2025-07-09 16:45:45,272 - __main__ - INFO - 📊 数据库实际数据量: 800
2025-07-09 16:45:45,273 - __main__ - WARNING - ⚠️ 数据量不匹配！获取1600条，数据库800条
2025-07-09 16:45:45,514 - __main__ - INFO - 🏁 测试完成
2025-07-09 16:51:10,501 - __main__ - INFO - 🎯 开始测试修复后的偏移量策略
2025-07-09 16:51:10,501 - __main__ - INFO - 🚀 开始测试修复后的偏移量策略
2025-07-09 16:51:10,744 - __main__ - INFO - 📊 测试股票: 000003, 周期: 5分钟
2025-07-09 16:51:10,744 - __main__ - INFO - 📈 获取强制下载数据（修复后的偏移量策略）...
2025-07-09 16:51:10,909 - __main__ - ERROR - ❌ 获取数据失败: 无数据
2025-07-09 16:51:11,166 - __main__ - INFO - 
🔍 对比修复前后的效果
2025-07-09 16:51:11,877 - __main__ - INFO - 📊 修复前后对比:
2025-07-09 16:51:11,878 - __main__ - INFO -    修复前(000001): 800 条，2025-06-17 10:55:00+08:00 到 2025-07-09 15:00:00+08:00
2025-07-09 16:51:11,878 - __main__ - INFO -    修复后(000003): 0 条，None 到 None
2025-07-09 16:51:11,879 - __main__ - WARNING - ⚠️ 修复效果: 数据量有所下降
2025-07-09 16:51:11,957 - __main__ - INFO - 🏁 测试完成
2025-07-09 16:54:11,979 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 16:54:12,001 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 16:54:12,003 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 16:54:12,003 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 16:56:10,353 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 16:56:10,353 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 17:24:53,554 - __main__ - INFO - 🎯 开始测试 client.bars 方法参数
2025-07-09 17:24:53,554 - __main__ - INFO - 🚀 开始测试 client.bars 方法的参数
2025-07-09 17:24:53,652 - __main__ - INFO - 📊 测试股票: 000001, 频率: 0 (5分钟)
2025-07-09 17:24:53,652 - __main__ - INFO - 
🔍 测试1: 传统方式 - client.bars(symbol, frequency, offset)
2025-07-09 17:24:53,694 - __main__ - INFO - ✅ 传统方式成功: 800 条数据
2025-07-09 17:24:53,694 - __main__ - INFO -    时间范围: 2025-06-17 10:55:00 到 2025-07-09 15:00:00
2025-07-09 17:24:53,694 - __main__ - INFO - 
🔍 测试2: 尝试使用length参数 - client.bars(symbol, frequency, offset=0, length=800)
2025-07-09 17:24:53,714 - __main__ - WARNING - ⚠️ length参数返回空数据
2025-07-09 17:24:53,715 - __main__ - INFO - 
🔍 测试3: 尝试offset作为起始位置 - client.bars(symbol, frequency, offset=801, length=800)
2025-07-09 17:24:53,753 - __main__ - INFO - ✅ offset起始位置成功: 800 条数据
2025-07-09 17:24:53,754 - __main__ - INFO -    时间范围: 2025-06-17 10:55:00 到 2025-07-09 15:00:00
2025-07-09 17:24:53,754 - __main__ - INFO - 
🔍 测试4: 对比不同offset值的数据
2025-07-09 17:24:53,812 - __main__ - INFO - 
🔍 测试bars方法的方法签名
2025-07-09 17:24:53,834 - __main__ - INFO - 📋 bars方法签名: (symbol='000001', frequency=9, start=0, offset=800, **kwargs)
2025-07-09 17:24:53,834 - __main__ - INFO -    参数: symbol, 默认值: 000001
2025-07-09 17:24:53,834 - __main__ - INFO -    参数: frequency, 默认值: 9
2025-07-09 17:24:53,835 - __main__ - INFO -    参数: start, 默认值: 0
2025-07-09 17:24:53,835 - __main__ - INFO -    参数: offset, 默认值: 800
2025-07-09 17:24:53,835 - __main__ - INFO -    参数: kwargs, 默认值: 无默认值
2025-07-09 17:24:53,835 - __main__ - INFO - 
🔍 测试其他可能的数据获取方法
2025-07-09 17:24:53,855 - __main__ - INFO - 📋 客户端可用方法: ['F10', 'F10C', 'bars', 'bestip', 'block', 'client', 'close', 'closed', 'finance', 'get_k_data', 'index', 'index_bars', 'k', 'minute', 'minutes', 'ohlc', 'pool', 'quotes', 'reconnect', 'server', 'stock_all', 'stock_count', 'stocks', 'timeout', 'traffic', 'transaction', 'transactions', 'verbose', 'xdxr']
2025-07-09 17:24:53,855 - __main__ - INFO - 📊 可能的K线相关方法: ['bars', 'block', 'get_k_data', 'index_bars', 'k', 'stock_all', 'stock_count', 'stocks']
2025-07-09 17:24:53,855 - __main__ - INFO - 🏁 测试完成
2025-07-09 17:26:11,105 - __main__ - INFO - 🎯 开始测试 client.bars 方法参数
2025-07-09 17:26:11,105 - __main__ - INFO - 🚀 开始测试 client.bars 方法的参数
2025-07-09 17:26:11,226 - __main__ - INFO - 📊 测试股票: 000001, 频率: 0 (5分钟)
2025-07-09 17:26:11,226 - __main__ - INFO - 
🔍 测试1: 传统方式 - client.bars(symbol, frequency, offset)
2025-07-09 17:26:11,272 - __main__ - INFO - ✅ 传统方式成功: 800 条数据
2025-07-09 17:26:11,272 - __main__ - INFO -    时间范围: 2025-06-17 10:55:00 到 2025-07-09 15:00:00
2025-07-09 17:26:11,273 - __main__ - INFO - 
🔍 测试2: 尝试使用length参数 - client.bars(symbol, frequency, offset=0, length=800)
2025-07-09 17:26:11,297 - __main__ - WARNING - ⚠️ length参数返回空数据
2025-07-09 17:26:11,297 - __main__ - INFO - 
🔍 测试3: 尝试offset作为起始位置 - client.bars(symbol, frequency, offset=801, length=800)
2025-07-09 17:26:11,341 - __main__ - INFO - ✅ offset起始位置成功: 800 条数据
2025-07-09 17:26:11,341 - __main__ - INFO -    时间范围: 2025-06-17 10:55:00 到 2025-07-09 15:00:00
2025-07-09 17:26:11,341 - __main__ - INFO - 
🔍 测试4: 对比不同offset值的数据
2025-07-09 17:26:11,409 - __main__ - INFO - 
🔍 测试bars方法的方法签名
2025-07-09 17:26:11,438 - __main__ - INFO - 📋 bars方法签名: (symbol='000001', frequency=9, start=0, offset=800, **kwargs)
2025-07-09 17:26:11,438 - __main__ - INFO -    参数: symbol, 默认值: 000001
2025-07-09 17:26:11,438 - __main__ - INFO -    参数: frequency, 默认值: 9
2025-07-09 17:26:11,438 - __main__ - INFO -    参数: start, 默认值: 0
2025-07-09 17:26:11,439 - __main__ - INFO -    参数: offset, 默认值: 800
2025-07-09 17:26:11,439 - __main__ - INFO -    参数: kwargs, 默认值: 无默认值
2025-07-09 17:26:11,439 - __main__ - INFO - 
🔍 测试其他可能的数据获取方法
2025-07-09 17:26:11,467 - __main__ - INFO - 📋 客户端可用方法: ['F10', 'F10C', 'bars', 'bestip', 'block', 'client', 'close', 'closed', 'finance', 'get_k_data', 'index', 'index_bars', 'k', 'minute', 'minutes', 'ohlc', 'pool', 'quotes', 'reconnect', 'server', 'stock_all', 'stock_count', 'stocks', 'timeout', 'traffic', 'transaction', 'transactions', 'verbose', 'xdxr']
2025-07-09 17:26:11,467 - __main__ - INFO - 📊 可能的K线相关方法: ['bars', 'block', 'get_k_data', 'index_bars', 'k', 'stock_all', 'stock_count', 'stocks']
2025-07-09 17:26:11,467 - __main__ - INFO - 🏁 测试完成
2025-07-09 17:38:00,028 - __main__ - INFO - 🎯 开始测试get_security_bars直接调用
2025-07-09 17:38:00,028 - __main__ - INFO - 🚀 开始测试直接调用get_security_bars
2025-07-09 17:38:00,297 - __main__ - INFO - 📊 测试股票: 000001, 频率: 0 (5分钟), 市场: 0
2025-07-09 17:38:00,297 - __main__ - INFO - 
🔍 测试1: 获取第0-800条数据
2025-07-09 17:38:00,342 - __main__ - INFO - ✅ 第0-800条数据获取成功: 800 条
2025-07-09 17:38:00,344 - __main__ - INFO -    时间范围: 2025-06-17 10:55:00 到 2025-07-09 15:00:00
2025-07-09 17:38:00,344 - __main__ - INFO - 
🔍 测试2: 获取第800-1600条数据
2025-07-09 17:38:00,383 - __main__ - INFO - ✅ 第800-1600条数据获取成功: 800 条
2025-07-09 17:38:00,384 - __main__ - INFO -    时间范围: 2025-05-22 13:45:00 到 2025-06-17 10:50:00
2025-07-09 17:38:00,384 - __main__ - INFO - 
🔍 测试3: 检查数据重叠
2025-07-09 17:38:00,384 - __main__ - INFO - 📊 第0-800条时间范围: 2025-06-17 10:55:00 到 2025-07-09 15:00:00
2025-07-09 17:38:00,384 - __main__ - INFO - 📊 第800-1600条时间范围: 2025-05-22 13:45:00 到 2025-06-17 10:50:00
2025-07-09 17:38:00,385 - __main__ - INFO - ✅ 数据无重叠，第800-1600条是更早的历史数据
2025-07-09 17:38:00,386 - __main__ - INFO - 📊 合并后数据: 1600 条（去重后）
2025-07-09 17:38:00,387 - __main__ - INFO - ✅ 无重复数据，偏移量策略成功！
2025-07-09 17:38:00,432 - __main__ - INFO - 
🚀 开始测试新的偏移量策略
2025-07-09 17:38:00,545 - __main__ - INFO - 📊 测试股票: 000004, 周期: 5分钟
2025-07-09 17:38:00,545 - __main__ - INFO - 📈 获取强制下载数据（新的偏移量策略）...
2025-07-09 17:38:00,708 - __main__ - INFO - ✅ 获取数据成功: 2000 条
2025-07-09 17:38:00,708 - __main__ - INFO - 📊 测试数据库插入...
2025-07-09 17:38:00,817 - __main__ - INFO - 🧹 已清空股票 000004 的测试数据
2025-07-09 17:38:00,820 - __main__ - ERROR - ❌ 测试失败: 错误:  NULL value in column "trade_time" violates not-null constraint
HINT:  Columns used for time partitioning cannot be NULL.

2025-07-09 17:38:00,821 - __main__ - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Program/stock_data/test_get_security_bars.py", line 194, in test_new_offset_strategy
    inserted_count = db.batch_insert_df(
        table_name,
    ...<2 lines>...
        enable_dedup=True
    )
  File "/home/<USER>/Program/stock_data/data/timescaledb_manager.py", line 201, in batch_insert_df
    cursor.executemany(sql, data_tuples)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
psycopg2.errors.NotNullViolation: 错误:  NULL value in column "trade_time" violates not-null constraint
HINT:  Columns used for time partitioning cannot be NULL.


2025-07-09 17:38:00,867 - __main__ - INFO - 🏁 测试完成
2025-07-09 18:25:55,966 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 18:25:55,987 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 18:25:55,989 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 18:25:55,989 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 18:26:02,662 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 18:26:02,662 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 18:33:44,138 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 18:33:44,158 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 18:33:44,160 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 18:33:44,160 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 18:34:05,085 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 18:34:05,085 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 18:47:22,033 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 18:47:22,055 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 18:47:22,058 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 18:47:22,058 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 18:47:37,319 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 18:47:37,319 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 19:00:35,120 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 19:00:35,141 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 19:00:35,143 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 19:00:35,143 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 19:00:42,181 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 19:00:42,182 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 19:10:15,747 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 19:10:15,769 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 19:10:15,771 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 19:10:15,771 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 19:10:20,607 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 19:10:20,607 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 19:26:38,584 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 19:26:38,606 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 19:26:38,608 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 19:26:38,608 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 19:26:47,469 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 19:26:47,469 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 21:05:40,827 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 21:05:40,849 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 21:05:40,852 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 21:05:40,853 - __main__ - INFO - ⏰ 优化版定时任务设置完成
2025-07-09 21:05:40,853 - __main__ - INFO - 📅 任务调度:
2025-07-09 21:05:40,853 - __main__ - INFO -    - 每日15:30: 增量数据更新（日线+分钟线+指数+概念）
2025-07-09 21:05:40,853 - __main__ - INFO -    - 每周三02:00: 批量数据更新（概念全量+股票信息）
2025-07-09 21:05:40,853 - __main__ - INFO -    - 每日01:00: 交易日历刷新
2025-07-09 21:05:40,853 - __main__ - INFO -    - 每4小时: 新闻数据采集
2025-07-09 21:05:40,853 - __main__ - INFO - 🔄 进入主循环
2025-07-09 21:06:01,054 - __main__ - INFO - 📅 今天 20250709 是交易日，开始每日增量数据更新
2025-07-09 21:10:03,714 - __main__ - INFO - ⏹️ 收到停止信号
2025-07-09 21:10:03,721 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 21:12:06,304 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 21:12:06,326 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 21:12:06,328 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 21:12:06,328 - __main__ - INFO - ⏰ 优化版定时任务设置完成
2025-07-09 21:12:06,328 - __main__ - INFO - 📅 任务调度:
2025-07-09 21:12:06,328 - __main__ - INFO -    - 每日15:30: 增量数据更新（日线+分钟线+指数+概念）
2025-07-09 21:12:06,328 - __main__ - INFO -    - 每周三02:00: 批量数据更新（概念全量+股票信息）
2025-07-09 21:12:06,328 - __main__ - INFO -    - 每日01:00: 交易日历刷新
2025-07-09 21:12:06,328 - __main__ - INFO -    - 每4小时: 新闻数据采集
2025-07-09 21:12:06,329 - __main__ - INFO - 🔄 进入主循环
2025-07-09 21:14:28,340 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 21:14:28,361 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 21:14:28,363 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 21:14:28,364 - __main__ - INFO - ⏰ 优化版定时任务设置完成
2025-07-09 21:14:28,364 - __main__ - INFO - 📅 任务调度:
2025-07-09 21:14:28,364 - __main__ - INFO -    - 每日15:30: 增量数据更新（日线+分钟线+指数+概念）
2025-07-09 21:14:28,364 - __main__ - INFO -    - 每周三02:00: 批量数据更新（概念全量+股票信息）
2025-07-09 21:14:28,364 - __main__ - INFO -    - 每日01:00: 交易日历刷新
2025-07-09 21:14:28,364 - __main__ - INFO -    - 每4小时: 新闻数据采集
2025-07-09 21:14:28,364 - __main__ - INFO - 🔄 进入主循环
2025-07-09 21:14:29,340 - __main__ - INFO - ⏹️ 收到停止信号
2025-07-09 21:14:29,341 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 21:14:59,480 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 21:14:59,501 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 21:14:59,503 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 21:14:59,503 - __main__ - INFO - ⏰ 优化版定时任务设置完成
2025-07-09 21:14:59,503 - __main__ - INFO - 📅 任务调度:
2025-07-09 21:14:59,503 - __main__ - INFO -    - 每日15:30: 增量数据更新（日线+分钟线+指数+概念）
2025-07-09 21:14:59,503 - __main__ - INFO -    - 每周三02:00: 批量数据更新（概念全量+股票信息）
2025-07-09 21:14:59,503 - __main__ - INFO -    - 每日01:00: 交易日历刷新
2025-07-09 21:14:59,504 - __main__ - INFO -    - 每4小时: 新闻数据采集
2025-07-09 21:14:59,504 - __main__ - INFO - 🔄 进入主循环
2025-07-09 21:15:00,736 - __main__ - INFO - 📅 今天 20250709 是交易日，开始每日增量数据更新
2025-07-09 21:15:34,652 - __main__ - INFO - ⏹️ 收到停止信号
2025-07-09 21:15:34,657 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 21:18:24,551 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 21:18:24,572 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 21:18:24,574 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 21:18:24,574 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 21:19:17,036 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 21:19:17,036 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 21:34:15,849 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 21:34:15,870 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 21:34:15,872 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 21:34:15,872 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 21:34:42,097 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 21:34:42,097 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 21:37:43,568 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 21:37:43,591 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 21:37:43,594 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 21:37:43,594 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 21:37:52,735 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 21:37:52,736 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 21:38:03,699 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 21:38:03,721 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 21:38:03,723 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 21:38:03,723 - __main__ - INFO - 🔄 强制执行数据采集
2025-07-09 21:38:12,897 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 21:38:12,898 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 21:39:39,008 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 21:39:39,030 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 21:39:39,032 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 21:39:39,032 - __main__ - INFO - 🚀 开始强制下载股票数据（2000条历史数据）
2025-07-09 21:39:39,032 - __main__ - ERROR - ❌ 强制下载股票数据失败: 'MarketDataFetcher' object has no attribute 'login_baostock'
2025-07-09 21:39:39,032 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 21:39:39,032 - __main__ - INFO - ✅ 强制下载股票数据任务完成
2025-07-09 21:41:34,010 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 21:41:34,031 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 21:41:34,033 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 21:41:34,033 - __main__ - INFO - 🚀 开始强制下载股票数据（2000条历史数据）
2025-07-09 21:41:43,537 - __main__ - INFO - ⏹️ 程序被用户终止
2025-07-09 21:41:59,765 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 21:41:59,786 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 21:41:59,788 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 21:41:59,789 - __main__ - INFO - 🚀 开始强制下载股票数据（2000条历史数据）
2025-07-09 21:43:36,103 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 21:43:36,124 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 21:43:36,126 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 21:43:36,126 - __main__ - INFO - 🚀 开始强制下载股票数据（2000条历史数据）
2025-07-09 21:58:03,604 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-07-09 21:58:03,626 - __main__ - INFO - ✅ 数据库连接成功
2025-07-09 21:58:03,628 - __main__ - INFO - ✅ 系统组件初始化完成
2025-07-09 21:58:03,628 - __main__ - INFO - ⏰ 优化版定时任务设置完成
2025-07-09 21:58:03,628 - __main__ - INFO - 📅 任务调度:
2025-07-09 21:58:03,628 - __main__ - INFO -    - 每日15:30: 增量数据更新（日线+分钟线+指数+概念）
2025-07-09 21:58:03,628 - __main__ - INFO -    - 每周三02:00: 批量数据更新（概念全量+股票信息）
2025-07-09 21:58:03,628 - __main__ - INFO -    - 每日01:00: 交易日历刷新
2025-07-09 21:58:03,629 - __main__ - INFO -    - 每4小时: 新闻数据采集
2025-07-09 21:58:03,629 - __main__ - INFO - 🔄 进入主循环
2025-07-09 23:59:25,787 - __main__ - INFO - ✅ 强制下载股票数据完成
2025-07-09 23:59:25,788 - __main__ - INFO - ✅ 数据库连接已关闭
2025-07-09 23:59:25,788 - __main__ - INFO - ✅ 强制下载股票数据任务完成
2025-07-10 01:58:04,089 - __main__ - INFO - 📰 开始采集新闻数据
2025-07-10 01:58:04,132 - __main__ - ERROR - ❌ 新闻数据采集失败: 'MarketDataFetcher' object has no attribute 'collect_cx_news'
2025-07-10 05:58:04,369 - __main__ - INFO - 📰 开始采集新闻数据
2025-07-10 05:58:04,412 - __main__ - ERROR - ❌ 新闻数据采集失败: 'MarketDataFetcher' object has no attribute 'collect_cx_news'
