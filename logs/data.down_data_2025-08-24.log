2025-08-24 12:39:45,966 - data.down_data - INFO - 初始化市场数据采集器 - 使用TimescaleDB
2025-08-24 12:39:45,967 - data.down_data - INFO - 🚀 开始强制下载历史数据任务（自定义时间范围）...
2025-08-24 12:39:45,967 - data.down_data - INFO - 📅 日线数据时间范围: 2015-01-01 到 2025-08-24
2025-08-24 12:39:45,967 - data.down_data - INFO - 📅 分钟数据时间范围: 2025-01-01 到 2025-08-24
2025-08-24 12:39:45,967 - data.down_data - INFO - 📊 下载内容: 指定时间范围的历史数据（日线+分钟线+评分）
2025-08-24 12:39:45,967 - data.down_data - INFO - 💡 数据源策略: 强制使用adata获取前复权数据
2025-08-24 12:39:45,967 - data.down_data - INFO - 📈 开始强制下载股票历史数据（自定义时间范围版本）...
2025-08-24 12:39:45,967 - data.down_data - INFO - 💡 包含内容：
2025-08-24 12:39:45,967 - data.down_data - INFO -    - 日线数据：2015-01-01 到 2025-08-24
2025-08-24 12:39:45,967 - data.down_data - INFO -    - 5分钟K线：2025-01-01 到 2025-08-24
2025-08-24 12:39:45,967 - data.down_data - INFO -    - 15分钟K线：2025-01-01 到 2025-08-24
2025-08-24 12:39:45,967 - data.down_data - INFO -    - 股票评分：1条记录/股票
2025-08-24 12:39:45,967 - data.down_data - INFO - 💡 优势：按股票代码单循环执行，避免多次循环，提高效率
2025-08-24 15:19:12,338 - data.down_data - INFO - ✅ 强制下载股票历史数据完成（自定义时间范围版本）
2025-08-24 15:19:12,338 - data.down_data - INFO - ✅ 强制下载历史数据任务完成（自定义时间范围）
