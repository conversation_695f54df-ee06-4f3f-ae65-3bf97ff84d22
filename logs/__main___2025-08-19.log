2025-08-19 18:44:09,268 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-08-19 18:44:09,284 - __main__ - INFO - ✅ 数据库连接成功
2025-08-19 18:44:09,286 - __main__ - INFO - ✅ 系统组件初始化完成
2025-08-19 18:44:09,286 - __main__ - INFO - ⏰ 优化版定时任务设置完成
2025-08-19 18:44:09,286 - __main__ - INFO - 📅 任务调度:
2025-08-19 18:44:09,286 - __main__ - INFO -    - 每日15:30: 增量数据更新（日线+分钟线+指数+概念）
2025-08-19 18:44:09,286 - __main__ - INFO -    - 每周三02:00: 批量数据更新（概念全量+股票信息）
2025-08-19 18:44:09,286 - __main__ - INFO -    - 每日01:00: 交易日历刷新
2025-08-19 18:44:09,286 - __main__ - INFO -    - 每4小时: 新闻数据采集
2025-08-19 18:44:09,286 - __main__ - INFO - 🔄 进入主循环
2025-08-19 18:44:11,184 - __main__ - INFO - ⏹️ 收到停止信号
2025-08-19 18:44:11,185 - __main__ - INFO - ✅ 数据库连接已关闭
2025-08-19 18:44:20,775 - __main__ - INFO - 🚀 股票数据采集系统启动
2025-08-19 18:44:20,791 - __main__ - INFO - ✅ 数据库连接成功
2025-08-19 18:44:20,794 - __main__ - INFO - ✅ 系统组件初始化完成
2025-08-19 18:44:20,794 - __main__ - INFO - 🔄 强制下载模式：执行强制下载股票数据任务
2025-08-19 18:44:20,794 - __main__ - INFO - 🚀 开始强制下载股票数据（指定时间范围的历史数据）
2025-08-19 18:44:20,794 - __main__ - INFO - 📅 数据时间范围配置:
2025-08-19 18:44:20,794 - __main__ - INFO -    - 日线数据: 2015-01-01 到 2025-08-19
2025-08-19 18:44:20,794 - __main__ - INFO -    - 分钟数据: 2025-01-01 到 2025-08-19
2025-08-19 18:44:20,794 - __main__ - INFO - 💡 数据源策略: 强制使用adata获取前复权数据
2025-08-19 18:45:31,046 - __main__ - INFO - ✅ 数据库连接已关闭
2025-08-19 18:45:31,047 - __main__ - INFO - ⏹️ 程序被用户终止
