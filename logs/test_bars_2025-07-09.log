2025-07-09 17:28:09,201 - test_bars - INFO - 🚀 开始测试 client.bars 方法的参数
2025-07-09 17:28:09,308 - test_bars - INFO - 📊 测试股票: 000001, 频率: 0 (5分钟)
2025-07-09 17:28:09,308 - test_bars - INFO - 🔍 测试1: 传统方式 - client.bars(symbol, frequency, offset)
2025-07-09 17:28:09,350 - test_bars - INFO - ✅ 传统方式成功: 800 条数据
2025-07-09 17:28:09,350 - test_bars - INFO -    时间范围: 2025-06-17 10:55:00 到 2025-07-09 15:00:00
2025-07-09 17:28:09,350 - test_bars - INFO - 🔍 测试2: 尝试使用length参数 - client.bars(symbol, frequency, offset=0, length=800)
2025-07-09 17:28:09,372 - test_bars - WARNING - ⚠️ length参数返回空数据
2025-07-09 17:28:09,373 - test_bars - INFO - 🏁 测试完成
