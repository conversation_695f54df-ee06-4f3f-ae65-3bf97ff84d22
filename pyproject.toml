[tool.pylance]
# Pylance 类型检查配置
typeCheckingMode = "basic"
reportMissingImports = "warning"
reportMissingTypeStubs = "none"
reportUnknownParameterType = "none"
reportUnknownArgumentType = "none"
reportUnknownLambdaType = "none"
reportUnknownVariableType = "none"
reportUnknownMemberType = "none"
reportMissingTypeArgument = "warning"
reportInvalidTypeVarUse = "warning"
reportCallInDefaultInitializer = "none"
reportUnnecessaryIsInstance = "none"
reportUnnecessaryComparison = "none"
reportConstantRedefinition = "none"
reportDuplicateImport = "warning"
reportUnusedImport = "warning"
reportUnusedClass = "none"
reportUnusedFunction = "none"
reportUnusedVariable = "none"
reportUntypedFunctionDecorator = "none"
reportUntypedClassDecorator = "none"
reportUntypedBaseClass = "none"
reportUntypedNamedTuple = "none"
reportPrivateUsage = "none"
reportPrivateImportUsage = "none"
reportInconsistentConstructor = "none"
reportOverlappingOverload = "warning"
reportMissingSuperCall = "none"
reportPropertyTypeMismatch = "none"
reportFunctionMemberAccess = "none"
reportIncompatibleMethodOverride = "warning"
reportIncompatibleVariableOverride = "warning"
reportInvalidStringEscapeSequence = "warning"
reportUnknownParameterType = "none"
reportMissingParameterType = "none"
reportInvalidTypeVarUse = "warning"
reportCallInDefaultInitializer = "none"
reportUnnecessaryIsInstance = "none"
reportUnnecessaryComparison = "none"
reportUnnecessaryCast = "none"
reportAssertAlwaysTrue = "warning"
reportSelfClsParameterName = "warning"
reportImplicitStringConcatenation = "none"
reportUndefinedVariable = "error"
reportUnboundVariable = "error"
reportInvalidStubStatement = "none"
reportIncompleteStub = "none"
reportUnsupportedDunderAll = "warning"
reportUnusedCoroutine = "warning"

# 忽略特定的第三方库
stubPath = "typings"
exclude = [
    "**/__pycache__",
    "**/node_modules",
    "**/.venv",
    "**/venv",
    "logs/**",
    "docs/**"
]

[tool.mypy]
# MyPy 配置（如果使用）
python_version = "3.8"
warn_return_any = false
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = false
disallow_untyped_decorators = false
no_implicit_optional = false
warn_redundant_casts = true
warn_unused_ignores = false
warn_no_return = true
warn_unreachable = true
strict_equality = true
ignore_missing_imports = true

# 忽略特定模块
[[tool.mypy.overrides]]
module = [
    "adata.*",
    "akshare.*", 
    "mootdx.*",
    "tushare.*",
    "baostock.*",
    "pandas.*",
    "numpy.*"
]
ignore_missing_imports = true
ignore_errors = true

[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "stock-data"
version = "1.0.0"
description = "Stock data collection and analysis system"
authors = [{name = "Stock Data Team"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "pandas>=1.3.0",
    "numpy>=1.21.0",
    "psycopg2-binary>=2.9.0",
    "schedule>=1.1.0",
    "tqdm>=4.62.0",
    "python-dotenv>=0.19.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "black>=21.0",
    "flake8>=3.9",
    "mypy>=0.910"
]
