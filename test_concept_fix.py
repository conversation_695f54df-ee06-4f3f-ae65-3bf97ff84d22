#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试概念数据时间处理修复
"""

import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.logger import setup_logger
from data.database_manager import DatabaseManager
from data.market_fetcher import MarketDataFetcher

logger = setup_logger(__name__)

def test_concept_time_processing():
    """测试概念数据时间处理"""
    try:
        logger.info("🧪 开始测试概念数据时间处理修复...")

        # 初始化数据库和fetcher
        db = DatabaseManager()
        if not db.connect():
            logger.error("❌ 数据库连接失败")
            return False

        fetcher = MarketDataFetcher(db)

        # 获取测试用的概念代码
        concept_codes = db.execute_query("SELECT DISTINCT index_code FROM concept_info LIMIT 5")
        if not concept_codes:
            logger.warning("⚠️ 数据库中没有概念代码，跳过测试")
            return True

        today = pd.Timestamp.now().strftime('%Y-%m-%d')
        success_count = 0
        total_tests = 0

        for concept_code_row in concept_codes:
            test_index_code = concept_code_row[0]
            logger.info(f"🎯 测试概念代码: {test_index_code}")

            # 测试概念日线数据处理
            logger.info("📊 测试概念日线数据处理...")
            total_tests += 1
            try:
                fetcher.get_concept_ths_daily_data_incremental(test_index_code, today, today)
                logger.info(f"✅ 概念 {test_index_code} 日线数据处理测试通过")
                success_count += 1
            except Exception as e:
                logger.error(f"❌ 概念 {test_index_code} 日线数据处理测试失败: {e}")
                import traceback
                logger.debug(traceback.format_exc())

            # 测试概念分钟数据处理
            logger.info("📊 测试概念分钟数据处理...")
            total_tests += 1
            try:
                fetcher.collect_concept_min_data_incremental(test_index_code, today, today)
                logger.info(f"✅ 概念 {test_index_code} 分钟数据处理测试通过")
                success_count += 1
            except Exception as e:
                logger.error(f"❌ 概念 {test_index_code} 分钟数据处理测试失败: {e}")
                import traceback
                logger.debug(traceback.format_exc())

            # 只测试前两个概念，避免测试时间过长
            if len(concept_codes) > 2:
                break

        logger.info(f"🎉 概念数据时间处理修复测试完成: {success_count}/{total_tests} 通过")
        return success_count > 0  # 至少有一个测试通过就算成功

    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return False
    finally:
        if 'db' in locals():
            db.disconnect()

def test_edge_cases():
    """测试边界情况"""
    logger.info("🧪 测试边界情况...")
    
    # 测试空DataFrame的时间处理
    empty_df = pd.DataFrame()
    try:
        # 这应该不会抛出异常
        if not empty_df.empty:
            empty_df.loc[:, 'trade_time'] = pd.to_datetime(empty_df['trade_time'], errors='coerce')
        logger.info("✅ 空DataFrame处理测试通过")
    except Exception as e:
        logger.error(f"❌ 空DataFrame处理测试失败: {e}")
    
    # 测试包含无效时间数据的DataFrame
    invalid_time_df = pd.DataFrame({
        'trade_time': ['invalid_date', None, '2025-01-01', 'another_invalid'],
        'close': [100, 101, 102, 103]
    })
    
    try:
        invalid_time_df.loc[:, 'trade_time'] = pd.to_datetime(invalid_time_df['trade_time'], errors='coerce')
        
        # 检查转换结果
        if invalid_time_df['trade_time'].isna().all():
            logger.info("✅ 全部无效时间数据检测测试通过")
        else:
            # 删除无效时间的行
            valid_df = invalid_time_df.dropna(subset=['trade_time'])
            if not valid_df.empty and hasattr(valid_df['trade_time'], 'dt'):
                # 只有在有有效数据时才使用.dt访问器
                valid_df.loc[:, 'trade_time'] = valid_df['trade_time'].dt.normalize()
                logger.info("✅ 部分有效时间数据处理测试通过")
            
    except Exception as e:
        logger.error(f"❌ 无效时间数据处理测试失败: {e}")

if __name__ == "__main__":
    logger.info("🚀 开始概念数据修复测试...")
    
    # 测试边界情况
    test_edge_cases()
    
    # 测试实际数据处理
    success = test_concept_time_processing()
    
    if success:
        logger.info("🎉 所有测试通过！概念数据时间处理修复成功")
    else:
        logger.error("❌ 测试失败，需要进一步检查")
