#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细调试概念数据问题
"""

import pandas as pd
import sys
import os
import adata as ad

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.logger import setup_logger

logger = setup_logger(__name__)

def debug_datetime_conversion():
    """调试时间转换问题"""
    try:
        logger.info("🔍 开始调试时间转换问题...")
        
        test_index_code = "885311"
        logger.info(f"📊 获取概念 {test_index_code} 的原始数据...")
        
        # 获取日线数据
        con_daily_data = ad.stock.market.get_market_concept_ths(index_code=test_index_code)
        
        if con_daily_data is None or con_daily_data.empty:
            logger.error("❌ 概念日线数据为空")
            return
            
        logger.info(f"📊 原始数据形状: {con_daily_data.shape}")
        
        # 检查 trade_time 列的详细信息
        if 'trade_time' in con_daily_data.columns:
            logger.info(f"📊 trade_time 列类型: {con_daily_data['trade_time'].dtype}")
            logger.info(f"📊 trade_time 前10个值:")
            for i, val in enumerate(con_daily_data['trade_time'].head(10)):
                logger.info(f"   [{i}]: '{val}' (类型: {type(val)})")
                
            # 模拟我们的转换过程
            logger.info("🔄 模拟转换过程...")
            
            # 步骤1：复制数据
            test_df = con_daily_data.copy()
            logger.info(f"📊 复制后 trade_time 类型: {test_df['trade_time'].dtype}")
            
            # 步骤2：尝试转换
            logger.info("🔄 尝试 pd.to_datetime 转换...")
            try:
                test_df.loc[:, 'trade_time'] = pd.to_datetime(test_df['trade_time'], errors='coerce')
                logger.info(f"✅ 转换后类型: {test_df['trade_time'].dtype}")
                logger.info(f"📊 转换后前5个值:")
                for i, val in enumerate(test_df['trade_time'].head(5)):
                    logger.info(f"   [{i}]: {val} (类型: {type(val)})")
                    
                # 检查是否有 NaT
                nat_count = test_df['trade_time'].isna().sum()
                logger.info(f"📊 NaT 值数量: {nat_count}/{len(test_df)}")
                
                # 检查类型验证
                is_datetime = pd.api.types.is_datetime64_any_dtype(test_df['trade_time'])
                logger.info(f"📊 is_datetime64_any_dtype: {is_datetime}")
                
                if is_datetime:
                    logger.info("✅ 类型验证通过，尝试 .dt.normalize()")
                    normalized = test_df['trade_time'].dt.normalize()
                    logger.info(f"✅ normalize 成功，类型: {normalized.dtype}")
                else:
                    logger.error(f"❌ 类型验证失败，实际类型: {test_df['trade_time'].dtype}")
                    
                    # 尝试强制转换
                    logger.info("🔄 尝试强制转换...")
                    forced_conversion = pd.to_datetime(test_df['trade_time'].astype(str), errors='coerce')
                    logger.info(f"📊 强制转换后类型: {forced_conversion.dtype}")
                    
                    is_datetime_forced = pd.api.types.is_datetime64_any_dtype(forced_conversion)
                    logger.info(f"📊 强制转换后 is_datetime64_any_dtype: {is_datetime_forced}")
                    
            except Exception as convert_error:
                logger.error(f"❌ 转换失败: {convert_error}")
                import traceback
                logger.error(traceback.format_exc())
                
        # 测试分钟数据的数值转换
        logger.info("📊 测试分钟数据...")
        try:
            con_min_data = ad.stock.market.get_market_concept_min_ths(test_index_code)
            
            if con_min_data is not None and not con_min_data.empty:
                logger.info(f"📊 分钟数据形状: {con_min_data.shape}")
                
                # 测试数值转换
                for col in ['volume', 'amount', 'price']:
                    if col in con_min_data.columns:
                        logger.info(f"📊 {col} 原始类型: {con_min_data[col].dtype}")
                        logger.info(f"📊 {col} 前3个值: {list(con_min_data[col].head(3))}")
                        
                        # 尝试数值转换
                        try:
                            numeric_col = pd.to_numeric(con_min_data[col], errors='coerce')
                            logger.info(f"✅ {col} 转换成功，类型: {numeric_col.dtype}")
                            
                            # 测试聚合操作
                            test_sum = numeric_col.sum()
                            logger.info(f"✅ {col} 聚合测试成功: {test_sum}")
                            
                        except Exception as numeric_error:
                            logger.error(f"❌ {col} 数值转换失败: {numeric_error}")
                            
        except Exception as min_error:
            logger.error(f"❌ 分钟数据测试失败: {min_error}")
            
    except Exception as e:
        logger.error(f"❌ 调试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    debug_datetime_conversion()
