#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据采集系统主程序
高效的股票数据采集和管理解决方案

功能特性:
- 自动化数据采集
- 智能任务调度
- 数据库连接管理
- 异常处理和恢复

作者: Xzh
版本: 2.0.0
"""

import time
import argparse
import schedule
import threading
from datetime import datetime, timedelta
from typing import Optional

from utils.logger import setup_logger
from utils.trade_calendar import trade_time_manager
from utils.warning_manager import setup_warnings_for_environment
from data.database_manager import DatabaseManager
from data.market_fetcher import MarketDataFetcher
from data.down_data import DownData

logger = setup_logger(__name__)


class StockDataApp:
    """股票数据采集应用程序"""

    def __init__(self):
        """初始化应用程序"""
        self.db: Optional[DatabaseManager] = None
        self.fetcher: Optional[MarketDataFetcher] = None
        self.down_data: Optional[DownData] = None
        self.running = False

        logger.info("🚀 股票数据采集系统启动")

    def initialize_database(self) -> bool:
        """初始化数据库连接"""
        try:
            self.db = DatabaseManager()
            if self.db.connect():
                logger.info("✅ 数据库连接成功")
                return True
            else:
                logger.error("❌ 数据库连接失败")
                return False
        except Exception as e:
            logger.error(f"❌ 数据库初始化异常: {e}")
            return False

    def initialize_components(self):
        """初始化系统组件"""
        if self.db is None:
            raise RuntimeError("数据库未初始化，请先调用 initialize_database()")

        try:
            self.fetcher = MarketDataFetcher(self.db)
            self.down_data = DownData(self.db)
            logger.info("✅ 系统组件初始化完成")
        except Exception as e:
            logger.error(f"❌ 组件初始化失败: {e}")
            raise

    def collect_daily_data(self, start_date: Optional[str] = None, end_date: Optional[str] = None):
        """采集日线数据"""
        if self.fetcher is None:
            raise RuntimeError("MarketDataFetcher 未初始化")

        try:
            if start_date is None:
                yesterday = datetime.now() - timedelta(days=1)
                yesterday_date = yesterday.strftime("%Y-%m-%d")
                start_date = yesterday_date
            if end_date is None:
                end_date = datetime.now().strftime('%Y-%m-%d')

            logger.info(f"📈 开始采集日线数据: {start_date} - {end_date}")

            with self.fetcher:
                self.fetcher.collect_stock_data(start_date, end_date)

            logger.info("✅ 日线数据采集完成")

        except Exception as e:
            logger.error(f"❌ 日线数据采集失败: {e}")
            raise

    def collect_minute_data(self, start_date: Optional[str] = None, end_date: Optional[str] = None, force_download: bool = False):
        """采集分钟数据"""
        if self.fetcher is None:
            raise RuntimeError("MarketDataFetcher 未初始化")

        try:
            if start_date is None:
                start_date = datetime.now().strftime('%Y-%m-%d')
            if end_date is None:
                end_date = datetime.now().strftime('%Y-%m-%d')

            mode_desc = "强制下载模式（2000条）" if force_download else "默认模式（240条）"
            logger.info(f"📊 开始采集分钟数据: {start_date} - {end_date} ({mode_desc})")

            with self.fetcher:
                self.fetcher.collect_minute_data(start_date, end_date, force_download=force_download)

            logger.info("✅ 分钟数据采集完成")

        except Exception as e:
            logger.error(f"❌ 分钟数据采集失败: {e}")

    def collect_concept_data(self):
        """采集概念数据"""
        if self.fetcher is None:
            raise RuntimeError("MarketDataFetcher 未初始化")

        try:
            logger.info("🏷️ 开始采集概念数据")

            with self.fetcher:
                self.fetcher.collect_concept_data()

            logger.info("✅ 概念数据采集完成")

        except Exception as e:
            logger.error(f"❌ 概念数据采集失败: {e}")

    def collect_realtime_data(self):
        """采集实时数据"""
        if self.fetcher is None or self.db is None:
            raise RuntimeError("MarketDataFetcher 或 DatabaseManager 未初始化")

        try:
            logger.info("📊 开始采集实时数据")

            with self.fetcher:
                tick_data = self.fetcher.get_stock_tick_data()
                if not tick_data.empty:
                    self.db.batch_insert_df(
                        'stock_tick_data',
                        tick_data,
                        conflict_fields=['stock_code', 'trade_time'],
                        enable_dedup=True
                    )
                    logger.info(f"✅ 实时数据采集完成: {len(tick_data)} 条记录")
                else:
                    logger.warning("⚠️ 实时数据为空")

        except Exception as e:
            logger.error(f"❌ 实时数据采集失败: {e}")

    def collect_news_data(self):
        """采集新闻数据"""
        if self.down_data is None:
            raise RuntimeError("DownData 未初始化")

        try:
            logger.info("📰 开始采集新闻数据")
            self.down_data.collect_news()
            logger.info("✅ 新闻数据采集完成")
        except Exception as e:
            logger.error(f"❌ 新闻数据采集失败: {e}")

    def daily_incremental_task(self):
        """
        每日15:30增量数据更新任务 - 优化版本

        根据用户需求：
        - 当天日线数据（1条记录）
        - 当天5分钟K线数据（约48条记录）
        - 当天15分钟K线数据（约16条记录）
        - 指数的日线和周期K线数据
        - 概念相关数据（当天）
        """
        if self.down_data is None:
            raise RuntimeError("DownData 未初始化")

        # 检查当天是否是交易日
        today = datetime.now()
        today_date = today.strftime("%Y%m%d")

        if trade_time_manager.is_trade_date(today_date):
            logger.info(f"📅 今天 {today_date} 是交易日，开始每日增量数据更新")
            try:
                # 采集当天的数据
                today_str = today.strftime('%Y-%m-%d')
                self.down_data.collect_daily_schedule(today_str, today_str)

                logger.info("✅ 每日增量数据更新完成")
            except Exception as e:
                logger.error(f"❌ 每日增量数据更新失败: {e}")
        else:
            logger.info(f"📅 今天 {today_date} 不是交易日，跳过每日增量数据更新")

    def weekly_batch_task(self):
        """
        每周三批量数据更新任务 - 优化版本

        根据用户需求：
        - 概念相关的所有数据（全量更新）
        - 股票信息数据（基础信息更新）
        """
        if self.down_data is None:
            raise RuntimeError("DownData 未初始化")

        logger.info("📅 开始每周三批量数据更新")
        try:
            # 使用当前日期作为参数
            today_str = datetime.now().strftime('%Y-%m-%d')
            self.down_data.collect_weekly_schedule(today_str, today_str)

            logger.info("✅ 每周三批量数据更新完成")
        except Exception as e:
            logger.error(f"❌ 每周三批量数据更新失败: {e}")

    def force_stock_data_task(self):
        """
        强制下载股票数据任务 - 获取2000条历史数据

        使用强制下载模式获取大量历史数据：
        - 日线数据（2000条历史记录/股票）
        - 5分钟数据（2000条历史记录/股票）
        - 15分钟数据（2000条历史记录/股票）
        - 股票评分数据（1条记录/股票）
        """
        if self.down_data is None:
            raise RuntimeError("DownData 未初始化")

        logger.info("🚀 开始强制下载股票数据（2000条历史数据）")
        try:
            # 使用当前日期作为参数
            today_str = datetime.now().strftime('%Y-%m-%d')

            # 调用专门的强制下载方法
            self.down_data.collect_force_download_data(today_str, today_str)

            logger.info("✅ 强制下载股票数据完成")
        except Exception as e:
            logger.error(f"❌ 强制下载股票数据失败: {e}")

    def after_market_task(self):
        """
        盘后数据采集任务（保留兼容性）

        注意：此方法已被 daily_incremental_task 替代
        保留此方法是为了向后兼容
        """
        logger.warning("⚠️ after_market_task 已被 daily_incremental_task 替代")
        self.daily_incremental_task()

    def realtime_task(self):
        """实时数据采集任务"""
        if trade_time_manager.is_trading_time():
            self.collect_realtime_data()

    def setup_schedule(self):
        """
        设置定时任务 - 优化版本

        根据用户需求优化任务调度：
        - 每日15:30：日线+分钟线+指数+概念数据（增量更新）
        - 每周三：概念所有数据+股票信息（全量更新）
        - 其他：交易日历刷新、新闻数据等
        """
        # 每天凌晨1点刷新交易日历
        schedule.every().day.at("01:00").do(  # type: ignore
            trade_time_manager.refresh_trade_dates
        )

        # 每日15:30采集当天数据（优化版本）
        # 包含：日线(1条) + 5分钟(48条) + 15分钟(16条) + 指数 + 概念
        schedule.every().day.at("15:30").do(  # type: ignore
            lambda: threading.Thread(target=self.daily_incremental_task).start()
        )

        # 每周三进行批量更新
        # 包含：概念所有数据 + 股票信息
        schedule.every().wednesday.at("02:00").do(  # type: ignore
            lambda: threading.Thread(target=self.weekly_batch_task).start()
        )

        # 交易时间内每30秒采集实时数据（可选）
        # schedule.every(30).seconds.do(self.realtime_task)

        # 每4小时采集新闻数据
        schedule.every(4).hours.do(  # type: ignore
            lambda: threading.Thread(target=self.collect_news_data).start()
        )

        logger.info("⏰ 优化版定时任务设置完成")
        logger.info("📅 任务调度:")
        logger.info("   - 每日15:30: 增量数据更新（日线+分钟线+指数+概念）")
        logger.info("   - 每周三02:00: 批量数据更新（概念全量+股票信息）")
        logger.info("   - 每日01:00: 交易日历刷新")
        logger.info("   - 每4小时: 新闻数据采集")

    def health_check(self):
        """系统健康检查"""
        try:
            # 检查数据库连接
            if not self.db or not self.db.connection_pool:
                logger.warning("⚠️ 数据库连接异常，尝试重新连接")
                self.initialize_database()
                
            return True
        except Exception as e:
            logger.error(f"❌ 健康检查失败: {e}")
            return False

    def run(self, force_download: bool = False):
        """运行主程序"""
        try:
            # 初始化数据库
            if not self.initialize_database():
                return False

            # 初始化组件
            self.initialize_components()

            # 如果强制下载，执行强制下载任务
            if force_download:
                logger.info("🔄 强制下载模式：执行强制下载股票数据任务")
                self.force_stock_data_task()
                return True
            
            # 设置定时任务
            self.setup_schedule()
            
            # 添加健康检查任务
            schedule.every(5).minutes.do(self.health_check)  # type: ignore
            
            # 主循环
            self.running = True
            logger.info("🔄 进入主循环")
            
            while self.running:
                try:
                    schedule.run_pending()
                    time.sleep(1)
                except KeyboardInterrupt:
                    logger.info("⏹️ 收到停止信号")
                    self.running = False
                except Exception as e:
                    logger.error(f"❌ 主循环异常: {e}")
                    time.sleep(60)  # 异常后等待1分钟再继续
                    
        except Exception as e:
            logger.error(f"❌ 程序运行失败: {e}")
            return False
        finally:
            self.cleanup()
            
        return True

    def run_after_market(self):
        """手动执行盘后任务"""
        try:
            if not self.initialize_database():
                return False
                
            self.initialize_components()
            
            # 强制刷新交易日历
            trade_time_manager.refresh_trade_dates()
            
            # 执行盘后任务
            self.after_market_task()
            
            return True
        except Exception as e:
            logger.error(f"❌ 手动盘后任务失败: {e}")
            return False
        finally:
            self.cleanup()

    def cleanup(self):
        """清理资源"""
        try:
            if self.db:
                self.db.disconnect()
                logger.info("✅ 数据库连接已关闭")
        except Exception as e:
            logger.error(f"⚠️ 清理资源时出错: {e}")


def main():
    """主函数"""
    # 设置警告管理器 - 抑制第三方库的pandas链式赋值警告
    setup_warnings_for_environment('production')

    parser = argparse.ArgumentParser(description='股票数据采集系统')
    parser.add_argument('--force-download', action='store_true',
                       help='强制执行数据下载')
    parser.add_argument('--after-market', action='store_true',
                       help='手动执行盘后任务')
    parser.add_argument('--refresh-calendar', action='store_true',
                       help='刷新交易日历')
    parser.add_argument('--minute-data', action='store_true',
                       help='采集分钟数据')
    parser.add_argument('--concept-data', action='store_true',
                       help='采集概念数据')
    parser.add_argument('--weekly-batch', action='store_true',
                       help='执行每周三批量更新任务')
    parser.add_argument('--daily-incremental', action='store_true',
                       help='执行每日15:30增量更新任务')
    parser.add_argument('--force-stock-data', action='store_true',
                       help='强制下载股票数据（2000条历史数据）')

    args = parser.parse_args()
    
    app = StockDataApp()
    
    try:
        if args.refresh_calendar:
            # 刷新交易日历
            trade_time_manager.refresh_trade_dates()
            logger.info("✅ 交易日历刷新完成")
            
        elif args.after_market:
            # 手动执行盘后任务
            success = app.run_after_market()
            if success:
                logger.info("✅ 盘后任务执行完成")
            else:
                logger.error("❌ 盘后任务执行失败")

        elif args.minute_data:
            # 采集分钟数据 - 使用强制下载模式获取2000条历史数据
            if not app.initialize_database():
                return
            app.initialize_components()
            app.collect_minute_data(force_download=True)  # 关键修复：启用强制下载模式
            app.cleanup()
            logger.info("✅ 分钟数据采集完成")

        elif args.concept_data:
            # 采集概念数据
            if not app.initialize_database():
                return
            app.initialize_components()
            app.collect_concept_data()
            app.cleanup()
            logger.info("✅ 概念数据采集完成")

        elif args.weekly_batch:
            # 执行每周三批量更新任务
            if not app.initialize_database():
                return
            app.initialize_components()
            app.weekly_batch_task()
            app.cleanup()
            logger.info("✅ 每周三批量更新任务完成")

        elif args.daily_incremental:
            # 执行每日15:30增量更新任务
            if not app.initialize_database():
                return
            app.initialize_components()
            app.daily_incremental_task()
            app.cleanup()
            logger.info("✅ 每日15:30增量更新任务完成")

        elif args.force_stock_data:
            # 强制下载股票数据（2000条历史数据）
            if not app.initialize_database():
                return
            app.initialize_components()
            app.force_stock_data_task()
            app.cleanup()
            logger.info("✅ 强制下载股票数据任务完成")

        else:
            # 正常运行
            success = app.run(force_download=args.force_download)
            if not success:
                logger.error("❌ 程序运行失败")
                
    except KeyboardInterrupt:
        logger.info("⏹️ 程序被用户终止")
    except Exception as e:
        logger.error(f"❌ 程序异常: {e}")


if __name__ == "__main__":
    main()
