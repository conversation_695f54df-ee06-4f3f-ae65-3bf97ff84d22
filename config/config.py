#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用配置管理
简化的配置管理解决方案

功能特性:
- 环境变量支持
- 类型安全
- 默认值配置
- 配置验证

作者: Xzh
版本: 2.0.0
"""

import os
from dataclasses import dataclass
from pathlib import Path
from typing import Tuple

# 尝试加载 .env 文件
try:
    from dotenv import load_dotenv
    # 查找 .env 文件路径
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✅ 已加载环境配置文件: {env_path}")
    else:
        print(f"⚠️ 环境配置文件不存在: {env_path}")
except ImportError:
    print("⚠️ python-dotenv 未安装，将使用系统环境变量")


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "*********"
    port: int = 6668  # 自定义端口
    username: str = "postgres"
    password: str = "241110"  # 更新默认密码
    database: str = "xystock"
    minconn: int = 2
    maxconn: int = 10
    timeout: int = 30
    db_type: str = "timescaledb"  # 数据库类型：timescaledb


@dataclass
class APIConfig:
    """API配置"""
    rate_limit: float = 5.0  # 每秒请求数
    rate_capacity: int = 20  # 令牌桶容量
    retry_count: int = 3     # 重试次数
    timeout: int = 30        # 超时时间


@dataclass
class DataConfig:
    """数据采集配置"""
    batch_size: int = 100                    # 批处理大小
    max_workers: int = 20                    # 最大工作线程数
    minute_periods: Tuple[int, ...] = (5, 15) # 分钟数据周期
    start_date: str = "2019-01-01"           # 默认开始日期


@dataclass
class LogConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_enabled: bool = True
    console_enabled: bool = True
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


@dataclass
class TimeConfig:
    """时间配置"""
    timezone: str = "Asia/Shanghai"
    datetime_format: str = "%Y-%m-%d %H:%M:%S"
    date_format: str = "%Y-%m-%d"


class Config:
    """应用配置类"""
    
    def __init__(self):
        """初始化配置"""
        self.database = self._load_database_config()
        self.api = self._load_api_config()
        self.data = self._load_data_config()
        self.log = self._load_log_config()
        self.time = self._load_time_config()
    
    def _load_database_config(self) -> DatabaseConfig:
        """加载数据库配置"""
        return DatabaseConfig(
            host=os.getenv("DB_HOST", "*********"),
            port=int(os.getenv("DB_PORT", "6668")),
            username=os.getenv("DB_USERNAME", "postgres"),
            password=os.getenv("DB_PASSWORD", "241110"),
            database=os.getenv("DB_DATABASE", "xystock"),
            minconn=int(os.getenv("DB_MINCONN", "2")),
            maxconn=int(os.getenv("DB_MAXCONN", "10")),
            timeout=int(os.getenv("DB_TIMEOUT", "30")),
            db_type=os.getenv("DB_TYPE", "timescaledb")
        )
    
    def _load_api_config(self) -> APIConfig:
        """加载API配置"""
        return APIConfig(
            rate_limit=float(os.getenv("API_RATE_LIMIT", "5.0")),
            rate_capacity=int(os.getenv("API_RATE_CAPACITY", "20")),
            retry_count=int(os.getenv("API_RETRY_COUNT", "3")),
            timeout=int(os.getenv("API_TIMEOUT", "30"))
        )
    
    def _load_data_config(self) -> DataConfig:
        """加载数据配置"""
        periods_str = os.getenv("DATA_MINUTE_PERIODS", "5,15")
        periods: Tuple[int, ...] = tuple(int(p.strip()) for p in periods_str.split(","))

        return DataConfig(
            batch_size=int(os.getenv("DATA_BATCH_SIZE", "100")),
            max_workers=int(os.getenv("DATA_MAX_WORKERS", "10")),
            minute_periods=periods,
            start_date=os.getenv("DATA_START_DATE", "2019-01-01")
        )
    
    def _load_log_config(self) -> LogConfig:
        """加载日志配置"""
        return LogConfig(
            level=os.getenv("LOG_LEVEL", "INFO"),
            format=os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
            file_enabled=os.getenv("LOG_FILE_ENABLED", "true").lower() == "true",
            console_enabled=os.getenv("LOG_CONSOLE_ENABLED", "true").lower() == "true",
            max_file_size=int(os.getenv("LOG_MAX_FILE_SIZE", str(10 * 1024 * 1024))),
            backup_count=int(os.getenv("LOG_BACKUP_COUNT", "5"))
        )
    
    def _load_time_config(self) -> TimeConfig:
        """加载时间配置"""
        return TimeConfig(
            timezone=os.getenv("TIME_TIMEZONE", "Asia/Shanghai"),
            datetime_format=os.getenv("TIME_DATETIME_FORMAT", "%Y-%m-%d %H:%M:%S"),
            date_format=os.getenv("TIME_DATE_FORMAT", "%Y-%m-%d")
        )
    
    def validate(self) -> bool:
        """验证配置"""
        try:
            # 验证数据库配置
            assert self.database.host, "数据库主机不能为空"
            assert 1 <= self.database.port <= 65535, "数据库端口必须在1-65535之间"
            assert self.database.username, "数据库用户名不能为空"
            assert self.database.password, "数据库密码不能为空"
            assert self.database.database, "数据库名不能为空"
            
            # 验证API配置
            assert self.api.rate_limit > 0, "API速率限制必须大于0"
            assert self.api.rate_capacity > 0, "API容量必须大于0"
            assert self.api.retry_count >= 0, "重试次数不能为负数"
            
            # 验证数据配置
            assert self.data.batch_size > 0, "批处理大小必须大于0"
            assert self.data.max_workers > 0, "最大工作线程数必须大于0"
            assert all(period > 0 for period in self.data.minute_periods), "分钟周期必须大于0"
            
            return True
            
        except AssertionError as e:
            raise ValueError(f"配置验证失败: {e}")
    
    def __str__(self) -> str:
        """配置信息字符串表示"""
        return f"""
配置信息:
数据库: {self.database.host}:{self.database.port}/{self.database.database}
API限制: {self.api.rate_limit} req/s
数据批次: {self.data.batch_size}
工作线程: {self.data.max_workers}
分钟周期: {self.data.minute_periods}
日志级别: {self.log.level}
时区: {self.time.timezone}
        """.strip()


# 创建全局配置实例
config = Config()

# 验证配置
try:
    config.validate()
except ValueError as e:
    print(f"配置错误: {e}")
    exit(1)


# 兼容性：保持原有的settings接口
class Settings:
    """兼容性设置类"""

    def __init__(self):
        # 数据库配置 - TimescaleDB
        self.TIMESCALEDB_HOST = config.database.host
        self.TIMESCALEDB_PORT = config.database.port
        self.TIMESCALEDB_USERNAME = config.database.username
        self.TIMESCALEDB_PASSWORD = config.database.password
        self.TIMESCALEDB_DATABASE = config.database.database
        self.TIMESCALEDB_MINCONN = config.database.minconn
        self.TIMESCALEDB_MAXCONN = config.database.maxconn
        
        # API配置
        self.API_RATE_LIMIT = config.api.rate_limit
        self.API_RATE_CAPACITY = config.api.rate_capacity
        self.REQUEST_RETRY_COUNT = config.api.retry_count
        
        # 数据配置
        self.BATCH_SIZE = config.data.batch_size
        self.MAX_WORKERS = config.data.max_workers
        
        # 时间配置
        self.TIMEZONE = config.time.timezone
        self.DATETIME_FORMAT = config.time.datetime_format
        self.DATE_FORMAT = config.time.date_format
        
        # 其他配置
        self.DATABASE_TYPE = config.database.db_type
        self.PROXY_ENABLED = False


# 创建兼容性实例
settings = Settings()
