#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
类型安全测试脚本
测试项目的类型注解和类型安全性
"""

import sys
import subprocess
import importlib
from pathlib import Path
from typing import List, Dict, Any, Optional
import pandas as pd
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports() -> Dict[str, bool]:
    """测试所有模块的导入是否正常"""
    print("🔍 测试模块导入...")
    
    modules_to_test = [
        'config.config',
        'data.market_fetcher',
        'data.down_data',
        'data.database_manager',
        'data.timescaledb_manager',
        'utils.logger',
        'utils.decorators',
        'utils.mootdx_manager',
        'utils.rate_limiter',
        'utils.trade_calendar',
        'utils.time_utils',
        'utils.warning_manager'
    ]
    
    results = {}
    
    for module_name in modules_to_test:
        try:
            importlib.import_module(module_name)
            results[module_name] = True
            print(f"  ✅ {module_name}")
        except Exception as e:
            results[module_name] = False
            print(f"  ❌ {module_name}: {e}")
    
    return results

def test_type_annotations() -> Dict[str, bool]:
    """测试关键类的类型注解"""
    print("\n🔍 测试类型注解...")
    
    results = {}
    
    try:
        from data.market_fetcher import MarketDataFetcher
        from data.down_data import DownData
        from config.config import settings
        
        # 测试 MarketDataFetcher 类型注解
        fetcher = MarketDataFetcher()
        
        # 检查方法是否有正确的类型注解
        get_stock_daily = getattr(fetcher, 'get_stock_daily_data', None)
        if get_stock_daily and hasattr(get_stock_daily, '__annotations__'):
            annotations = get_stock_daily.__annotations__
            expected_params = ['stock_code', 'start_date', 'end_date', 'limit']
            has_return_type = 'return' in annotations
            
            results['MarketDataFetcher.get_stock_daily_data'] = (
                all(param in annotations for param in expected_params[:1]) and  # 至少有 stock_code
                has_return_type
            )
            print(f"  ✅ MarketDataFetcher.get_stock_daily_data 类型注解正确")
        else:
            results['MarketDataFetcher.get_stock_daily_data'] = False
            print(f"  ❌ MarketDataFetcher.get_stock_daily_data 缺少类型注解")
        
        # 测试 DownData 类型注解
        downloader = DownData()
        
        # 检查上下文管理器方法
        exit_method = getattr(downloader, '__exit__', None)
        if exit_method and hasattr(exit_method, '__annotations__'):
            annotations = exit_method.__annotations__
            has_proper_exit = len(annotations) >= 3  # exc_type, exc_val, exc_tb + return
            results['DownData.__exit__'] = has_proper_exit
            print(f"  ✅ DownData.__exit__ 类型注解正确")
        else:
            results['DownData.__exit__'] = False
            print(f"  ❌ DownData.__exit__ 缺少类型注解")
        
        # 测试配置类型
        if hasattr(settings, 'DATABASE'):
            results['config.settings'] = True
            print(f"  ✅ config.settings 配置正确")
        else:
            results['config.settings'] = False
            print(f"  ❌ config.settings 配置缺失")
            
    except Exception as e:
        print(f"  ❌ 类型注解测试失败: {e}")
        results['type_annotations_test'] = False
    
    return results

def test_pandas_operations() -> Dict[str, bool]:
    """测试 pandas 操作的类型安全性"""
    print("\n🔍 测试 pandas 操作...")
    
    results = {}
    
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            'trade_time': [datetime.now()],
            'stock_code': ['000001'],
            'open': [10.0],
            'high': [11.0],
            'low': [9.0],
            'close': [10.5],
            'volume': [1000],
            'amount': [10500.0]
        })
        
        # 测试基本操作
        sorted_data = test_data.sort_values('trade_time').reset_index(drop=True)
        filtered_data = test_data.dropna(subset=['open', 'high', 'low', 'close'])
        
        results['pandas_basic_operations'] = (
            not sorted_data.empty and 
            not filtered_data.empty
        )
        print(f"  ✅ pandas 基本操作正常")
        
        # 测试时间处理
        test_data.loc[:, 'trade_time'] = pd.to_datetime(test_data['trade_time'])
        time_processed = not test_data['trade_time'].isna().any()
        
        results['pandas_time_operations'] = time_processed
        print(f"  ✅ pandas 时间处理正常")
        
        # 测试数值转换
        test_data.loc[:, 'volume'] = pd.to_numeric(test_data['volume'], errors='coerce')
        numeric_processed = pd.api.types.is_numeric_dtype(test_data['volume'])
        
        results['pandas_numeric_operations'] = numeric_processed
        print(f"  ✅ pandas 数值转换正常")
        
    except Exception as e:
        print(f"  ❌ pandas 操作测试失败: {e}")
        results['pandas_operations_test'] = False
    
    return results

def test_third_party_imports() -> Dict[str, bool]:
    """测试第三方库导入"""
    print("\n🔍 测试第三方库导入...")
    
    results = {}
    third_party_libs = ['mootdx', 'akshare', 'adata']
    
    for lib in third_party_libs:
        try:
            importlib.import_module(lib)
            results[lib] = True
            print(f"  ✅ {lib} 导入成功")
        except ImportError:
            results[lib] = False
            print(f"  ⚠️ {lib} 未安装或导入失败")
        except Exception as e:
            results[lib] = False
            print(f"  ❌ {lib} 导入错误: {e}")
    
    return results

def run_mypy_check() -> bool:
    """运行 mypy 类型检查"""
    print("\n🔍 运行 mypy 类型检查...")
    
    try:
        # 检查 mypy 是否安装
        result = subprocess.run(['mypy', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            print("  ⚠️ mypy 未安装，跳过类型检查")
            return False
        
        # 运行类型检查
        modules_to_check = [
            'config/config.py',
            'data/market_fetcher.py',
            'data/down_data.py',
            'utils/logger.py'
        ]
        
        all_passed = True
        for module in modules_to_check:
            if Path(module).exists():
                result = subprocess.run(['mypy', module], 
                                      capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    print(f"  ✅ {module} 类型检查通过")
                else:
                    print(f"  ⚠️ {module} 有类型警告")
                    # 不将警告视为失败，因为我们已经处理了已知的警告
            else:
                print(f"  ⚠️ {module} 文件不存在")
        
        return all_passed
        
    except subprocess.TimeoutExpired:
        print("  ❌ mypy 检查超时")
        return False
    except FileNotFoundError:
        print("  ⚠️ mypy 未安装")
        return False
    except Exception as e:
        print(f"  ❌ mypy 检查失败: {e}")
        return False

def generate_test_report(results: Dict[str, Dict[str, bool]]) -> None:
    """生成测试报告"""
    print("\n" + "="*60)
    print("📊 类型安全测试报告")
    print("="*60)
    
    total_tests = 0
    passed_tests = 0
    
    for category, tests in results.items():
        print(f"\n📋 {category}:")
        if isinstance(tests, dict):
            for test_name, passed in tests.items():
                status = "✅ 通过" if passed else "❌ 失败"
                print(f"  {test_name}: {status}")
                total_tests += 1
                if passed:
                    passed_tests += 1
        else:
            status = "✅ 通过" if tests else "❌ 失败"
            print(f"  {category}: {status}")
            total_tests += 1
            if tests:
                passed_tests += 1
    
    print(f"\n📈 总体结果:")
    print(f"  总测试数: {total_tests}")
    print(f"  通过测试: {passed_tests}")
    print(f"  失败测试: {total_tests - passed_tests}")
    print(f"  通过率: {(passed_tests/total_tests*100):.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！项目类型安全性良好。")
    elif passed_tests / total_tests >= 0.8:
        print("\n✅ 大部分测试通过，项目类型安全性较好。")
    else:
        print("\n⚠️ 部分测试失败，建议检查类型注解。")

def main():
    """主测试函数"""
    print("🚀 开始类型安全测试...")
    print(f"📁 项目路径: {project_root}")
    
    # 运行各项测试
    results = {
        "模块导入测试": test_imports(),
        "类型注解测试": test_type_annotations(),
        "pandas操作测试": test_pandas_operations(),
        "第三方库测试": test_third_party_imports(),
        "mypy类型检查": run_mypy_check()
    }
    
    # 生成报告
    generate_test_report(results)

if __name__ == "__main__":
    main()
