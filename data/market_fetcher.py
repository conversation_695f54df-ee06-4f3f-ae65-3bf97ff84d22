#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场数据采集器
高效的股票、指数、概念数据采集解决方案

功能特性:
- 股票日线和分钟数据：使用mootdx数据源（通达信接口）
- 概念数据、风险数据、指数数据：使用adata数据源
- 智能重试机制
- 批量数据处理
- 会话管理优化
- 支持循环获取大量历史数据（突破800条限制）

数据源配置:
- 股票日线数据：mootdx（通达信）- 默认一天数据，强制下载2000条
- 股票分钟数据：mootdx（通达信）- 默认一天数据，强制下载2000条
- 概念数据：adata
- 风险数据：adata
- 指数数据：adata

技术特点:
- mootdx单次最多800条数据，通过循环获取突破限制
- 支持前复权、后复权和不复权
- 自动处理股票代码格式转换
- 高效的数据清洗和格式化

作者: Xzh
版本: 3.0.0
"""

import adata as ad  # type: ignore # 用于概念数据、风险数据和指数数据
from mootdx.quotes import Quotes  # type: ignore # mootdx数据源
import pandas as pd
import threading
import time
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any, Set, Union
from tqdm import tqdm

from utils.logger import setup_logger
from utils.decorators import retry_on_error  # type: ignore
from utils.warning_manager import suppress_warnings, warning_manager
from data.database_manager import DatabaseManager

logger = setup_logger(__name__)


def round_price_fields(df: pd.DataFrame, price_columns: Optional[List[str]] = None, decimal_places: int = 2) -> pd.DataFrame:
    """
    统一处理价格字段精度，确保最多保留指定位数小数

    Args:
        df: 待处理的DataFrame
        price_columns: 价格字段列表，默认为['open', 'high', 'low', 'close', 'pre_close']
        decimal_places: 保留小数位数，默认为2位

    Returns:
        pd.DataFrame: 处理后的DataFrame
    """
    if df.empty:
        return df

    if price_columns is None:
        price_columns = ['open', 'high', 'low', 'close', 'pre_close']

    # 只处理存在的价格字段
    existing_price_columns = [col for col in price_columns if col in df.columns]

    if existing_price_columns:
        for col in existing_price_columns:
            # 先确保是数值类型，如果不是则尝试转换
            if not pd.api.types.is_numeric_dtype(df[col]):  # type: ignore
                # 尝试转换为数值类型
                df.loc[:, col] = pd.to_numeric(df[col], errors='coerce')  # type: ignore

            # 进行精度控制
            if pd.api.types.is_numeric_dtype(df[col]):  # type: ignore
                # 使用df.loc[]避免链式赋值警告，四舍五入到指定小数位数
                df.loc[:, col] = df[col].round(decimal_places)

    return df


class MarketDataFetcher:
    """市场数据采集器 - 高效的多源数据采集"""

    def __init__(self, db: DatabaseManager):
        """
        初始化市场数据采集器

        Args:
            db: 数据库管理器实例
        """
        self.db = db
        self._stock_codes: Optional[List[str]] = None
        self._thread_local = threading.local()
        self.minute_periods = {5, 15}  # 分钟数据周期

        # 指数代码映射
        self._index_codes = {
            '000001': '上证指数',
            '399001': '深证成指',
            '399006': '创业板指',
            '000852': '中证1000',
            '000300': '沪深300',
            '930050': '中证A50',
            '000510': '中证A500'        #200-800中盘股
        }

        # mootdx数据源失败记录
        self.failed_stocks: Dict[str, Set[str]] = {
            'daily': set(),    # 日线数据获取失败的股票
            'minute': set()    # 分钟数据获取失败的股票
        }

        # mootdx数据源配置 - 专用于股票日线和分钟数据
        self.data_source_priority = ['mootdx']  # 仅使用mootdx数据源

        # mootdx客户端配置
        self.mootdx_config: Dict[str, Any] = {
            'market': 'std',           # 标准股票市场
            'multithread': True,       # 启用多线程
            'heartbeat': True,         # 启用心跳包
            'bestip': False,           # 不自动选择最快服务器（使用默认）
            'timeout': 15,             # 连接超时时间（秒）
            'quiet': True              # 静默模式，不打印日志
        }

        # mootdx数据获取配置 - 基于k方法的时间分段获取版本，真正支持2000条历史数据
        self.mootdx_data_config: Dict[str, Any] = {
            'default_limit': 240,              # 默认获取数据量（一天约240条分钟数据）
            'force_download_limit': 2000,      # 强制下载历史数据的数量（基于k方法时间分段获取支持2000条）
            'max_single_request': 800,         # mootdx单次请求最大数据量（bars方法技术限制）
            'k_method_enabled': True,          # 启用k方法，支持基于时间范围的数据获取
            'time_segment_years': 8,           # 时间分段策略：回溯8年历史数据（优化性能）
            'segment_months': 6,               # 每个时间段的月数（6个月一段，减少分段数量提升性能）
            'max_segments': 16,                # 最大时间段数（8年*2段/年=16段，减少请求次数）
            'adjust_type': 'qfq',              # 复权类型：'qfq'前复权，''不复权，'hfq'后复权
            'retry_count': 3,                  # 重试次数
            'retry_delay': 0.2,                # 重试延迟（秒，进一步缩短延迟提升性能）
            'enable_time_segmentation': True,  # 启用时间分段模式，支持真正的2000条数据获取
            'dedup_enabled': True,             # 启用去重功能，处理时间段间的重复数据
            'sort_by_time': True,              # 按时间排序，确保数据的时间顺序正确
            'k_method_mode': True,             # 启用k方法模式，基于时间范围获取历史数据
            'enable_minute_time_segmentation': True,  # 启用分钟数据时间分段模式，支持分钟数据2000条获取
        }

        # 数据源轮换配置（已禁用，因为只使用mootdx单一数据源）
        self.rotation_config: Dict[str, Any] = {
            'enabled': False,               # 禁用数据源轮换（单一数据源）
            'batch_size': 1000,            # 保留配置结构以维持兼容性
            'current_batch': 0,            # 当前批次计数器
            'processed_count': 0,          # 已处理股票计数器
            'original_priority': ['mootdx'], # 仅mootdx数据源
            'rotated_priority': ['mootdx'],  # 仅mootdx数据源
            'current_primary': 'mootdx',   # 当前主数据源为mootdx
            'rotation_history': []         # 轮换历史记录
        }

        # 设置警告过滤器，抑制第三方库的pandas警告
        warning_manager.filter_specific_warnings()

        logger.info("✅ 市场数据采集器初始化完成")
        logger.info("📊 数据源配置:")
        logger.info("   股票日线数据: mootdx（通达信接口）")
        logger.info("   股票分钟数据: mootdx（通达信接口）")
        logger.info("   概念数据: adata")
        logger.info("   风险数据: adata")
        logger.info("   指数数据: adata")
        logger.info(f"📋 mootdx配置: 默认{self.mootdx_data_config['default_limit']}条，强制下载{self.mootdx_data_config['force_download_limit']}条")

        # 根据k方法模式显示不同的配置信息
        if self.mootdx_data_config.get('k_method_mode', False):
            logger.info("🔧 k方法时间分段模式: 基于时间范围获取历史数据，支持真正的2000条数据获取")
            logger.info(f"⚙️ 分段配置: 每段{self.mootdx_data_config['segment_months']}个月，"
                       f"最多{self.mootdx_data_config['max_segments']}段，回溯{self.mootdx_data_config['time_segment_years']}年")
        else:
            logger.info("🔧 传统模式: 基于bars方法获取数据，单次最多800条")

        logger.info(f"⚙️ 高级功能: 去重{'启用' if self.mootdx_data_config['dedup_enabled'] else '禁用'}，"
                   f"时间排序{'启用' if self.mootdx_data_config['sort_by_time'] else '禁用'}")

    def __enter__(self):
        """上下文管理器入口"""
        # 由于当前只使用mootdx数据源，不需要baostock登录
        logger.debug("📊 进入MarketDataFetcher上下文")
        return self

    def __exit__(self, exc_type: Optional[type], exc_val: Optional[Exception], exc_tb: Optional[Any]) -> None:
        """上下文管理器出口"""
        # 标准上下文管理器参数，符合协议要求
        _ = exc_type, exc_val, exc_tb  # 避免未使用参数警告
        # 由于当前只使用mootdx数据源，不需要baostock登出
        logger.debug("📊 退出MarketDataFetcher上下文")

    def login_baostock(self):
        """baostock登录（当前未使用）"""
        # 当前项目只使用mootdx数据源，保留此方法以维持兼容性
        logger.debug("📊 baostock登录（当前未使用）")
        pass

    def logout_baostock(self):
        """baostock登出（当前未使用）"""
        # 当前项目只使用mootdx数据源，保留此方法以维持兼容性
        logger.debug("📊 baostock登出（当前未使用）")
        pass

    def _create_mootdx_client(self) -> Optional[Quotes]:
        """
        创建mootdx客户端实例（已优化，使用资源管理器）

        使用新的mootdx资源管理器来创建和管理客户端连接，
        自动处理资源释放和连接复用，避免ResourceWarning。

        Returns:
            Optional[Quotes]: mootdx客户端实例，失败时返回None
        """
        # 使用新的mootdx资源管理器
        from utils.mootdx_manager import get_mootdx_client
        return get_mootdx_client()

    def _convert_stock_code_for_mootdx(self, stock_code: str) -> str:
        """
        转换股票代码为mootdx格式

        mootdx使用纯数字股票代码，不需要市场前缀

        Args:
            stock_code: 原始股票代码（如000001、600000等）

        Returns:
            str: mootdx格式的股票代码
        """
        # 移除可能的市场前缀（sz.、sh.等）
        if '.' in stock_code:
            stock_code = stock_code.split('.')[1]

        # 确保是6位数字格式
        stock_code = stock_code.zfill(6)

        return stock_code

    def _get_frequency_code(self, period: Optional[int] = None, data_type: str = 'daily') -> int:
        """
        获取mootdx的频率代码

        Args:
            period: 分钟周期（仅分钟数据需要）
            data_type: 数据类型，'daily'或'minute'

        Returns:
            int: mootdx频率代码
        """
        if data_type == 'daily':
            return 9  # 日K线
        elif data_type == 'minute':
            # 分钟K线频率映射
            frequency_map: Dict[int, int] = {
                1: 8,   # 1分钟K线
                5: 0,   # 5分钟K线
                15: 1,  # 15分钟K线
                30: 2,  # 30分钟K线
                60: 3   # 60分钟K线（1小时）
            }
            return frequency_map.get(period or 5, 0)  # 默认5分钟
        else:
            return 9  # 默认日K线



    @property
    def stock_codes(self) -> List[str]:
        """从数据库获取股票代码列表"""
        if self._stock_codes is None:
            try:
                # 从数据库stock_info表获取股票代码
                result = self.db.execute_query("SELECT stock_code FROM stock_info ORDER BY stock_code")  # type: ignore
                if result:
                    self._stock_codes = [str(row[0]) for row in result]  # type: ignore
                    logger.info(f"✅ 从数据库获取到 {len(self._stock_codes)} 只股票")
                else:
                    # 如果数据库为空，则使用现有的collect_stock_info方法
                    logger.warning("⚠️ 数据库中没有股票信息，调用collect_stock_info更新...")
                    self.collect_stock_info()
                    # 重新从数据库获取
                    result = self.db.execute_query("SELECT stock_code FROM stock_info ORDER BY stock_code")  # type: ignore
                    self._stock_codes = [str(row[0]) for row in result] if result else []  # type: ignore
                    logger.info(f"✅ 更新后从数据库获取到 {len(self._stock_codes)} 只股票")
            except Exception as e:
                logger.error(f"❌ 获取股票列表失败: {e}")
                self._stock_codes = []

        return self._stock_codes

    def refresh_stock_codes(self) -> None:
        """刷新股票代码列表缓存"""
        self._stock_codes = None
        logger.info("🔄 股票代码列表缓存已清空，下次访问时将重新加载")

    def rotate_data_source(self, force_rotate: bool = False) -> bool:
        """
        智能数据源轮换机制

        每处理1000只股票后自动切换主备数据源，实现负载均衡和数据源轮换。
        这样可以：
        1. 避免单一数据源过载
        2. 提高数据获取的稳定性
        3. 实现数据源的均衡使用

        Args:
            force_rotate: 是否强制轮换（忽略批次计数）

        Returns:
            bool: 是否发生了轮换
        """
        if not self.rotation_config['enabled']:
            return False

        # 检查是否需要轮换
        should_rotate = (
            force_rotate or
            self.rotation_config['processed_count'] >= self.rotation_config['batch_size']
        )

        if should_rotate:
            # 执行数据源轮换
            if self.rotation_config['current_primary'] == 'adata':
                # 当前主数据源是adata，切换到baostock
                self.data_source_priority = self.rotation_config['rotated_priority'].copy()
                self.rotation_config['current_primary'] = 'baostock'
                new_primary = 'baostock'
                new_backup = 'adata'
            else:
                # 当前主数据源是baostock，切换到adata
                self.data_source_priority = self.rotation_config['original_priority'].copy()
                self.rotation_config['current_primary'] = 'adata'
                new_primary = 'adata'
                new_backup = 'baostock'

            # 更新轮换统计
            self.rotation_config['current_batch'] += 1
            self.rotation_config['processed_count'] = 0

            # 记录轮换历史
            rotation_record: Dict[str, Any] = {
                'batch': self.rotation_config['current_batch'],
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'primary_source': new_primary,
                'backup_source': new_backup,
                'reason': 'force_rotate' if force_rotate else 'batch_complete'
            }
            self.rotation_config['rotation_history'].append(rotation_record)

            # 记录轮换日志
            logger.info(f"🔄 数据源轮换执行:")
            logger.info(f"   批次: {self.rotation_config['current_batch']}")
            logger.info(f"   新的主数据源: {new_primary}")
            logger.info(f"   新的备用数据源: {new_backup}")
            logger.info(f"   数据源优先级: {' -> '.join(self.data_source_priority)}")

            return True

        return False

    def increment_processed_count(self):
        """
        增加已处理股票计数器

        每处理一只股票后调用此方法，用于跟踪轮换进度
        """
        if self.rotation_config['enabled']:
            self.rotation_config['processed_count'] += 1

            # 检查是否达到轮换阈值
            if self.rotation_config['processed_count'] >= self.rotation_config['batch_size']:
                logger.info(f"📊 已处理 {self.rotation_config['processed_count']} 只股票，准备进行数据源轮换...")

    def get_rotation_status(self) -> Dict[str, Any]:
        """
        获取数据源轮换状态信息

        Returns:
            dict: 包含轮换状态的详细信息
        """
        return {
            'enabled': self.rotation_config['enabled'],
            'batch_size': self.rotation_config['batch_size'],
            'current_batch': self.rotation_config['current_batch'],
            'processed_count': self.rotation_config['processed_count'],
            'current_primary': self.rotation_config['current_primary'],
            'current_priority': self.data_source_priority.copy(),
            'progress_percentage': (self.rotation_config['processed_count'] / self.rotation_config['batch_size']) * 100,
            'next_rotation_in': self.rotation_config['batch_size'] - self.rotation_config['processed_count'],
            'rotation_history_count': len(self.rotation_config['rotation_history'])
        }

    def reset_rotation_counter(self):
        """
        重置轮换计数器

        在开始新的批量数据采集时调用，确保轮换逻辑正确工作
        """
        self.rotation_config['processed_count'] = 0
        self.rotation_config['current_batch'] = 0
        # 重置为原始数据源优先级
        self.data_source_priority = self.rotation_config['original_priority'].copy()
        self.rotation_config['current_primary'] = 'adata'

        logger.info("🔄 数据源轮换计数器已重置")

    def disable_rotation(self):
        """禁用数据源轮换"""
        self.rotation_config['enabled'] = False
        # 恢复原始数据源优先级
        self.data_source_priority = self.rotation_config['original_priority'].copy()
        self.rotation_config['current_primary'] = 'adata'
        logger.info("⏸️ 数据源轮换已禁用")

    def enable_rotation(self):
        """启用数据源轮换"""
        self.rotation_config['enabled'] = True
        logger.info("▶️ 数据源轮换已启用")

    def update_stock_info(self):
        """手动更新股票基础信息"""
        try:
            self.collect_stock_info()  # 直接调用采集方法
            self.refresh_stock_codes()  # 刷新缓存
            logger.info("✅ 股票基础信息手动更新完成")
        except Exception as e:
            logger.error(f"❌ 手动更新股票信息失败: {e}")
            raise
    
    def collect_stock_info(self):
        """采集股票基础信息"""
        try:
            import akshare as ak  # type: ignore
            df = ak.stock_zh_a_spot_em()
            # 过滤掉ST股票和退市股票
            df = df[  # type: ignore
                (~df['名称'].str.contains('退')) &  # type: ignore
                (~df['名称'].str.contains('ST')) &  # type: ignore
                (~df['代码'].str.startswith('8')) &  # type: ignore
                (~df['代码'].str.startswith('68')) &  # type: ignore
                (~df['代码'].str.startswith('4')) &  # type: ignore
                (~df['代码'].str.startswith('9'))  # type: ignore
            ].dropna(subset=['今开'])  # type: ignore
            
            # 重命名列
            df.rename(columns={
                '代码': 'stock_code',
                '名称': 'stock_name',
                '最新价': 'close',
                '市盈率-动态': 'pe_dynamic',
                '市净率': 'pb_ratio',  # 修正字段名
                '总市值': 'market_cap',
                '流通市值': 'free_market',
                '年初至今涨跌幅': 'to_now'
            }, inplace=True)
            
            # 添加缺失的score字段
            # df['score'] = 0.0  # 默认评分为0

            # 选择需要的列
            df = df[['stock_code', 'stock_name', 'close', 'pe_dynamic', 'pb_ratio', 'market_cap', 'free_market', 'to_now']]

            # 插入数据库
            self.db.batch_insert_df('stock_info', df, conflict_fields=['stock_code'])
            logger.info(f"✅ 股票基础信息采集完成: {len(df)} 条记录")
        except Exception as e:
            logger.error(f"❌ 股票基础信息采集失败: {e}")


    def get_stock_daily_data(self, stock_code: str, start_date: str, end_date: str,
                           limit: Optional[int] = None, force_download: bool = False) -> pd.DataFrame:
        """
        获取股票日线数据 - 使用mootdx数据源（通达信接口）

        通过mootdx获取股票日线数据，支持大量历史数据的循环获取。
        mootdx单次最多返回800条数据，本方法通过循环请求突破此限制。

        Args:
            stock_code: 股票代码（如000001、600000等）
            start_date: 开始日期（格式：YYYY-MM-DD，用于日志显示）
            end_date: 结束日期（格式：YYYY-MM-DD，用于日志显示）
            limit: 获取数据条数，None时使用默认配置
            force_download: 是否强制下载大量数据

        Returns:
            pd.DataFrame: 清洗后的日线数据
        """
        try:
            # 参数保留用于接口兼容性和日志显示
            _ = start_date, end_date

            # 确定获取数据量
            if force_download:
                target_limit = self.mootdx_data_config['force_download_limit']
                logger.info(f"🔄 强制下载模式：获取股票{stock_code}最近{target_limit}条日线数据")
            elif limit is not None:
                target_limit = limit
            else:
                target_limit = self.mootdx_data_config['default_limit']

            # 获取日线数据
            df = self._get_stock_daily_data_mootdx(stock_code, target_limit)

            if not df.empty:
                logger.debug(f"✅ 使用mootdx成功获取股票{stock_code}日线数据: {len(df)}条")
                return df
            else:
                logger.warning(f"⚠️ mootdx获取股票{stock_code}日线数据为空")

        except Exception as e:
            logger.warning(f"⚠️ mootdx获取股票{stock_code}日线数据失败: {e}")

        # mootdx数据源失败，记录失败股票
        self.failed_stocks['daily'].add(stock_code)
        logger.error(f"❌ mootdx无法获取股票{stock_code}的日线数据")
        return pd.DataFrame()

    def _get_stock_daily_data_mootdx(self, stock_code: str, limit: int) -> pd.DataFrame:
        """
        使用mootdx获取股票日线数据的具体实现

        支持循环获取大量历史数据，突破mootdx单次800条的限制

        Args:
            stock_code: 股票代码
            limit: 需要获取的数据条数

        Returns:
            pd.DataFrame: 原始日线数据
        """
        try:
            # 转换股票代码格式
            mootdx_code = self._convert_stock_code_for_mootdx(stock_code)

            # 创建mootdx客户端
            client = self._create_mootdx_client()
            if client is None:
                logger.error(f"❌ 无法创建mootdx客户端获取股票{stock_code}日线数据")
                return pd.DataFrame()

            # 获取日K线频率代码
            frequency = self._get_frequency_code(data_type='daily')

            # 如果需要的数据量小于等于800条，直接获取
            max_single_request = self.mootdx_data_config['max_single_request']
            if limit <= max_single_request:
                logger.debug(f"📊 直接获取股票{stock_code}日线数据{limit}条（最新数据）")

                # 修复：使用get_security_bars方法，确保获取最新的limit条数据
                df = self._get_security_bars_data(
                    client=client,
                    symbol=mootdx_code,
                    frequency=frequency,
                    start=0,  # 从最新数据开始
                    count=limit,  # 获取指定条数
                    adjust_type=self.mootdx_data_config['adjust_type']
                )

                if df is not None and not df.empty:  # type: ignore
                    # 数据清洗和格式转换
                    df = self._clean_daily_data_mootdx(df, stock_code)
                    logger.debug(f"✅ 成功获取股票{stock_code}日线数据: {len(df)}条")
                    return df
                else:
                    logger.warning(f"⚠️ mootdx返回空数据：股票{stock_code}")
                    return pd.DataFrame()

            # 需要循环获取大量数据 - 智能循环获取版本，真正支持2000条历史数据
            # 使用智能循环获取策略，基于offset参数的有效性突破800条限制
            logger.info(f"🔄 智能循环获取股票{stock_code}日线数据，目标{limit}条，单次限制{max_single_request}条")

            # 使用偏移量策略获取大量历史数据
            if limit > max_single_request:
                logger.info(f"📊 启用偏移量策略：目标{limit}条，将分段获取历史数据")
                df = self._get_stock_data_with_offset_mootdx(
                    client, mootdx_code, 9, limit, 'daily', stock_code  # 9=日线频率
                )
            else:
                # 单次获取模式（限制在800条以内）
                actual_limit = min(limit, max_single_request)
                logger.debug(f"📊 单次获取模式：实际获取{actual_limit}条最新日线数据")

                # 使用统一的get_security_bars方法
                df = self._get_security_bars_data(
                    client=client,
                    symbol=mootdx_code,
                    frequency=frequency,
                    start=0,  # 从最新数据开始
                    count=actual_limit,
                    adjust_type=self.mootdx_data_config['adjust_type']
                )

            if df is not None and not df.empty:  # type: ignore
                logger.info(f"✅ 获取完成：股票{stock_code}共获取{len(df)}条日线数据")

                # 数据清洗和格式转换
                df = self._clean_daily_data_mootdx(df, stock_code)
                return df
            else:
                logger.warning(f"⚠️ 获取失败：股票{stock_code}未获取到任何日线数据")
                return pd.DataFrame()



        except Exception as e:
            logger.error(f"❌ mootdx获取股票{stock_code}日线数据异常: {e}")
            return pd.DataFrame()

    def _get_stock_data_in_batches_mootdx(self, client: Any, symbol: str, frequency: int,
                                         target_limit: int, data_type: str, stock_code: str) -> pd.DataFrame:
        """
        批次获取mootdx股票数据，突破单次800条限制，真正支持2000条历史数据获取

        技术实现原理：
        1. mootdx的bars方法每次最多返回800条最新数据
        2. 通过多次调用，每次获取800条，然后合并去重
        3. 使用时间戳作为去重依据，确保数据的唯一性和连续性
        4. 支持日线和分钟数据的批次获取

        Args:
            client: mootdx客户端实例
            symbol: mootdx格式的股票代码
            frequency: 数据频率代码
            target_limit: 目标获取数据量（如2000条）
            data_type: 数据类型（'daily' 或 'minute'）
            stock_code: 原始股票代码（用于日志）

        Returns:
            pd.DataFrame: 合并后的历史数据，按时间正序排列
        """
        try:
            # 优化批次获取配置，支持真正的2000条数据获取
            batch_size = self.mootdx_data_config['max_single_request']  # 800条
            # 动态计算批次数量，确保能获取到目标数量的数据
            max_batches = max(3, (target_limit // batch_size) + 2)  # 至少3批次，根据目标动态调整
            overlap = 50  # 重叠50条用于去重检查

            logger.info(f"🔄 开始优化批次获取{data_type}数据：股票{stock_code}，目标{target_limit}条")
            logger.debug(f"📊 优化批次配置：每批{batch_size}条，最多{max_batches}批次，重叠{overlap}条")

            all_data = []  # 存储所有批次的数据
            total_collected = 0  # 已收集的数据总量
            batch_count = 0  # 当前批次计数

            # 循环获取多个批次的数据
            while total_collected < target_limit and batch_count < max_batches:
                batch_count += 1

                # 计算当前批次需要获取的数据量
                remaining_needed = target_limit - total_collected
                current_batch_size = min(batch_size, remaining_needed + overlap)

                logger.debug(f"📦 第{batch_count}批次：获取{current_batch_size}条数据")

                try:
                    # 使用统一的get_security_bars方法获取当前批次数据
                    batch_df = self._get_security_bars_data(
                        client=client,
                        symbol=symbol,
                        frequency=frequency,
                        start=0,  # 从最新数据开始
                        count=current_batch_size,
                        adjust_type=self.mootdx_data_config['adjust_type']
                    )

                    if batch_df is None or batch_df.empty:  # type: ignore
                        logger.warning(f"⚠️ 第{batch_count}批次返回空数据，停止获取")
                        break

                    logger.debug(f"✅ 第{batch_count}批次获取成功：{len(batch_df)}条数据")

                    # 添加到总数据集
                    all_data.append(batch_df)  # type: ignore
                    total_collected += len(batch_df)

                    # 如果当前批次数据量小于请求量，说明已经获取到所有可用数据
                    if len(batch_df) < current_batch_size:
                        logger.info(f"📊 第{batch_count}批次数据不足{current_batch_size}条，已获取所有可用数据")
                        break

                    # 短暂延迟，避免请求过于频繁
                    if batch_count < max_batches:
                        time.sleep(self.mootdx_data_config['retry_delay'])

                except Exception as e:
                    logger.warning(f"⚠️ 第{batch_count}批次获取失败: {e}")
                    # 如果已经有数据，继续处理；否则重试
                    if not all_data:
                        continue
                    else:
                        break

            # 合并所有批次的数据
            if not all_data:
                logger.warning(f"⚠️ 批次获取失败：股票{stock_code}未获取到任何{data_type}数据")
                return pd.DataFrame()

            logger.info(f"📊 批次获取完成：共{batch_count}个批次，原始数据{total_collected}条")

            # 合并DataFrame
            combined_df = pd.concat(all_data, ignore_index=True)  # type: ignore

            # 数据去重和排序处理
            if self.mootdx_data_config['dedup_enabled']:
                # 基于datetime字段去重（保留最新的记录）
                if 'datetime' in combined_df.columns:
                    before_dedup = len(combined_df)
                    combined_df = combined_df.drop_duplicates(subset=['datetime'], keep='last')
                    after_dedup = len(combined_df)

                    if before_dedup != after_dedup:
                        logger.debug(f"🧹 去重处理：{before_dedup} -> {after_dedup} 条（移除{before_dedup - after_dedup}条重复）")

            # 按时间排序（确保数据按时间正序排列）
            if self.mootdx_data_config['sort_by_time'] and 'datetime' in combined_df.columns:
                combined_df = combined_df.sort_values('datetime').reset_index(drop=True)  # type: ignore
                logger.debug("📅 数据已按时间正序排列")

            # 截取目标数量的数据（取最新的N条）
            if len(combined_df) > target_limit:
                combined_df = combined_df.tail(target_limit).reset_index(drop=True)
                logger.debug(f"✂️ 截取最新{target_limit}条数据")

            final_count = len(combined_df)
            logger.info(f"✅ 批次获取最终结果：股票{stock_code}获得{final_count}条{data_type}数据")

            # 验证数据质量
            if final_count >= target_limit * 0.8:  # 至少获取到目标的80%
                logger.info(f"🎯 数据质量良好：获取{final_count}条，目标{target_limit}条（{final_count/target_limit*100:.1f}%）")
            else:
                logger.warning(f"⚠️ 数据量不足：获取{final_count}条，目标{target_limit}条（{final_count/target_limit*100:.1f}%）")

            return combined_df

        except Exception as e:
            logger.error(f"❌ 批次获取{data_type}数据异常：股票{stock_code}，错误: {e}")
            return pd.DataFrame()

    def _get_stock_data_intelligent_batches_mootdx(self, client: Any, symbol: str, frequency: int,
                                                  target_limit: int, data_type: str, stock_code: str) -> pd.DataFrame:
        """
        智能循环获取mootdx股票数据，真正突破800条限制，支持2000条历史数据获取

        核心策略：
        1. 基于测试发现：mootdx的offset参数可以获取不同数量的历史数据
        2. offset=100获取最新100条，offset=800获取最新800条，时间范围不同
        3. 通过计算需要的循环次数，使用不同的offset值获取不同时间段的数据
        4. 智能去重和合并，确保获得真正的2000条不重复历史数据

        技术实现：
        - 第1次：offset=800，获取最新800条（2022-03-18 到 2025-07-07）
        - 第2次：offset=1600，尝试获取更早的800条
        - 第3次：offset=2400，尝试获取更早的800条
        - 智能合并去重，获得最多2000条历史数据

        Args:
            client: mootdx客户端实例
            symbol: mootdx格式的股票代码
            frequency: 数据频率代码
            target_limit: 目标获取数据量（如2000条）
            data_type: 数据类型（'daily' 或 'minute'）
            stock_code: 原始股票代码（用于日志）

        Returns:
            pd.DataFrame: 智能合并后的历史数据，按时间正序排列
        """
        try:
            # 智能循环获取配置 - 优化版本，支持真正的2000条数据获取
            batch_size = self.mootdx_data_config['max_single_request']  # 800条
            # 动态计算批次数量，确保能获取到目标数量的数据
            max_batches = max(5, (target_limit // batch_size) + 3)  # 至少5批次，根据目标动态调整

            logger.info(f"🔄 开始智能循环获取{data_type}数据：股票{stock_code}，目标{target_limit}条")
            logger.debug(f"📊 智能配置：每批{batch_size}条，最多{max_batches}批次，支持2000条数据获取")

            all_data = []  # 存储所有批次的数据
            total_collected = 0  # 已收集的数据总量
            batch_count = 0  # 当前批次计数

            # 计算需要的批次数
            needed_batches = min(max_batches, (target_limit + batch_size - 1) // batch_size)
            logger.debug(f"📊 计算需要{needed_batches}个批次来获取{target_limit}条数据")

            # 智能循环获取多个批次的数据
            for batch_num in range(1, needed_batches + 1):
                batch_count = batch_num

                # 计算当前批次的offset值
                # 关键策略：使用递增的offset值来尝试获取更多历史数据
                current_offset = batch_size * batch_num

                logger.debug(f"📦 第{batch_count}批次：使用offset={current_offset}获取数据")

                try:
                    # 调用mootdx获取当前批次数据
                    batch_df = client.bars(  # type: ignore
                        symbol=symbol,
                        frequency=frequency,
                        offset=current_offset,
                        adjust=self.mootdx_data_config['adjust_type']
                    )

                    if batch_df is None or batch_df.empty:
                        logger.warning(f"⚠️ 第{batch_count}批次返回空数据，停止获取")
                        break

                    current_count = len(batch_df)
                    logger.debug(f"✅ 第{batch_count}批次获取成功：{current_count}条数据")

                    # 显示时间范围（如果有datetime字段）
                    if 'datetime' in batch_df.columns and not batch_df.empty:
                        earliest = batch_df['datetime'].min()
                        latest = batch_df['datetime'].max()
                        logger.debug(f"   时间范围: {earliest} 到 {latest}")

                    # 添加到总数据集
                    all_data.append(batch_df)  # type: ignore
                    total_collected += current_count

                    # 如果当前批次数据量小于期望的batch_size，说明已经获取到所有可用数据
                    if current_count < batch_size:
                        logger.info(f"📊 第{batch_count}批次数据不足{batch_size}条，已获取所有可用数据")
                        break

                    # 短暂延迟，避免请求过于频繁
                    if batch_count < needed_batches:
                        time.sleep(self.mootdx_data_config['retry_delay'])

                except Exception as e:
                    logger.warning(f"⚠️ 第{batch_count}批次获取失败: {e}")
                    # 如果已经有数据，继续处理；否则重试
                    if not all_data:
                        continue
                    else:
                        break

            # 合并所有批次的数据
            if not all_data:
                logger.warning(f"⚠️ 智能循环获取失败：股票{stock_code}未获取到任何{data_type}数据")
                return pd.DataFrame()

            logger.info(f"📊 智能循环获取完成：共{batch_count}个批次，原始数据{total_collected}条")

            # 合并DataFrame
            combined_df = pd.concat(all_data, ignore_index=True)  # type: ignore

            # 智能数据去重和排序处理
            if self.mootdx_data_config['dedup_enabled']:
                # 基于datetime字段去重（保留最新的记录）
                if 'datetime' in combined_df.columns:
                    before_dedup = len(combined_df)
                    combined_df = combined_df.drop_duplicates(subset=['datetime'], keep='last')
                    after_dedup = len(combined_df)

                    if before_dedup != after_dedup:
                        logger.debug(f"🧹 智能去重处理：{before_dedup} -> {after_dedup} 条（移除{before_dedup - after_dedup}条重复）")

            # 按时间排序（确保数据按时间正序排列）
            if self.mootdx_data_config['sort_by_time'] and 'datetime' in combined_df.columns:
                combined_df = combined_df.sort_values('datetime').reset_index(drop=True)  # type: ignore
                logger.debug("📅 数据已按时间正序排列")

            # 截取目标数量的数据（取最新的N条）
            if len(combined_df) > target_limit:
                combined_df = combined_df.tail(target_limit).reset_index(drop=True)
                logger.debug(f"✂️ 截取最新{target_limit}条数据")

            final_count = len(combined_df)
            logger.info(f"✅ 智能循环获取最终结果：股票{stock_code}获得{final_count}条{data_type}数据")

            # 验证数据质量和效果
            if final_count >= target_limit * 0.9:  # 至少获取到目标的90%
                logger.info(f"🎯 智能循环获取效果优秀：获取{final_count}条，目标{target_limit}条（{final_count/target_limit*100:.1f}%）")
            elif final_count > batch_size:  # 至少超过单次获取的限制
                logger.info(f"🎯 智能循环获取效果良好：获取{final_count}条，超过单次限制{batch_size}条")
            else:
                logger.warning(f"⚠️ 智能循环获取效果有限：获取{final_count}条，未超过单次限制")

            # 显示时间覆盖范围
            if 'datetime' in combined_df.columns and not combined_df.empty:
                earliest = combined_df['datetime'].min()  # type: ignore
                latest = combined_df['datetime'].max()  # type: ignore
                time_span_days = (pd.to_datetime(latest) - pd.to_datetime(earliest)).days  # type: ignore
                logger.info(f"📅 时间覆盖范围：{earliest} 到 {latest}（共{time_span_days}天）")

            return combined_df

        except Exception as e:
            logger.error(f"❌ 智能循环获取{data_type}数据异常：股票{stock_code}，错误: {e}")
            return pd.DataFrame()

    def _get_security_bars_data(self, client: Any, symbol: str, frequency: int, start: int, count: int, adjust_type: str = 'qfq') -> pd.DataFrame:
        """
        统一的get_security_bars数据获取方法

        Args:
            client: mootdx客户端
            symbol: 股票代码
            frequency: 频率代码（0=5分钟, 1=15分钟, 9=日线等）
            start: 起始位置
            count: 获取数量
            adjust_type: 复权类型

        Returns:
            pd.DataFrame: 获取的数据
        """
        try:
            # 参数保留用于接口兼容性
            _ = adjust_type

            # 确定市场代码 (0=深圳, 1=上海)
            market = 1 if symbol.startswith('6') else 0

            # 直接调用get_security_bars方法
            if hasattr(client, 'client') and hasattr(client.client, 'get_security_bars'):  # type: ignore
                raw_data = client.client.get_security_bars(  # type: ignore
                    category=frequency,
                    market=market,
                    code=symbol,
                    start=start,
                    count=count
                )

                if raw_data and len(raw_data) > 0:  # type: ignore
                    # 转换为DataFrame
                    df = client.client.to_df(raw_data)  # type: ignore

                    if not df.empty:  # type: ignore
                        # 简化的调试信息
                        logger.debug(f"📊 get_security_bars数据: {df.shape}, 列: {list(df.columns)}")  # type: ignore

                        if 'datetime' not in df.columns:  # type: ignore
                            logger.warning(f"⚠️ 缺少datetime列！可用列: {list(df.columns)}")  # type: ignore
                            return pd.DataFrame()

                        # 重命名列以匹配项目格式
                        column_mapping = {
                            'open': 'open',
                            'close': 'close',
                            'high': 'high',
                            'low': 'low',
                            'vol': 'volume',
                            'amount': 'amount',
                            'datetime': 'datetime'
                        }

                        # 只保留存在的列
                        existing_columns = {k: v for k, v in column_mapping.items() if k in df.columns}  # type: ignore
                        df = df.rename(columns=existing_columns)  # type: ignore

                        # 处理datetime字段但不设置为索引（保留为列）
                        if 'datetime' in df.columns:  # type: ignore
                            # 转换为datetime类型
                            df.loc[:, 'datetime'] = pd.to_datetime(df['datetime'], errors='coerce')  # type: ignore

                            # 检查转换结果
                            null_count = df['datetime'].isnull().sum()
                            if null_count > 0:
                                logger.warning(f"⚠️ datetime转换后有{null_count}个NULL值")
                                df = df.dropna(subset=['datetime'])

                            logger.debug(f"📊 datetime处理完成: {len(df)}条数据")

                        # 按时间排序
                        if 'datetime' in df.columns and not df.empty:
                            df = df.sort_values('datetime')

                        # 格式化价格数据为2位小数
                        price_columns = ['open', 'close', 'high', 'low']
                        for col in price_columns:
                            if col in df.columns:
                                df.loc[:, col] = df[col].round(2)

                        return df

            return pd.DataFrame()

        except Exception as e:
            logger.debug(f"⚠️ get_security_bars获取失败: {e}")
            return pd.DataFrame()

    def _get_stock_data_with_offset_mootdx(self, client: Any, symbol: str, frequency: int, target_limit: int, data_type: str, stock_code: str) -> pd.DataFrame:
        """
        使用偏移量策略获取股票数据 - 基于用户建议的偏移量方法

        实现思路：for i in range(10): data+=api.get_security_bars(9,0,'000001',(9-i)*800,800)
        通过不同的偏移量获取不重复的历史数据，真正实现2000+条数据获取

        支持日线数据和分钟数据的统一获取策略

        Args:
            client: mootdx客户端
            symbol: 股票代码（mootdx格式）
            frequency: 频率代码（0=5分钟, 1=15分钟, 9=日线等）
            target_limit: 目标数据量
            data_type: 数据类型描述（如'daily', '5minute', '15minute'）
            stock_code: 原始股票代码

        Returns:
            pd.DataFrame: 获取的股票数据
        """
        try:
            # 偏移量策略配置
            batch_size = self.mootdx_data_config['max_single_request']  # 800条
            # 计算需要的批次数量，确保能获取到目标数量的数据
            max_batches = max(3, (target_limit // batch_size) + 1)  # 至少3批次

            logger.info(f"🔄 开始偏移量获取{data_type}数据：股票{stock_code}，目标{target_limit}条")
            logger.debug(f"📊 偏移量配置：每批{batch_size}条，最多{max_batches}批次，使用get_security_bars直接调用")

            all_data = []
            total_collected = 0

            # 实现用户建议的偏移量策略：for i in range(10): data+=api.get_security_bars(9,0,'000001',(9-i)*800,800)
            for i in range(max_batches):
                # 用户建议的偏移量计算：i * batch_size
                # 第一批次：start=0，获取第0-799条
                # 第二批次：start=800，获取第800-1599条
                # 第三批次：start=1600，获取第1600-2399条
                start_position = i * batch_size

                logger.debug(f"📦 第{i+1}批次：起始位置{start_position}，获取{batch_size}条数据")

                try:
                    # 使用统一的get_security_bars方法
                    batch_df = self._get_security_bars_data(
                        client=client,
                        symbol=symbol,
                        frequency=frequency,
                        start=start_position,
                        count=batch_size,
                        adjust_type=self.mootdx_data_config['adjust_type']
                    )

                    if batch_df is None or batch_df.empty:  # type: ignore
                        logger.debug(f"⚠️ 第{i+1}批次返回空数据，起始位置{start_position}")
                        # 如果连续获取空数据，可能已经到达数据边界
                        if i > 0:  # 第一批次之后的空数据才停止
                            logger.info(f"📊 第{i+1}批次无数据，已获取所有可用历史数据")
                            break
                        continue

                    current_count = len(batch_df)
                    logger.debug(f"✅ 第{i+1}批次获取成功：{current_count}条数据，起始位置{start_position}")

                    # 清洗当前批次数据
                    if 'minute' in data_type:
                        cleaned_batch = self._clean_minute_data_mootdx(batch_df, stock_code, int(data_type.replace('minute', '')))
                    else:
                        # 日线数据清洗
                        cleaned_batch = self._clean_daily_data_mootdx(batch_df, stock_code)

                    if not cleaned_batch.empty:
                        all_data.append(cleaned_batch)  # type: ignore
                        total_collected += len(cleaned_batch)

                        logger.debug(f"📊 累计收集：{total_collected}条数据，当前批次{len(cleaned_batch)}条")

                        # 检查是否已达到目标数量（留一些余量）
                        if total_collected >= target_limit * 1.1:  # 110%的目标，确保有足够数据
                            logger.info(f"📊 累计数据已足够：{total_collected} >= {target_limit * 1.1:.0f}，提前停止以提升性能")
                            break

                    # 如果当前批次数据量小于期望的batch_size，说明已经获取到所有可用数据
                    if current_count < batch_size:
                        logger.info(f"📊 第{i+1}批次数据不足{batch_size}条，已获取所有可用数据")
                        break

                except Exception as batch_error:
                    logger.warning(f"⚠️ 第{i+1}批次获取失败：{batch_error}")
                    continue

            # 合并所有批次数据
            if all_data:
                combined_df = pd.concat(all_data, ignore_index=True)  # type: ignore

                # 偏移量策略不需要去重，因为每个偏移量获取的都是不同时间段的数据
                logger.debug(f"📊 偏移量策略合并完成：{len(combined_df)}条原始数据，无需去重")

                # 按时间排序（确保数据按时间正序排列）
                if 'trade_time' in combined_df.columns:
                    combined_df = combined_df.sort_values('trade_time').reset_index(drop=True)  # type: ignore
                elif 'datetime' in combined_df.columns:
                    combined_df = combined_df.sort_values('datetime').reset_index(drop=True)  # type: ignore

                # 如果数据超过目标，截取最新的数据
                if len(combined_df) > target_limit:
                    combined_df = combined_df.tail(target_limit).reset_index(drop=True)
                    logger.debug(f"📊 截取最新{target_limit}条数据")

                logger.info(f"✅ 偏移量策略获取最终结果：股票{stock_code}获得{len(combined_df)}条{data_type}数据")

                # 评估获取效果
                success_rate = len(combined_df) / target_limit * 100
                if success_rate >= 80:
                    logger.info(f"🎯 偏移量策略效果优秀：获取{len(combined_df)}条，目标{target_limit}条（{success_rate:.1f}%）")
                elif success_rate >= 50:
                    logger.info(f"🎯 偏移量策略效果良好：获取{len(combined_df)}条，目标{target_limit}条（{success_rate:.1f}%）")
                else:
                    logger.warning(f"⚠️ 偏移量策略效果有限：获取{len(combined_df)}条，目标{target_limit}条（{success_rate:.1f}%）")

                # 显示时间覆盖范围
                if 'trade_time' in combined_df.columns and not combined_df.empty:
                    earliest = combined_df['trade_time'].min()  # type: ignore
                    latest = combined_df['trade_time'].max()  # type: ignore
                    time_span = (latest - earliest).days  # type: ignore
                    logger.info(f"📅 时间覆盖范围：{earliest} 到 {latest}（共{time_span}天）")

                return combined_df
            else:
                logger.warning(f"⚠️ 偏移量策略获取失败：股票{stock_code}未获取到任何{data_type}数据")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"❌ 偏移量策略获取{data_type}数据失败：{e}")
            return pd.DataFrame()








    def _clean_daily_data_mootdx(self, df: pd.DataFrame, stock_code: str) -> pd.DataFrame:
        """
        清洗mootdx日线数据

        mootdx返回字段: datetime, open, close, high, low, vol, amount
        需要转换为标准格式: trade_time, stock_code, open, high, low, close, volume, amount

        Args:
            df: mootdx原始日线数据DataFrame
            stock_code: 股票代码

        Returns:
            pd.DataFrame: 清洗后的日线数据
        """
        if df.empty:
            return df

        try:
            # 创建副本避免修改原数据
            df_clean = df.copy()

            # 重命名列以匹配数据库表结构
            column_mapping = {
                'datetime': 'trade_time',  # 交易时间
            }

            # 应用列名映射
            for old_col, new_col in column_mapping.items():
                if old_col in df_clean.columns:
                    df_clean = df_clean.rename(columns={old_col: new_col})

            # 添加股票代码列
            df_clean['stock_code'] = stock_code

            # 时间字段处理 - 关键修复：确保trade_time字段不为NULL
            if 'trade_time' in df_clean.columns and not df_clean.empty:
                # 调试信息：检查原始时间数据
                logger.debug(f"📊 股票{stock_code}：原始时间数据类型: {df_clean['trade_time'].dtype}")
                logger.debug(f"📊 股票{stock_code}：原始时间样例: {df_clean['trade_time'].head(2).tolist()}")

                # 简化的时间转换 - 直奔主题
                try:
                    # 转换为datetime类型
                    df_clean.loc[:, 'trade_time'] = pd.to_datetime(df_clean['trade_time'], errors='coerce')  # type: ignore

                    # 过滤掉时间转换失败的记录
                    df_clean = df_clean.dropna(subset=['trade_time'])  # type: ignore

                    if df_clean.empty:
                        logger.warning(f"⚠️ 股票{stock_code}：时间转换后无有效数据")
                        return pd.DataFrame()

                    # 检查第一个值是否是有效的时间对象
                    first_time = df_clean['trade_time'].iloc[0]  # type: ignore
                    if not pd.isna(first_time) and hasattr(first_time, 'date'):  # type: ignore
                        logger.debug(f"✅ 股票{stock_code}：时间转换成功，样例: {first_time}")
                    else:
                        logger.error(f"❌ 股票{stock_code}：时间转换失败，无效的时间对象")
                        return pd.DataFrame()

                except Exception as time_convert_error:
                    logger.error(f"❌ 股票{stock_code}：时间转换异常: {time_convert_error}")
                    return pd.DataFrame()

                if not df_clean.empty:
                    # 根据用户偏好：日线数据的trade_time设置为日期+00:00:00格式
                    try:
                        # 直接标准化时间为日期格式（00:00:00）
                        # 由于数据已经是Timestamp对象，直接使用.dt.normalize()
                        df_clean.loc[:, 'trade_time'] = pd.to_datetime(df_clean['trade_time']).dt.normalize()  # type: ignore

                        logger.debug(f"✅ 股票{stock_code}：时间标准化成功")

                    except Exception as dt_error:
                        logger.error(f"❌ 股票{stock_code}：时间标准化失败: {dt_error}")
                        return pd.DataFrame()

                    # 再次验证时间字段不为空
                    df_clean = df_clean.dropna(subset=['trade_time'])  # type: ignore

                    if df_clean.empty:
                        logger.warning(f"⚠️ 股票{stock_code}：时间字段处理后无有效数据")
                        return pd.DataFrame()
            else:
                logger.error(f"❌ 股票{stock_code}：缺少trade_time字段或数据为空")
                return pd.DataFrame()

            # 数值字段处理
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                if col in df_clean.columns:
                    # 转换为数值类型
                    df_clean.loc[:, col] = pd.to_numeric(df_clean[col], errors='coerce')  # type: ignore

            # 价格字段精度控制：确保价格字段最多保留两位小数
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                if col in df_clean.columns:
                    df_clean.loc[:, col] = df_clean[col].round(2)

            # 过滤无效数据
            df_clean = df_clean.dropna(subset=['open', 'high', 'low', 'close'])  # type: ignore

            # 确保数据按时间正序排列（最早的在前）
            if 'trade_time' in df_clean.columns:
                df_clean = df_clean.sort_values('trade_time').reset_index(drop=True)  # type: ignore

            # 选择需要的列，确保顺序正确
            required_columns = ['trade_time', 'stock_code', 'open', 'high', 'low', 'close', 'volume', 'amount']
            available_columns = [col for col in required_columns if col in df_clean.columns]

            if available_columns:
                df_clean = df_clean[available_columns]

            # 最终验证：确保关键字段不为NULL（数据库约束要求）
            if not df_clean.empty:
                # 检查trade_time字段
                null_trade_time = df_clean['trade_time'].isnull().sum()
                if null_trade_time > 0:
                    logger.warning(f"⚠️ 股票{stock_code}：发现{null_trade_time}条trade_time为NULL的记录，将被过滤")
                    df_clean = df_clean.dropna(subset=['trade_time'])  # type: ignore

                # 检查stock_code字段
                null_stock_code = df_clean['stock_code'].isnull().sum()
                if null_stock_code > 0:
                    logger.warning(f"⚠️ 股票{stock_code}：发现{null_stock_code}条stock_code为NULL的记录，将被过滤")
                    df_clean = df_clean.dropna(subset=['stock_code'])  # type: ignore

                # 最终检查
                if df_clean.empty:
                    logger.warning(f"⚠️ 股票{stock_code}：数据清洗后无有效记录")
                    return pd.DataFrame()

            logger.debug(f"✅ mootdx日线数据清洗完成：股票{stock_code}，{len(df_clean)}条记录")
            return df_clean

        except Exception as e:
            logger.error(f"❌ 清洗mootdx日线数据失败：股票{stock_code}，错误: {e}")
            return pd.DataFrame()

    @suppress_warnings(['adata'])
    def _get_stock_daily_data_adata(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        使用adata获取股票日线数据

        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pd.DataFrame: 原始日线数据
        """
        try:
            # 使用adata获取股票日线数据 - 抑制adata内部的pandas链式赋值警告
            df = ad.stock.market.get_market(  # type: ignore
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date,
                k_type=1,  # 日线数据
                adjust_type=1  # 前复权
            )

            if df.empty:  # type: ignore
                return pd.DataFrame()

            # 数据清洗和转换 - 适配adata数据格式
            df = self._clean_daily_data_adata(df)  # type: ignore

            return df

        except Exception as e:
            logger.error(f"adata获取股票{stock_code}日线数据异常: {e}")
            raise



    def get_stock_minute_data(self, stock_code: str, start_date: str, end_date: str, period: int,
                            limit: Optional[int] = None, force_download: bool = False) -> pd.DataFrame:
        """
        获取股票分钟数据 - 使用mootdx数据源（通达信接口）

        通过mootdx获取股票分钟数据，支持多种分钟周期（1、5、15、30、60分钟）。
        mootdx单次最多返回800条数据，本方法通过循环请求突破此限制。

        Args:
            stock_code: 股票代码（如000001、600000等）
            start_date: 开始日期（格式：YYYY-MM-DD，用于日志显示）
            end_date: 结束日期（格式：YYYY-MM-DD，用于日志显示）
            period: 分钟周期（1, 5, 15, 30, 60）
            limit: 获取数据条数，None时使用默认配置
            force_download: 是否强制下载大量数据

        Returns:
            pd.DataFrame: 清洗后的分钟数据
        """
        try:
            # 参数保留用于接口兼容性和日志显示
            _ = start_date, end_date

            # 确定获取数据量
            if force_download:
                target_limit = self.mootdx_data_config['force_download_limit']
                logger.info(f"🔄 强制下载模式：获取股票{stock_code} {period}分钟数据{target_limit}条")
            elif limit is not None:
                target_limit = limit
            else:
                target_limit = self.mootdx_data_config['default_limit']

            # 获取分钟数据
            df = self._get_stock_minute_data_mootdx(stock_code, period, target_limit)

            if not df.empty:
                logger.debug(f"✅ 使用mootdx成功获取股票{stock_code} {period}分钟数据: {len(df)}条")
                return df
            else:
                logger.warning(f"⚠️ mootdx获取股票{stock_code} {period}分钟数据为空")

        except Exception as e:
            logger.warning(f"⚠️ mootdx获取股票{stock_code} {period}分钟数据失败: {e}")

        # mootdx数据源失败，记录失败股票
        self.failed_stocks['minute'].add(f"{stock_code}_{period}min")
        logger.error(f"❌ mootdx无法获取股票{stock_code}的{period}分钟数据")
        return pd.DataFrame()

    def _get_stock_minute_data_mootdx(self, stock_code: str, period: int, limit: int) -> pd.DataFrame:
        """
        使用mootdx获取股票分钟数据的具体实现

        支持循环获取大量历史数据，突破mootdx单次800条的限制

        Args:
            stock_code: 股票代码
            period: 分钟周期（1, 5, 15, 30, 60）
            limit: 需要获取的数据条数

        Returns:
            pd.DataFrame: 原始分钟数据
        """
        try:
            # 转换股票代码格式
            mootdx_code = self._convert_stock_code_for_mootdx(stock_code)

            # 创建mootdx客户端
            client = self._create_mootdx_client()
            if client is None:
                logger.error(f"❌ 无法创建mootdx客户端获取股票{stock_code} {period}分钟数据")
                return pd.DataFrame()

            # 获取分钟K线频率代码
            frequency = self._get_frequency_code(period=period, data_type='minute')

            # 如果需要的数据量小于等于800条，直接获取
            max_single_request = self.mootdx_data_config['max_single_request']
            if limit <= max_single_request:
                logger.debug(f"📊 直接获取股票{stock_code} {period}分钟数据{limit}条（最新数据）")

                # 修复：使用get_security_bars方法，确保获取最新的limit条数据
                df = self._get_security_bars_data(
                    client=client,
                    symbol=mootdx_code,
                    frequency=frequency,
                    start=0,  # 从最新数据开始
                    count=limit,  # 获取指定条数
                    adjust_type=self.mootdx_data_config['adjust_type']
                )

                if df is not None and not df.empty:  # type: ignore
                    # 数据清洗和格式转换
                    df = self._clean_minute_data_mootdx(df, stock_code, period)
                    logger.debug(f"✅ 成功获取股票{stock_code} {period}分钟数据: {len(df)}条")
                    return df
                else:
                    logger.warning(f"⚠️ mootdx返回空数据：股票{stock_code} {period}分钟")
                    return pd.DataFrame()

            # 需要获取大量分钟数据 - 优化版本，真正支持2000条历史数据
            # 优先使用时间分段获取策略，与日线数据保持一致，突破mootdx单次800条的限制
            logger.info(f"🔄 智能获取股票{stock_code} {period}分钟数据，目标{limit}条，单次限制{max_single_request}条")

            # 启用分钟数据偏移量策略获取大量历史数据（基于您的建议）
            # 使用偏移量策略：for i in range(10): data+=api.get_security_bars(9,0,'000001',(9-i)*800,800)
            if limit > max_single_request:
                logger.info(f"📊 启用分钟数据偏移量策略：目标{limit}条{period}分钟数据，将使用偏移量获取历史数据")
                df = self._get_stock_data_with_offset_mootdx(
                    client, mootdx_code, frequency, limit, f'{period}minute', stock_code
                )
            else:
                # 单次获取模式（限制在800条以内）
                actual_limit = min(limit, max_single_request)
                logger.debug(f"📊 单次获取模式：实际获取{actual_limit}条最新{period}分钟数据")

                # 使用统一的get_security_bars方法
                df = self._get_security_bars_data(
                    client=client,
                    symbol=mootdx_code,
                    frequency=frequency,
                    start=0,  # 从最新数据开始
                    count=actual_limit,
                    adjust_type=self.mootdx_data_config['adjust_type']
                )

            if df is not None and not df.empty:  # type: ignore
                logger.info(f"✅ 获取完成：股票{stock_code} {period}分钟共获取{len(df)}条数据")

                # 数据清洗和格式转换
                df = self._clean_minute_data_mootdx(df, stock_code, period)
                return df
            else:
                logger.warning(f"⚠️ 获取失败：股票{stock_code} {period}分钟未获取到任何数据")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"❌ mootdx获取股票{stock_code} {period}分钟数据异常: {e}")
            return pd.DataFrame()

    def _clean_minute_data_mootdx(self, df: pd.DataFrame, stock_code: str, period: int) -> pd.DataFrame:
        """
        清洗mootdx分钟数据

        mootdx返回字段: datetime, open, close, high, low, vol, amount
        需要转换为标准格式: trade_time, stock_code, open, high, low, close, volume, amount

        Args:
            df: mootdx原始分钟数据DataFrame
            stock_code: 股票代码
            period: 分钟周期

        Returns:
            pd.DataFrame: 清洗后的分钟数据
        """
        if df.empty:
            return df

        try:
            # 创建副本避免修改原数据
            df_clean = df.copy()

            # 重命名列以匹配数据库表结构
            # mootdx返回的字段: datetime, open, close, high, low, vol, amount, year, month, day, hour, minute, volume
            # 需要的字段: trade_time, stock_code, open, high, low, close, volume, amount

            column_mapping = {
                'datetime': 'trade_time',  # 交易时间
                # vol字段已经被mootdx自动转换为volume，无需重命名
                # open, close, high, low, amount 保持不变
            }

            # 应用列名映射
            for old_col, new_col in column_mapping.items():
                if old_col in df_clean.columns:
                    df_clean = df_clean.rename(columns={old_col: new_col})

            # 添加股票代码列
            df_clean['stock_code'] = stock_code

            # 时间字段处理 - 关键修复：确保trade_time字段不为NULL
            if 'trade_time' in df_clean.columns and not df_clean.empty:
                # 确保时间格式正确
                df_clean.loc[:, 'trade_time'] = pd.to_datetime(df_clean['trade_time'], errors='coerce')  # type: ignore

                # 过滤掉时间转换失败的记录
                df_clean = df_clean.dropna(subset=['trade_time'])  # type: ignore

                if df_clean.empty:
                    logger.warning(f"⚠️ 股票{stock_code}：{period}分钟数据时间字段处理后无有效数据")
                    return pd.DataFrame()
            else:
                logger.error(f"❌ 股票{stock_code}：{period}分钟数据缺少trade_time字段或数据为空")
                return pd.DataFrame()

            # 数值字段处理
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                if col in df_clean.columns:
                    # 转换为数值类型
                    df_clean.loc[:, col] = pd.to_numeric(df_clean[col], errors='coerce')  # type: ignore

            # 价格字段精度控制：确保价格字段最多保留两位小数
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                if col in df_clean.columns:
                    df_clean.loc[:, col] = df_clean[col].round(2)

            # 过滤无效数据
            df_clean = df_clean.dropna(subset=['open', 'high', 'low', 'close'])  # type: ignore

            # 确保数据按时间正序排列（最早的在前）
            if 'trade_time' in df_clean.columns:
                df_clean = df_clean.sort_values('trade_time').reset_index(drop=True)  # type: ignore

            # 选择需要的列，确保顺序正确
            required_columns = ['trade_time', 'stock_code', 'open', 'high', 'low', 'close', 'volume', 'amount']
            available_columns = [col for col in required_columns if col in df_clean.columns]

            if available_columns:
                df_clean = df_clean[available_columns]

            # 最终验证：确保关键字段不为NULL（数据库约束要求）
            if not df_clean.empty:
                # 检查trade_time字段
                null_trade_time = df_clean['trade_time'].isnull().sum()
                if null_trade_time > 0:
                    logger.warning(f"⚠️ 股票{stock_code}：{period}分钟数据发现{null_trade_time}条trade_time为NULL的记录，将被过滤")
                    df_clean = df_clean.dropna(subset=['trade_time'])  # type: ignore

                # 检查stock_code字段
                null_stock_code = df_clean['stock_code'].isnull().sum()
                if null_stock_code > 0:
                    logger.warning(f"⚠️ 股票{stock_code}：{period}分钟数据发现{null_stock_code}条stock_code为NULL的记录，将被过滤")
                    df_clean = df_clean.dropna(subset=['stock_code'])  # type: ignore

                # 最终检查
                if df_clean.empty:
                    logger.warning(f"⚠️ 股票{stock_code}：{period}分钟数据清洗后无有效记录")
                    return pd.DataFrame()

            logger.debug(f"✅ mootdx {period}分钟数据清洗完成：股票{stock_code}，{len(df_clean)}条记录")
            return df_clean

        except Exception as e:
            logger.error(f"❌ 清洗mootdx {period}分钟数据失败：股票{stock_code}，错误: {e}")
            return pd.DataFrame()

    @suppress_warnings(['adata'])
    def _get_stock_minute_data_adata(self, stock_code: str, start_date: str, end_date: str, period: int) -> pd.DataFrame:
        """
        使用adata获取股票分钟数据

        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            period: 分钟周期

        Returns:
            pd.DataFrame: 原始分钟数据
        """
        try:
            # adata的k_type映射：5分钟=5, 15分钟=15, 30分钟=30, 60分钟=60
            k_type_mapping = {5: 5, 15: 15, 30: 30, 60: 60}

            if period not in k_type_mapping:
                logger.warning(f"adata不支持{period}分钟周期，跳过")
                return pd.DataFrame()

            # 使用adata获取股票分钟数据 - 抑制adata内部的pandas链式赋值警告
            df = ad.stock.market.get_market(  # type: ignore
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date,
                k_type=k_type_mapping[period],  # 分钟数据
                adjust_type=1  # 前复权
            )

            if df.empty:  # type: ignore
                return pd.DataFrame()

            # 数据清洗和转换 - 适配adata数据格式
            df = self._clean_minute_data_adata(df)  # type: ignore

            return df

        except Exception as e:
            logger.error(f"adata获取股票{stock_code} {period}分钟数据异常: {e}")
            raise



    @retry_on_error(retries=3, delay=1)
    def get_stock_tick_data(self) -> pd.DataFrame:
        """获取股票实时数据 - 已优化，使用mootdx数据源"""
        try:
            logger.info("📊 股票实时数据功能已优化为使用mootdx数据源")
            # TODO: 实现基于mootdx的股票实时数据获取
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取股票实时数据失败: {e}")
            return pd.DataFrame()

    @retry_on_error(retries=3, delay=1)
    def get_concept_stocks(self, stock_code: str) -> bool:
        """获取股票概念信息"""
        try:

            try:
                concept_data = ad.stock.info.get_concept_ths(stock_code)
                if concept_data is not None and not concept_data.empty:
                    # 添加股票代码
                    concept_data['stock_code'] = stock_code

                    # 重命名列
                    concept_data.rename(columns={
                        'concept_code': 'index_code',
                        'name': 'concept_name',
                        'reason': 'reason'
                    }, inplace=True)

                    # 从stock_info表查询股票名称
                    try:
                        stock_name_result = self.db.execute_query(  # type: ignore
                            "SELECT stock_name FROM stock_info WHERE stock_code = %s",
                            (stock_code,)
                        )
                        if stock_name_result:
                            stock_name = stock_name_result[0][0]  # type: ignore
                        else:
                            stock_name = f"股票{stock_code}"  # 默认名称
                    except Exception as e:
                        logger.warning(f"查询股票 {stock_code} 名称失败: {e}")
                        stock_name = f"股票{stock_code}"  # 默认名称

                    # 添加股票名称
                    concept_data['stock_name'] = stock_name

                    # 按指定顺序重新组织字段：index_code, concept_name, stock_code, stock_name, reason
                    concept_data = concept_data[['index_code', 'concept_name', 'stock_code', 'stock_name', 'reason']]

                    result = self.db.batch_insert_df(
                        'concept_stocks',
                        concept_data,
                        conflict_fields=['index_code', 'stock_code'],
                        enable_dedup=True
                    )

                    return result > 0
            except:
                pass

            logger.debug(f"股票 {stock_code} 无概念数据")
            return False

        except Exception as e:
            logger.debug(f"获取股票 {stock_code} 概念信息失败: {e}")
            return False

    @retry_on_error(retries=3, delay=1)
    def get_all_concept_code_ths(self) -> Union[pd.DataFrame, bool]:
        """
        获取同花顺所有概念信息
        :return:
        """
        try:
            con_info = ad.stock.info.all_concept_code_ths()  # type: ignore
            con_info.rename(columns={  # type: ignore
                'name': 'concept_name'
            }, inplace=True)
            con_info = con_info[['index_code', 'concept_name', 'concept_code']]  # type: ignore
            # 修复链式赋值警告：使用df.loc[]方式避免链式赋值，确保pandas 3.0兼容性
            # 去除概念名称中的空格，提高数据质量
            con_info.loc[:, 'concept_name'] = con_info['concept_name'].str.replace(' ', '')  # type: ignore
            con_info.dropna(inplace=True)  # type: ignore

            _ = self.db.batch_insert_df(  # type: ignore
                'concept_info',
                con_info,  # type: ignore
                conflict_fields=['concept_code'],
                enable_dedup=True
            )

            logger.info(f"✅ 概念信息采集完成: {len(con_info)} 条记录")  # type: ignore
            return con_info  # type: ignore

        except Exception as e:
            logger.error(f"❌ 获取概念信息失败: {e}")
            return pd.DataFrame()

    def _clean_daily_data_adata(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗adata日线数据

        adata返回字段: stock_code, trade_time, trade_date, open, close, high, low,
                     volume, amount, change, change_pct, turnover_ratio, pre_close

        Args:
            df: adata原始日线数据DataFrame

        Returns:
            pd.DataFrame: 清洗后的日线数据
        """
        if df.empty:
            return df

        # adata数据已经是标准格式，只需要基本清洗
        # 修复链式赋值警告：处理时间字段
        if 'trade_time' in df.columns:
            df.loc[:, 'trade_time'] = pd.to_datetime(df['trade_time'])  # type: ignore

        # 修复链式赋值警告：数据类型转换
        # 使用df.loc[]方式进行数值类型转换，提高性能和兼容性
        numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'change_pct']
        existing_numeric_columns = [col for col in numeric_columns if col in df.columns]
        if existing_numeric_columns:
            for col in existing_numeric_columns:
                df.loc[:, col] = pd.to_numeric(df[col], errors='coerce')  # type: ignore

        # 价格字段精度控制：确保价格字段最多保留两位小数
        df = round_price_fields(df, ['open', 'high', 'low', 'close'], decimal_places=2)

        # 过滤无效数据
        df = df.dropna(subset=['open', 'high', 'low', 'close'])  # type: ignore

        # 选择需要的列 - 只保留数据库需要的字段
        required_columns = ['trade_time', 'stock_code', 'open', 'high', 'low', 'close', 'volume', 'amount']
        available_columns = [col for col in required_columns if col in df.columns]

        return df[available_columns] if available_columns else df



    def _clean_minute_data_adata(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗adata分钟数据

        adata返回字段: stock_code, trade_time, trade_date, open, close, high, low,
                     volume, amount, change, change_pct, turnover_ratio, pre_close

        Args:
            df: adata原始分钟数据DataFrame

        Returns:
            pd.DataFrame: 清洗后的分钟数据
        """
        if df.empty:
            return df

        # adata数据已经是标准格式，只需要基本清洗
        # 修复链式赋值警告：处理时间字段
        if 'trade_time' in df.columns:
            df.loc[:, 'trade_time'] = pd.to_datetime(df['trade_time'])  # type: ignore

        # 修复链式赋值警告：数据类型转换
        # 使用df.loc[]方式进行数值类型转换，提高性能和兼容性
        numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
        existing_numeric_columns = [col for col in numeric_columns if col in df.columns]
        if existing_numeric_columns:
            for col in existing_numeric_columns:
                df.loc[:, col] = pd.to_numeric(df[col], errors='coerce')  # type: ignore

        # 价格字段精度控制：确保价格字段最多保留两位小数
        df = round_price_fields(df, ['open', 'high', 'low', 'close'], decimal_places=2)

        # 过滤无效数据
        df = df.dropna(subset=['open', 'high', 'low', 'close'])  # type: ignore

        # 选择需要的列 - 只保留数据库需要的字段
        required_columns = ['trade_time', 'stock_code', 'open', 'high', 'low', 'close', 'volume', 'amount']
        available_columns = [col for col in required_columns if col in df.columns]

        return df[available_columns] if available_columns else df



    def retry_failed_stocks(self, start_date: str, end_date: str, data_type: str = 'both') -> Dict[str, int]:
        """
        重试获取失败的股票数据

        Args:
            start_date: 开始日期
            end_date: 结束日期
            data_type: 数据类型 ('daily', 'minute', 'both')

        Returns:
            Dict[str, int]: 重试结果统计
        """
        retry_stats = {
            'daily_retry_count': 0,
            'daily_success_count': 0,
            'minute_retry_count': 0,
            'minute_success_count': 0
        }

        # 重试日线数据
        if data_type in ['daily', 'both'] and self.failed_stocks['daily']:
            failed_daily_stocks = list(self.failed_stocks['daily'])
            retry_stats['daily_retry_count'] = len(failed_daily_stocks)
            logger.info(f"🔄 开始重试 {len(failed_daily_stocks)} 只失败的日线数据股票")

            # 清空失败记录，重新尝试
            self.failed_stocks['daily'].clear()

            with tqdm(total=len(failed_daily_stocks), desc="重试日线数据") as pbar:
                for stock_code in failed_daily_stocks:
                    try:
                        daily_data = self.get_stock_daily_data(stock_code, start_date, end_date)
                        if not daily_data.empty:
                            # 保存到数据库
                            self.db.batch_insert_df(
                                'stock_kline_day',
                                daily_data,
                                conflict_fields=['stock_code', 'trade_time'],
                                enable_dedup=True
                            )
                            retry_stats['daily_success_count'] += 1
                    except Exception as e:
                        logger.error(f"重试股票 {stock_code} 日线数据失败: {e}")
                    finally:
                        pbar.update(1)

        # 重试分钟数据
        if data_type in ['minute', 'both'] and self.failed_stocks['minute']:
            failed_minute_stocks = list(self.failed_stocks['minute'])
            retry_stats['minute_retry_count'] = len(failed_minute_stocks)
            logger.info(f"🔄 开始重试 {len(failed_minute_stocks)} 只失败的分钟数据股票")

            # 清空失败记录，重新尝试
            self.failed_stocks['minute'].clear()

            with tqdm(total=len(failed_minute_stocks), desc="重试分钟数据") as pbar:
                for stock_period in failed_minute_stocks:
                    try:
                        # 解析股票代码和周期
                        stock_code, period_str = stock_period.rsplit('_', 1)
                        period = int(period_str.replace('min', ''))

                        minute_data = self.get_stock_minute_data(stock_code, start_date, end_date, period)
                        if not minute_data.empty:
                            # 保存到数据库
                            table_name = f'stock_kline_{period}min'
                            self.db.batch_insert_df(
                                table_name,
                                minute_data,
                                conflict_fields=['stock_code', 'trade_time'],
                                enable_dedup=True
                            )
                            retry_stats['minute_success_count'] += 1
                    except Exception as e:
                        logger.error(f"重试股票 {stock_period} 分钟数据失败: {e}")
                    finally:
                        pbar.update(1)

        # 输出重试结果
        if retry_stats['daily_retry_count'] > 0:
            daily_success_rate = retry_stats['daily_success_count'] / retry_stats['daily_retry_count'] * 100
            logger.info(f"📊 日线数据重试完成: 重试{retry_stats['daily_retry_count']}只，成功{retry_stats['daily_success_count']}只，成功率{daily_success_rate:.2f}%")

        if retry_stats['minute_retry_count'] > 0:
            minute_success_rate = retry_stats['minute_success_count'] / retry_stats['minute_retry_count'] * 100
            logger.info(f"📊 分钟数据重试完成: 重试{retry_stats['minute_retry_count']}只，成功{retry_stats['minute_success_count']}只，成功率{minute_success_rate:.2f}%")

        return retry_stats

    def get_failed_stocks_summary(self) -> Dict[str, Any]:
        """
        获取失败股票汇总信息

        Returns:
            Dict[str, Any]: 失败股票汇总
        """
        return {
            'daily_failed_count': len(self.failed_stocks['daily']),
            'daily_failed_stocks': list(self.failed_stocks['daily']),
            'minute_failed_count': len(self.failed_stocks['minute']),
            'minute_failed_stocks': list(self.failed_stocks['minute']),
            'total_failed_count': len(self.failed_stocks['daily']) + len(self.failed_stocks['minute'])
        }

    def collect_daily_data_only(self, start_date: str, end_date: str, stock_codes: Optional[List[str]] = None,
                              force_download: bool = False):
        """
        每日数据更新任务 - 只采集日线数据（优化版本）

        专门用于每日15:30的增量更新任务：
        - 只采集日线数据，不包含分钟数据
        - 默认获取当天数据（1条记录）
        - 高效、快速的增量更新

        Args:
            start_date: 开始日期（格式：YYYY-MM-DD）
            end_date: 结束日期（格式：YYYY-MM-DD）
            stock_codes: 股票代码列表，为None时使用全部股票
            force_download: 是否强制下载历史数据（默认False，只获取当天数据）
        """
        if stock_codes is None:
            stock_codes = self.stock_codes

        if not stock_codes:
            logger.error("❌ 没有可采集的股票代码")
            return

        # 确定数据获取模式
        if force_download:
            data_limit = self.mootdx_data_config['force_download_limit']
            mode_desc = f"强制下载模式（{data_limit}条历史数据）"
        else:
            data_limit = 1  # 每日更新只获取1条当天数据
            mode_desc = f"每日增量模式（{data_limit}条当天数据）"

        logger.info(f"🚀 开始每日日线数据更新：{len(stock_codes)} 只股票")
        logger.info(f"📊 数据获取模式：{mode_desc}")
        logger.info(f"📅 更新日期范围：{start_date} 到 {end_date}")

        # 统计信息
        daily_success_count = 0
        total_attempts = len(stock_codes)

        # 重置失败股票记录
        self.failed_stocks['daily'].clear()

        with tqdm(total=len(stock_codes), desc="每日日线数据更新") as pbar:
            for i, stock_code in enumerate(stock_codes):
                try:
                    # 检查是否需要进行数据源轮换
                    if i > 0:  # 第一只股票不检查轮换
                        self.increment_processed_count()
                        rotated = self.rotate_data_source()
                        if rotated:
                            # 更新进度条描述，显示当前使用的数据源
                            current_primary = self.rotation_config['current_primary']
                            pbar.set_description(f"每日日线数据更新 (主数据源: {current_primary})")

                    # 只采集日线数据 - 传递force_download参数
                    daily_data = self.get_stock_daily_data(
                        stock_code, start_date, end_date,
                        limit=data_limit,  # 明确指定数据量
                        force_download=force_download
                    )

                    if not daily_data.empty:
                        # 插入数据库
                        inserted_count = self.db.batch_insert_df(
                            'stock_kline_day',
                            daily_data,
                            conflict_fields=['stock_code', 'trade_time'],
                            enable_dedup=True
                        )
                        daily_success_count += 1

                        # 记录详细信息（仅在调试模式下）
                        if logger.isEnabledFor(logging.DEBUG):
                            logger.debug(f"✅ 股票{stock_code}：获取{len(daily_data)}条，插入{inserted_count}条")

                except Exception as e:
                    logger.error(f"❌ 采集股票 {stock_code} 日线数据失败: {e}")

                finally:
                    pbar.update(1)

        # 输出统计结果
        success_rate = (daily_success_count / total_attempts) * 100 if total_attempts > 0 else 0

        logger.info(f"📊 每日日线数据更新完成统计：")
        logger.info(f"   总股票数：{total_attempts}")
        logger.info(f"   成功数量：{daily_success_count}")
        logger.info(f"   成功率：{success_rate:.1f}%")

        if self.failed_stocks['daily']:
            failed_count = len(self.failed_stocks['daily'])
            logger.warning(f"⚠️ 失败股票数：{failed_count}")
            logger.debug(f"   失败股票列表：{list(self.failed_stocks['daily'])[:10]}...")  # 只显示前10个

    def collect_daily_incremental_data(self, start_date: str, end_date: str, stock_codes: Optional[List[str]] = None,
                                     force_download: bool = False):
        """
        股票数据更新 - 单循环高效版本（支持增量和强制下载模式）

        根据force_download参数自动选择数据获取模式：
        - force_download=False: 每日增量更新模式
          * 日线数据（1条记录/股票）
          * 5分钟数据（约48条记录/股票）
          * 15分钟数据（约16条记录/股票）
          * 股票评分数据（1条记录/股票）
        - force_download=True: 强制下载模式
          * 日线数据（2000条历史记录/股票）
          * 5分钟数据（2000条历史记录/股票）
          * 15分钟数据（2000条历史记录/股票）
          * 股票评分数据（1条记录/股票）

        Args:
            start_date: 开始日期（格式：YYYY-MM-DD）
            end_date: 结束日期（格式：YYYY-MM-DD）
            stock_codes: 股票代码列表，为None时使用全部股票
            force_download: 是否强制下载历史数据（默认False为增量模式）
        """
        if stock_codes is None:
            stock_codes = self.stock_codes

        if not stock_codes:
            logger.error("❌ 没有可采集的股票代码")
            return

        # 根据force_download参数确定数据获取模式和数据量
        if force_download:
            # 强制下载模式：获取2000条历史数据
            daily_limit = self.mootdx_data_config['force_download_limit']
            minute_5_limit = self.mootdx_data_config['force_download_limit']
            minute_15_limit = self.mootdx_data_config['force_download_limit']
            mode_desc = f"强制下载模式（{daily_limit}条历史数据）"
            progress_desc = "强制下载股票数据"
        else:
            # 每日增量模式：获取当天数据
            daily_limit = 1      # 日线：1条当天数据
            minute_5_limit = 48  # 5分钟：约48条当天数据
            minute_15_limit = 16 # 15分钟：约16条当天数据
            mode_desc = f"每日增量模式（日线1条+5分钟{minute_5_limit}条+15分钟{minute_15_limit}条）"
            progress_desc = "每日股票数据增量更新"

        logger.info(f"🚀 开始股票数据更新（单循环高效版本）：{len(stock_codes)} 只股票")
        logger.info(f"📊 数据模式：{mode_desc}")
        logger.info(f"📅 更新日期：{start_date} 到 {end_date}")

        # 统计信息
        stats = {
            'daily': 0,      # 日线成功数
            'minute_5': 0,   # 5分钟成功数
            'minute_15': 0,  # 15分钟成功数
            'risk': 0,       # 评分成功数
            'total': len(stock_codes)
        }

        # 重置失败股票记录
        self.failed_stocks['daily'].clear()

        with tqdm(total=len(stock_codes), desc=progress_desc) as pbar:
            for i, stock_code in enumerate(stock_codes):
                try:
                    # 检查是否需要进行数据源轮换
                    if i > 0:  # 第一只股票不检查轮换
                        self.increment_processed_count()
                        rotated = self.rotate_data_source()
                        if rotated:
                            current_primary = self.rotation_config['current_primary']
                            pbar.set_description(f"每日股票数据增量更新 (主数据源: {current_primary})")

                    # 1. 获取日线数据（根据模式动态调整数据量）
                    try:
                        if force_download:
                            # 强制下载模式：不传递limit参数，让force_download=True生效
                            daily_data = self.get_stock_daily_data(
                                stock_code, start_date, end_date,
                                force_download=True  # 强制下载模式，获取2000条历史数据
                            )
                        else:
                            # 增量模式：传递小的limit参数
                            daily_data = self.get_stock_daily_data(
                                stock_code, start_date, end_date,
                                limit=daily_limit,  # 增量模式：1条当天数据
                                force_download=False
                            )

                        if not daily_data.empty:
                            self.db.batch_insert_df(
                                'stock_kline_day',
                                daily_data,
                                conflict_fields=['stock_code', 'trade_time'],
                                enable_dedup=True
                            )
                            stats['daily'] += 1
                            logger.debug(f"✅ {stock_code} 日线: {len(daily_data)}条")
                    except Exception as e:
                        logger.debug(f"❌ {stock_code} 日线失败: {e}")

                    # 2. 获取5分钟数据（根据模式动态调整数据量）
                    try:
                        if force_download:
                            # 强制下载模式：不传递limit参数，让force_download=True生效
                            minute_5_data = self.get_stock_minute_data(
                                stock_code, start_date, end_date, period=5,
                                force_download=True  # 强制下载模式，获取2000条历史数据
                            )
                        else:
                            # 增量模式：传递小的limit参数
                            minute_5_data = self.get_stock_minute_data(
                                stock_code, start_date, end_date, period=5,
                                limit=minute_5_limit,  # 增量模式：48条当天数据
                                force_download=False
                            )

                        if not minute_5_data.empty:
                            self.db.batch_insert_df(
                                'stock_kline_5min',
                                minute_5_data,
                                conflict_fields=['stock_code', 'trade_time'],
                                enable_dedup=True
                            )
                            stats['minute_5'] += 1
                            logger.debug(f"✅ {stock_code} 5分钟: {len(minute_5_data)}条")
                    except Exception as e:
                        logger.debug(f"❌ {stock_code} 5分钟失败: {e}")

                    # 3. 获取15分钟数据（根据模式动态调整数据量）
                    try:
                        if force_download:
                            # 强制下载模式：不传递limit参数，让force_download=True生效
                            minute_15_data = self.get_stock_minute_data(
                                stock_code, start_date, end_date, period=15,
                                force_download=True  # 强制下载模式，获取2000条历史数据
                            )
                        else:
                            # 增量模式：传递小的limit参数
                            minute_15_data = self.get_stock_minute_data(
                                stock_code, start_date, end_date, period=15,
                                limit=minute_15_limit,  # 增量模式：16条当天数据
                                force_download=False
                            )

                        if not minute_15_data.empty:
                            self.db.batch_insert_df(
                                'stock_kline_15min',
                                minute_15_data,
                                conflict_fields=['stock_code', 'trade_time'],
                                enable_dedup=True
                            )
                            stats['minute_15'] += 1
                            logger.debug(f"✅ {stock_code} 15分钟: {len(minute_15_data)}条")
                    except Exception as e:
                        logger.debug(f"❌ {stock_code} 15分钟失败: {e}")

                    # 4. 获取股票评分数据（1条记录）
                    try:
                        risk_count = self.collect_stock_risk(stock_code)
                        if risk_count > 0:
                            stats['risk'] += 1
                            logger.debug(f"✅ {stock_code} 评分: {risk_count}条")
                    except Exception as e:
                        logger.debug(f"❌ {stock_code} 评分失败: {e}")

                except Exception as e:
                    logger.error(f"❌ 股票 {stock_code} 增量更新失败: {e}")

                finally:
                    pbar.update(1)

        # 输出统计结果
        logger.info(f"📊 每日股票数据增量更新完成：")
        logger.info(f"   日线数据：{stats['daily']}/{stats['total']} ({stats['daily']/stats['total']*100:.1f}%)")
        logger.info(f"   5分钟数据：{stats['minute_5']}/{stats['total']} ({stats['minute_5']/stats['total']*100:.1f}%)")
        logger.info(f"   15分钟数据：{stats['minute_15']}/{stats['total']} ({stats['minute_15']/stats['total']*100:.1f}%)")
        logger.info(f"   评分数据：{stats['risk']}/{stats['total']} ({stats['risk']/stats['total']*100:.1f}%)")



    def collect_concept_kline_incremental_data(self, start_date: str, end_date: str):
        """
        每日增量概念K线数据更新 - 只获取当天概念K线数据

        专门用于每日15:30的增量更新：
        - 概念日线K线数据（当天数据）
        - 概念5分钟K线数据（当天约48条记录）
        - 概念15分钟K线数据（当天约16条记录）
        - 不包含概念信息表和概念成分股数据（这些在每周三更新）

        Args:
            start_date: 开始日期（格式：YYYY-MM-DD）
            end_date: 结束日期（格式：YYYY-MM-DD）
        """
        try:
            logger.info(f"🚀 开始每日概念K线增量更新")
            logger.info(f"📊 数据模式：概念日线+分钟K线（当天数据）")
            logger.info(f"📅 更新日期：{start_date} 到 {end_date}")

            # 获取所有概念代码
            try:
                index_code_results = self.db.execute_query("SELECT index_code FROM concept_info")  # type: ignore
                index_codes = [str(code[0]) for code in index_code_results]  # type: ignore

                if not index_codes:
                    logger.warning("⚠️ 未找到概念代码，跳过概念K线更新")
                    return

                logger.info(f"📊 找到 {len(index_codes)} 个概念，开始更新K线数据")

            except Exception as e:
                logger.error(f"❌ 获取概念代码失败: {e}")
                return

            # 统计信息
            daily_success_count = 0
            minute_success_count = 0
            total_concepts = len(index_codes)

            # 使用进度条显示更新进度
            with tqdm(total=total_concepts, desc="概念K线增量更新") as pbar:
                for index_code in index_codes:
                    try:
                        # 1. 更新概念日线K线数据（当天1条记录）
                        try:
                            self.get_concept_ths_daily_data_incremental(index_code, start_date, end_date)
                            daily_success_count += 1
                        except Exception as e:
                            logger.debug(f"概念 {index_code} 日线K线更新失败: {e}")

                        # 2. 更新概念分钟K线数据（当天约64条记录：48+16）
                        try:
                            self.collect_concept_min_data_incremental(index_code, start_date, end_date)
                            minute_success_count += 1
                        except Exception as e:
                            logger.debug(f"概念 {index_code} 分钟K线更新失败: {e}")

                    except Exception as e:
                        logger.debug(f"概念 {index_code} K线更新失败: {e}")

                    finally:
                        pbar.update(1)

            # 输出统计结果
            daily_success_rate = (daily_success_count / total_concepts) * 100 if total_concepts > 0 else 0
            minute_success_rate = (minute_success_count / total_concepts) * 100 if total_concepts > 0 else 0

            logger.info(f"📊 每日概念K线增量更新完成：")
            logger.info(f"   概念日线：{daily_success_count}/{total_concepts} ({daily_success_rate:.1f}%)")
            logger.info(f"   概念分钟线：{minute_success_count}/{total_concepts} ({minute_success_rate:.1f}%)")

        except Exception as e:
            logger.error(f"❌ 每日概念K线增量更新失败: {e}")
            raise

    def get_concept_ths_daily_data_incremental(self, index_code: str, start_date: str, end_date: str):
        """
        获取概念日线K线数据（增量模式）- 只获取当天数据

        专门用于每日15:30增量更新：
        - 只获取指定日期范围的概念日线K线数据
        - 避免获取全部历史数据

        Args:
            index_code: 概念指数代码
            start_date: 开始日期（格式：YYYY-MM-DD）
            end_date: 结束日期（格式：YYYY-MM-DD）
        """
        try:
            # 使用adata获取概念日线数据，但只获取指定日期范围
            con_daily_data = ad.stock.market.get_market_concept_ths(index_code=index_code)

            # 检查数据是否有效
            if con_daily_data is None or con_daily_data.empty:  # type: ignore
                logger.debug(f"概念指数 {index_code} 的日线K线数据为空")
                return

            # 安全的时间转换和过滤
            try:
                # 多步骤强制转换trade_time为datetime类型
                # 步骤1：转换为字符串
                time_series = con_daily_data['trade_time'].astype(str)  # type: ignore

                # 步骤2：转换为datetime
                datetime_series = pd.to_datetime(time_series, errors='coerce')  # type: ignore

                # 步骤3：如果还是object类型，创建新的Series
                if not pd.api.types.is_datetime64_any_dtype(datetime_series):  # type: ignore
                    logger.warning(f"⚠️ 概念指数 {index_code} 第一次转换失败，尝试重新创建Series")
                    datetime_series = pd.Series(pd.to_datetime(time_series.values, errors='coerce'), index=time_series.index)  # type: ignore

                # 步骤4：赋值回DataFrame
                con_daily_data = con_daily_data.copy()  # type: ignore
                con_daily_data['trade_time'] = datetime_series  # type: ignore

                # 检查时间转换是否成功
                if con_daily_data['trade_time'].isna().all():  # type: ignore
                    logger.error(f"❌ 概念指数 {index_code} 时间字段转换失败，所有值都是NaT")
                    return

                # 删除时间转换失败的行
                con_daily_data = con_daily_data.dropna(subset=['trade_time'])  # type: ignore

                if con_daily_data.empty:  # type: ignore
                    logger.debug(f"概念指数 {index_code} 时间转换后无有效数据")
                    return

                # 最终验证 trade_time 是 datetime 类型
                if not pd.api.types.is_datetime64_any_dtype(con_daily_data['trade_time']):  # type: ignore
                    logger.error(f"❌ 概念指数 {index_code} 最终验证失败，类型: {con_daily_data['trade_time'].dtype}")  # type: ignore
                    return

                # 标准化时间为日期格式（00:00:00）
                con_daily_data.loc[:, 'trade_time'] = con_daily_data['trade_time'].dt.normalize()  # type: ignore

                # 只保留指定日期范围的数据（增量模式）
                start_dt = pd.to_datetime(start_date)  # type: ignore
                end_dt = pd.to_datetime(end_date)  # type: ignore
                con_daily_data = con_daily_data[  # type: ignore
                    (con_daily_data['trade_time'] >= start_dt) &
                    (con_daily_data['trade_time'] <= end_dt)
                ]

                if con_daily_data.empty:  # type: ignore
                    logger.debug(f"概念指数 {index_code} 在 {start_date} 到 {end_date} 期间无数据")
                    return

            except Exception as time_error:
                logger.error(f"❌ 概念指数 {index_code} 时间处理失败: {time_error}")
                import traceback
                logger.debug(traceback.format_exc())
                return

            # 数据清洗和格式化
            con_daily_data.loc[:, 'index_code'] = index_code  # type: ignore

            # 价格字段精度控制：确保价格字段最多保留两位小数
            con_daily_data = round_price_fields(con_daily_data, ['open', 'high', 'low', 'close'], decimal_places=2)  # type: ignore

            # 数据类型转换
            con_daily_data.loc[:, 'volume'] = pd.to_numeric(con_daily_data['volume'], errors='coerce').fillna(0).astype(int)  # type: ignore
            con_daily_data.loc[:, 'index_code'] = con_daily_data['index_code'].astype(str)

            # 删除不需要的字段
            if 'trade_date' in con_daily_data.columns:
                con_daily_data = con_daily_data.drop('trade_date', axis=1)

            # 处理 NaN 值
            con_daily_data = con_daily_data.fillna(0)  # type: ignore

            # 保存到数据库
            if not con_daily_data.empty:
                self.db.batch_insert_df(
                    'concept_daily_data',
                    con_daily_data,
                    conflict_fields=['index_code', 'trade_time'],
                    enable_dedup=True
                )
                logger.debug(f"✅ 概念 {index_code} 日线增量更新: {len(con_daily_data)} 条")

        except Exception as e:
            logger.debug(f"概念指数 {index_code} 日线增量更新失败: {e}")
            raise

    def collect_concept_min_data_incremental(self, index_code: str, start_date: str, end_date: str):
        """
        获取概念分钟K线数据（增量模式）- 只获取当天数据

        专门用于每日15:30增量更新：
        - 只获取指定日期范围的概念分钟K线数据
        - 生成5分钟和15分钟K线
        - 避免获取全部历史数据

        Args:
            index_code: 概念指数代码
            start_date: 开始日期（格式：YYYY-MM-DD）
            end_date: 结束日期（格式：YYYY-MM-DD）
        """
        try:
            # 获取1分钟K线数据
            df = ad.stock.market.get_market_concept_min_ths(index_code)
            if df is None or df.empty:  # type: ignore
                logger.debug(f"概念指数 {index_code} 的分钟K线数据为空")
                return

            # 安全的时间转换和过滤
            try:
                # 确保trade_time是datetime类型
                df.loc[:, 'trade_time'] = pd.to_datetime(df['trade_time'], errors='coerce')  # type: ignore

                # 检查时间转换是否成功
                if df['trade_time'].isna().all():  # type: ignore
                    logger.error(f"❌ 概念指数 {index_code} 分钟数据时间字段转换失败，所有值都是NaT")
                    return

                # 删除时间转换失败的行
                df = df.dropna(subset=['trade_time'])  # type: ignore

                if df.empty:  # type: ignore
                    logger.debug(f"概念指数 {index_code} 分钟数据时间转换后无有效数据")
                    return

                # 只保留指定日期范围的数据（增量模式）
                start_dt = pd.to_datetime(start_date)  # type: ignore
                end_dt = pd.to_datetime(end_date) + pd.Timedelta(days=1)  # type: ignore  # 包含当天全部时间
                df = df[  # type: ignore
                    (df['trade_time'] >= start_dt) &
                    (df['trade_time'] < end_dt)
                ]

                if df.empty:  # type: ignore
                    logger.debug(f"概念指数 {index_code} 在 {start_date} 期间无分钟数据")
                    return

            except Exception as time_error:
                logger.error(f"❌ 概念指数 {index_code} 分钟数据时间处理失败: {time_error}")
                return

            # 数据清洗 - 确保所有数值列都是数值类型
            numeric_columns = ['volume', 'amount', 'price']
            for col in numeric_columns:
                if col in df.columns:  # type: ignore
                    df.loc[:, col] = pd.to_numeric(df[col], errors='coerce').fillna(0)  # type: ignore
                    # 验证转换是否成功
                    if not pd.api.types.is_numeric_dtype(df[col]):  # type: ignore
                        logger.warning(f"⚠️ 概念指数 {index_code} 列 {col} 数值转换失败，类型: {df[col].dtype}")  # type: ignore
                        df.loc[:, col] = 0  # type: ignore  # 设置默认值

            # 转换单位（volume 和 amount 转换为百万）
            for col in ['volume', 'amount']:
                if col in df.columns and pd.api.types.is_numeric_dtype(df[col]):  # type: ignore
                    df.loc[:, col] = (df[col] / 1000000).round(2)  # type: ignore  # 转换为百万

            # 重命名列
            df.rename(columns={'price': 'close'}, inplace=True)  # type: ignore

            # 确保 close 列也是数值类型
            if 'close' in df.columns:  # type: ignore
                df.loc[:, 'close'] = pd.to_numeric(df['close'], errors='coerce').fillna(0)  # type: ignore

            # 生成不同周期K线（5分钟、15分钟）
            for period in [5, 15]:
                # 按周期聚合数据
                period_df = df.groupby(  # type: ignore
                    pd.Grouper(key='trade_time', freq=f'{period}min', closed='right', label='right')
                ).agg({
                    'close': 'last',
                    'volume': 'sum',
                    'amount': 'sum'
                }).dropna().reset_index()  # type: ignore

                if period_df.empty:  # type: ignore
                    logger.debug(f"概念指数 {index_code} 的 {period} 分钟K线数据为空")
                    continue

                # 安全地排除 9:30 的数据
                try:
                    # 确保 trade_time 是 datetime 类型
                    if not pd.api.types.is_datetime64_any_dtype(period_df['trade_time']):  # type: ignore
                        period_df.loc[:, 'trade_time'] = pd.to_datetime(period_df['trade_time'], errors='coerce')  # type: ignore

                    # 排除 9:30 的数据
                    period_df = period_df[~((period_df['trade_time'].dt.hour == 9) & (period_df['trade_time'].dt.minute == 30))]  # type: ignore

                    if period_df.empty:  # type: ignore
                        logger.debug(f"概念指数 {index_code} 的 {period} 分钟K线数据过滤后为空")
                        continue

                except Exception as filter_error:
                    logger.error(f"❌ 概念指数 {index_code} {period}分钟数据过滤失败: {filter_error}")
                    continue

                # 添加必要字段
                period_df.loc[:, 'index_code'] = index_code  # type: ignore
                period_df.loc[:, 'period'] = period  # type: ignore

                # 安全的时间处理
                try:
                    period_df.loc[:, 'trade_time'] = pd.to_datetime(period_df['trade_time'], errors='coerce')  # type: ignore

                    # 检查时间转换是否成功
                    if period_df['trade_time'].isna().all():  # type: ignore
                        logger.error(f"❌ 概念指数 {index_code} {period}分钟数据时间转换失败，所有值都是NaT")
                        continue

                    # 删除时间转换失败的行
                    period_df = period_df.dropna(subset=['trade_time'])  # type: ignore

                    if period_df.empty:  # type: ignore
                        logger.debug(f"概念指数 {index_code} {period}分钟数据时间转换后无有效数据")
                        continue

                    # 只对没有时区的数据添加时区
                    if period_df['trade_time'].dt.tz is None:  # type: ignore
                        period_df.loc[:, 'trade_time'] = period_df['trade_time'].dt.tz_localize('Asia/Shanghai')  # type: ignore
                except Exception as time_error:
                    logger.error(f"❌ 概念指数 {index_code} {period}分钟数据时间处理失败: {time_error}")
                    continue

                # 保存到数据库
                if not period_df.empty:  # type: ignore
                    self.db.batch_insert_df(
                        'concept_min_data',
                        period_df,  # type: ignore
                        conflict_fields=['index_code', 'period', 'trade_time'],
                        enable_dedup=True
                    )
                    logger.debug(f"✅ 概念 {index_code} {period}分钟增量更新: {len(period_df)} 条")  # type: ignore

        except Exception as e:
            logger.debug(f"概念指数 {index_code} 分钟增量更新失败: {e}")
            raise

    def collect_stock_data(self, start_date: str, end_date: str, stock_codes: Optional[List[str]] = None,
                         force_download: bool = False):
        """
        采集股票数据（日线和分钟数据）- 使用mootdx数据源（通达信接口）

        使用mootdx数据源获取股票的日线和分钟数据，支持循环获取大量历史数据。
        默认获取一天的数据（约240条分钟数据），强制下载模式可获取2000条数据。

        Args:
            start_date: 开始日期（格式：YYYY-MM-DD，用于日志显示）
            end_date: 结束日期（格式：YYYY-MM-DD，用于日志显示）
            stock_codes: 股票代码列表，为None时使用全部股票
            force_download: 是否强制下载大量数据（2000条）
        """
        if stock_codes is None:
            stock_codes = self.stock_codes

        if not stock_codes:
            logger.error("没有可采集的股票代码")
            return

        # 确定数据获取模式
        if force_download:
            data_limit = self.mootdx_data_config['force_download_limit']
            mode_desc = f"强制下载模式（{data_limit}条）"
        else:
            data_limit = self.mootdx_data_config['default_limit']
            mode_desc = f"默认模式（{data_limit}条）"

        logger.info(f"🚀 开始采集 {len(stock_codes)} 只股票的数据（使用mootdx数据源）")
        logger.info(f"📊 数据源配置: {' -> '.join(self.data_source_priority)}")
        logger.info(f"📋 采集模式: {mode_desc}")
        logger.info(f"📋 mootdx配置:")
        logger.info(f"   单次最大请求: {self.mootdx_data_config['max_single_request']} 条")
        logger.info(f"   复权类型: {self.mootdx_data_config['adjust_type']}")
        logger.info(f"   重试次数: {self.mootdx_data_config['retry_count']}")

        # 显示轮换配置信息（已禁用）
        if self.rotation_config['enabled']:
            logger.info(f"🔄 数据源轮换已启用:")
            logger.info(f"   轮换批次大小: {self.rotation_config['batch_size']} 只股票")
            logger.info(f"   预计轮换次数: {len(stock_codes) // self.rotation_config['batch_size']} 次")
        else:
            logger.info("⏸️ 数据源轮换已禁用（mootdx单一数据源）")

        # 清空之前的失败记录
        self.failed_stocks['daily'].clear()
        self.failed_stocks['minute'].clear()

        daily_success_count = 0
        minute_success_count = 0
        total_minute_attempts = 0

        with tqdm(total=len(stock_codes), desc="采集股票数据") as pbar:
            for i, stock_code in enumerate(stock_codes):
                try:
                    # 检查是否需要进行数据源轮换
                    # 每处理完一只股票后检查轮换条件
                    if i > 0:  # 第一只股票不检查轮换
                        self.increment_processed_count()
                        rotated = self.rotate_data_source()
                        if rotated:
                            # 更新进度条描述，显示当前使用的数据源
                            current_primary = self.rotation_config['current_primary']
                            pbar.set_description(f"采集股票数据 (主数据源: {current_primary})")

                    # 采集日线数据 - 传递force_download参数
                    daily_data = self.get_stock_daily_data(
                        stock_code, start_date, end_date,
                        force_download=force_download
                    )
                    if not daily_data.empty:
                        self.db.batch_insert_df(
                            'stock_kline_day',
                            daily_data,
                            conflict_fields=['stock_code', 'trade_time'],
                            enable_dedup=True
                        )
                        daily_success_count += 1

                    # 采集分钟数据 - 传递force_download参数
                    for period in self.minute_periods:
                        total_minute_attempts += 1
                        minute_data = self.get_stock_minute_data(
                            stock_code, start_date, end_date, period,
                            force_download=force_download
                        )
                        if not minute_data.empty:
                            table_name = f'stock_kline_{period}min'
                            self.db.batch_insert_df(
                                table_name,
                                minute_data,
                                conflict_fields=['stock_code', 'trade_time'],
                                enable_dedup=True
                            )
                            minute_success_count += 1

                    # 采集概念数据
                    self.get_concept_stocks(stock_code)

                    # 更新股票风险指数
                    self.collect_stock_risk(stock_code)

                except Exception as e:
                    logger.error(f"采集股票 {stock_code} 数据失败: {e}")

                finally:
                    # 更新进度条
                    pbar.update(1)

                    # 每100只股票显示一次轮换状态（避免日志过多）
                    if (i + 1) % 100 == 0 and self.rotation_config['enabled']:
                        rotation_status = self.get_rotation_status()
                        logger.debug(f"📊 轮换进度: {rotation_status['processed_count']}/{rotation_status['batch_size']} "
                                   f"({rotation_status['progress_percentage']:.1f}%), "
                                   f"距离下次轮换还有 {rotation_status['next_rotation_in']} 只股票")

        # 计算成功率
        daily_success_rate = daily_success_count / len(stock_codes) * 100 if stock_codes else 0
        minute_success_rate = minute_success_count / total_minute_attempts * 100 if total_minute_attempts else 0

        logger.info(f"📊 数据采集完成:")
        logger.info(f"   日线数据: 成功 {daily_success_count}/{len(stock_codes)}, 成功率 {daily_success_rate:.2f}%")
        logger.info(f"   分钟数据: 成功 {minute_success_count}/{total_minute_attempts}, 成功率 {minute_success_rate:.2f}%")

        # 显示数据源轮换统计
        if self.rotation_config['enabled']:
            rotation_status = self.get_rotation_status()
            logger.info(f"🔄 数据源轮换统计:")
            logger.info(f"   总轮换次数: {rotation_status['current_batch']} 次")
            logger.info(f"   最终主数据源: {rotation_status['current_primary']}")
            logger.info(f"   最终数据源优先级: {' -> '.join(rotation_status['current_priority'])}")

            # 显示轮换历史（最近3次）
            if self.rotation_config['rotation_history']:
                recent_rotations = self.rotation_config['rotation_history'][-3:]
                logger.info(f"   最近轮换记录:")
                for record in recent_rotations:
                    logger.info(f"     批次{record['batch']}: {record['primary_source']} -> {record['backup_source']} "
                              f"({record['timestamp']})")

        # 显示失败统计
        failed_summary = self.get_failed_stocks_summary()
        if failed_summary['total_failed_count'] > 0:
            logger.warning(f"⚠️ 失败统计: 日线失败 {failed_summary['daily_failed_count']} 只, 分钟失败 {failed_summary['minute_failed_count']} 只")
            logger.info(f"💡 可以稍后调用 retry_failed_stocks() 方法重试失败的股票")
        else:
            logger.info(f"🎉 所有股票数据采集成功！")

    def collect_minute_data(self, start_date: str, end_date: str, stock_codes: Optional[List[str]] = None, force_download: bool = False):
        """
        单独采集分钟数据 - 支持智能数据源轮换和强制下载模式

        Args:
            start_date: 开始日期
            end_date: 结束日期
            stock_codes: 股票代码列表，为None时使用全部股票
            force_download: 是否强制下载大量历史数据（2000条），默认False使用240条
        """
        if stock_codes is None:
            stock_codes = self.stock_codes  # 限制数量，避免过长时间

        # 重置轮换计数器
        self.reset_rotation_counter()

        logger.info(f"🚀 开始采集 {len(stock_codes)} 只股票的分钟数据（支持智能数据源轮换）")

        if self.rotation_config['enabled']:
            logger.info(f"🔄 数据源轮换已启用，每 {self.rotation_config['batch_size']} 只股票轮换一次")

        with tqdm(total=len(stock_codes), desc="采集分钟数据") as pbar:
            for i, stock_code in enumerate(stock_codes):
                try:
                    # 检查是否需要进行数据源轮换
                    if i > 0:  # 第一只股票不检查轮换
                        self.increment_processed_count()
                        rotated = self.rotate_data_source()
                        if rotated:
                            # 更新进度条描述，显示当前使用的数据源
                            current_primary = self.rotation_config['current_primary']
                            pbar.set_description(f"采集分钟数据 (主数据源: {current_primary})")

                    # 采集分钟数据 - 传递force_download参数，支持强制下载2000条历史数据
                    for period in self.minute_periods:
                        minute_data = self.get_stock_minute_data(
                            stock_code, start_date, end_date, period,
                            force_download=force_download  # 关键修复：传递强制下载参数
                        )
                        if not minute_data.empty:
                            table_name = f'stock_kline_{period}min'
                            self.db.batch_insert_df(
                                table_name,
                                minute_data,
                                conflict_fields=['stock_code', 'trade_time'],
                                enable_dedup=True
                            )

                    # 采集概念数据
                    self.get_concept_stocks(stock_code)
                    # 更新股票风险指数
                    self.collect_stock_risk(stock_code)

                    # time.sleep(0.1)

                except Exception as e:
                    logger.error(f"采集股票 {stock_code} 分钟数据失败: {e}")

                finally:
                    pbar.update(1)

    def collect_concept_data(self, stock_codes: Optional[List[str]] = None):
        """单独采集概念数据"""
        if stock_codes is None:
            stock_codes = self.stock_codes

        for stock_code in stock_codes:
            try:
                self.get_concept_stocks(stock_code)
                # time.sleep(0.1)
            except Exception as e:
                logger.debug(f"采集股票 {stock_code} 概念数据失败: {e}")

    def collect_stock_risk(self, stock_code: str) -> int:
        """采集股票风险数据"""
        try:
            # 获取通达信风险评估得分
            risk_assessment = ad.sentiment.mine.mine_clearance_tdx(stock_code)
            if risk_assessment is None or risk_assessment.empty:
                return 0

            score = risk_assessment['score'][0]  # type: ignore
            if score:
                # 将numpy数据类型转换为Python int类型
                if hasattr(score, 'item'):  # type: ignore
                    score = int(score.item())  # type: ignore  # 将numpy类型转换为Python int
                else:
                    score = int(score)  # type: ignore  # 确保是Python int类型

                self.db.update_stock_score(
                    stock_code,
                    score
                )
                return score
            return 0  # 如果score为空或False，返回0
        except Exception as e:
            logger.error(f"获取通达信风险评估得分时出错: {e}")
            return 0
        
    # 下载指数数据
    def collect_index_data(self, start_date: str, end_date: str):
        """采集指数数据"""
        try:
            for index_code, _ in self._index_codes.items():
                    # 采集日线数据
                    self._collect_single_index_daily_data(index_code, start_date, end_date)
                    # 采集分钟数据
                    self._collect_single_index_minute(index_code)
        except Exception as e:
            logger.info(f"指数下载失败: {e}")

    @retry_on_error(retries=3, delay=1)
    def _collect_single_index_daily_data(self, index_code: str, start_date: str, end_date: str):
        """采集指数日线数据 - 使用adata数据源"""

        try:
            index_name = self._index_codes.get(index_code, f"指数{index_code}")

            # 使用adata获取指数日线数据
            df = ad.stock.market.get_market_index(  # type: ignore
                index_code=index_code,
                start_date=start_date,
                k_type=1  # 日线数据
            )

            if df.empty:  # type: ignore
                logger.warning(f"指数 {index_code} 没有数据")
                return

            # adata返回的列名: ['index_code', 'trade_date', 'trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount', 'change', 'change_pct']
            # 根据数据库表结构处理数据

            # 修复链式赋值警告：添加指数名称
            df.loc[:, 'index_name'] = index_name  # type: ignore

            # 修复链式赋值警告：处理时间格式
            # 使用df.loc[]方式避免链式赋值，确保pandas 3.0兼容性
            try:
                # 创建完全独立的副本
                df = df.copy(deep=True)  # type: ignore

                # 强制转换时间字段 - 使用更强制的方法
                try:
                    # 方法1：直接转换
                    df['trade_time'] = pd.to_datetime(df['trade_time'], errors='coerce')  # type: ignore

                    # 如果还是object类型，尝试强制转换
                    if df['trade_time'].dtype == 'object':  # type: ignore
                        # 方法2：重新创建DataFrame
                        df = pd.DataFrame(df.values, columns=df.columns, index=df.index)  # type: ignore
                        df['trade_time'] = pd.to_datetime(df['trade_time'], errors='coerce')  # type: ignore

                    # 最终验证
                    if not pd.api.types.is_datetime64_any_dtype(df['trade_time']):  # type: ignore
                        logger.warning(f"⚠️ 指数 {index_code} 时间字段类型异常，尝试强制转换: {df['trade_time'].dtype}")  # type: ignore
                        # 方法3：强制重建Series
                        time_values = pd.to_datetime(df['trade_time'].astype(str), errors='coerce')  # type: ignore
                        df = df.drop('trade_time', axis=1)  # type: ignore
                        df['trade_time'] = time_values  # type: ignore

                except Exception as convert_error:
                    logger.error(f"❌ 指数 {index_code} 时间转换失败: {convert_error}")
                    return

                # 检查转换是否成功
                if df['trade_time'].isna().all():  # type: ignore
                    logger.error(f"❌ 指数 {index_code} 时间字段转换失败，所有值都是NaT")
                    return

                # 最终验证转换结果
                if not pd.api.types.is_datetime64_any_dtype(df['trade_time']):  # type: ignore
                    logger.error(f"❌ 指数 {index_code} 时间转换最终验证失败: {df['trade_time'].dtype}")  # type: ignore
                    return

                # 添加时区信息（只对没有时区的数据添加）
                if df['trade_time'].dt.tz is None:  # type: ignore
                    df['trade_time'] = df['trade_time'].dt.tz_localize('Asia/Shanghai')  # type: ignore

                logger.debug(f"✅ 指数 {index_code} 时间处理成功")

            except Exception as time_error:
                logger.error(f"❌ 指数 {index_code} 时间处理失败: {time_error}")
                import traceback
                logger.error(traceback.format_exc())
                return

            # 过滤日期范围 - 优化时间范围过滤逻辑
            start_dt = pd.to_datetime(start_date).tz_localize('Asia/Shanghai')  # type: ignore
            end_dt = pd.to_datetime(end_date).tz_localize('Asia/Shanghai')  # type: ignore
            df = df[(df['trade_time'] >= start_dt) & (df['trade_time'] <= end_dt)]  # type: ignore

            if df.empty:  # type: ignore
                logger.warning(f"指数 {index_code} 在指定日期范围内没有数据")
                return

            # 根据数据库表结构添加缺失字段
            # 数据库字段: trade_time, index_code, index_name, open, close, high, low, pre_close, volume, amount, tick_volume, tick_amount, volume_ratio, change, change_pct, amplitude, turnover

            # 修复链式赋值警告：计算前收盘价和其他衍生字段
            # 使用df.loc[]方式进行字段计算和赋值
            df.loc[:, 'pre_close'] = df['close'] - df['change']  # type: ignore

            # 添加缺失字段（使用默认值或计算值） - 使用df.loc[]避免链式赋值警告
            df.loc[:, 'tick_volume'] = df['volume']  # type: ignore  # tick成交量等于成交量
            df.loc[:, 'tick_amount'] = df['amount']  # type: ignore  # tick成交额等于成交额
            df.loc[:, 'volume_ratio'] = 1.0  # type: ignore  # 默认量比
            df.loc[:, 'amplitude'] = ((df['high'] - df['low']) / df['pre_close'] * 100).round(2)  # type: ignore  # 振幅计算
            df.loc[:, 'turnover'] = 0.0  # type: ignore  # 默认换手率（指数无换手率概念）

            # 修复链式赋值警告：数据类型转换
            # 使用df.loc[]方式进行数值类型转换，提高性能和兼容性
            numeric_columns = ['open', 'high', 'low', 'close', 'pre_close', 'volume', 'amount',
                             'tick_volume', 'tick_amount', 'volume_ratio', 'change', 'change_pct',
                             'amplitude', 'turnover']
            for col in numeric_columns:
                if col in df.columns:  # type: ignore
                    # 使用df.loc[]避免链式赋值警告
                    df.loc[:, col] = pd.to_numeric(df[col], errors='coerce')  # type: ignore

            # 价格字段精度控制：确保价格字段最多保留两位小数
            df = round_price_fields(df, ['open', 'high', 'low', 'close', 'pre_close'], decimal_places=2)  # type: ignore

            # 删除不需要的字段
            if 'trade_date' in df.columns:  # type: ignore
                df = df.drop('trade_date', axis=1)  # type: ignore

            # 保存到数据库
            self.db.batch_insert_df(
                'index_daily_data',
                df,
                conflict_fields=['index_code', 'trade_time']
            )
            logger.info(f"✅ 指数 {index_name}({index_code}) 日线数据采集完成: {len(df)} 条记录")  # type: ignore

        except Exception as e:
            logger.error(f"❌ 采集指数 {index_code} 日线数据失败: {e}")
            raise

    @retry_on_error(retries=3, delay=1)
    def _collect_single_index_minute(self, index_code: str) -> pd.DataFrame:
        """获取单个指数的分钟数据 - 使用adata数据源

        Args:
            index_code: 指数代码

        Returns:
            处理后的DataFrame
        """
        try:
            # 1. 使用adata获取指数分钟数据
            df = ad.stock.market.get_market_index_min(index_code)  # type: ignore

            if df.empty:  # type: ignore
                logger.warning(f"指数 {index_code} 分钟数据为空")
                return pd.DataFrame()

            # adata返回的列名: ['index_code', 'trade_time', 'trade_date', 'price', 'avg_price', 'volume', 'amount', 'change', 'change_pct']
            # 根据数据库表结构处理数据
            # 数据库字段: trade_time, index_code, period, open, close, high, low, volume, amount

            # 2. 修复链式赋值警告：数据预处理
            # 使用df.loc[]方式避免链式赋值，确保pandas 3.0兼容性
            try:
                # 先转换为datetime类型
                df.loc[:, 'trade_time'] = pd.to_datetime(df['trade_time'], errors='coerce')  # type: ignore

                # 检查转换是否成功
                if df['trade_time'].isna().all():  # type: ignore
                    logger.error(f"❌ 指数 {index_code} 分钟数据时间字段转换失败")
                    return pd.DataFrame()

                # 过滤交易时间（9:30 到 15:00）
                df = df.set_index('trade_time')  # type: ignore

                # 添加时区信息（只对没有时区的数据添加）
                if df.index.tz is None:  # type: ignore
                    df.index = df.index.tz_localize('Asia/Shanghai')  # type: ignore

                df = df.between_time('09:30', '15:00')  # type: ignore

            except Exception as time_error:
                logger.error(f"❌ 指数 {index_code} 分钟数据时间处理失败: {time_error}")
                return pd.DataFrame()

            if df.empty:  # type: ignore
                logger.warning(f"指数 {index_code} 在交易时间内没有数据")
                return pd.DataFrame()

            # 初始化结果容器
            all_period_dfs = []

            # 3. 多周期处理 - 根据数据库表结构
            for period in [5, 15]:
                # 生成周期字符串（如5分钟转为'5min'）
                freq = f'{period}min'

                # 重采样处理 - 使用price字段作为OHLC数据源
                # 向后合并：label='right' 表示使用区间的结束时间作为标签
                resampled = df.resample(  # type: ignore
                    freq,
                    label='right',
                    closed='right'
                ).agg({
                    'price': ['first', 'max', 'min', 'last'],  # 开高低收
                    'volume': 'sum',
                    'amount': 'sum'
                })

                # 处理多级列名
                resampled.columns = ['open', 'high', 'low', 'close', 'volume', 'amount']

                # 添加元数据
                resampled = resampled.reset_index()  # type: ignore
                resampled['index_code'] = index_code  # type: ignore
                resampled['period'] = period  # type: ignore  # 添加周期标识

                # 过滤掉无效数据
                resampled = resampled.dropna(subset=['open', 'close'])  # type: ignore

                # 过滤掉不完整的区间 - 确保向后合并的正确性
                if not resampled.empty:  # type: ignore
                    # 计算第一个完整区间的时间
                    # 对于5分钟：第一个完整区间是9:35, 9:40, 9:45...
                    # 对于15分钟：第一个完整区间是9:45, 10:00, 10:15...

                    # 获取交易日开始时间 (9:30)
                    trading_start = resampled['trade_time'].iloc[0].replace(hour=9, minute=30, second=0, microsecond=0)  # type: ignore

                    # 计算第一个完整区间的结束时间
                    first_complete_time = trading_start + pd.Timedelta(minutes=period)  # type: ignore

                    # 只保留完整区间的数据
                    resampled = resampled[resampled['trade_time'] >= first_complete_time]  # type: ignore

                if not resampled.empty:  # type: ignore
                    all_period_dfs.append(resampled)  # type: ignore

            if not all_period_dfs:
                logger.warning(f"指数 {index_code} 重采样后没有有效数据")
                return pd.DataFrame()

            # 4. 合并数据
            merged_df = pd.concat(all_period_dfs, ignore_index=True).sort_values('trade_time')  # type: ignore

            # 5. 修复链式赋值警告：数据类型处理
            # 处理时间（确保时区正确） - 使用df.loc[]避免链式赋值警告
            try:
                merged_df.loc[:, 'trade_time'] = pd.to_datetime(merged_df['trade_time'], errors='coerce')  # type: ignore
                # 只对有时区的数据进行转换
                if merged_df['trade_time'].dt.tz is not None:  # type: ignore
                    merged_df.loc[:, 'trade_time'] = merged_df['trade_time'].dt.tz_convert('Asia/Shanghai')  # type: ignore
                else:
                    # 如果没有时区，先添加时区
                    merged_df.loc[:, 'trade_time'] = merged_df['trade_time'].dt.tz_localize('Asia/Shanghai')  # type: ignore
            except Exception as time_error:
                logger.error(f"❌ 指数 {index_code} 分钟数据时区处理失败: {time_error}")
                return pd.DataFrame()

            # 修复链式赋值警告：转换数值类型 - 只保留数据库表需要的字段
            # 使用df.loc[]方式进行数值类型转换，提高性能和兼容性
            numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_cols:
                if col in merged_df.columns:  # type: ignore
                    # 使用df.loc[]避免链式赋值警告
                    merged_df.loc[:, col] = pd.to_numeric(merged_df[col], errors='coerce')  # type: ignore

            # 6. 数据清洗
            merged_df = merged_df.dropna(subset=['open', 'close'])  # type: ignore

            # 7. 只保留数据库表需要的字段
            required_columns = ['trade_time', 'index_code', 'period', 'open', 'close', 'high', 'low', 'volume', 'amount']
            available_columns = [col for col in required_columns if col in merged_df.columns]
            merged_df = merged_df[available_columns]

            # 8. 保存到数据库
            if not merged_df.empty:
                try:
                    self.db.batch_insert_df(
                        'index_min_data',
                        merged_df,
                        conflict_fields=['trade_time', 'index_code', 'period'],
                        enable_dedup=True
                    )
                    logger.info(f"✅ 指数 {index_code} 分钟数据采集完成: {len(merged_df)} 条记录")
                except Exception as db_error:
                    logger.error(f"❌ 保存指数 {index_code} 分钟数据到数据库失败: {db_error}")
                    raise

            return merged_df

        except Exception as e:
            logger.error(f"❌ 获取指数 {index_code} 分钟数据失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return pd.DataFrame()

    @retry_on_error(retries=3, delay=1)
    def get_index_tick(self) -> pd.DataFrame:
        """获取指数实时数据 - 使用adata数据源"""
        try:
            all_index_data = []

            for index_code, index_name in self._index_codes.items():
                try:
                    # 使用adata获取指数当前行情
                    df = ad.stock.market.get_market_index_current(index_code)  # type: ignore

                    if df.empty:  # type: ignore
                        logger.warning(f"指数 {index_code} 实时数据为空")
                        continue

                    # adata返回的列名: ['index_code', 'trade_time', 'trade_date', 'open', 'high', 'low', 'price', 'volume', 'amount', 'change', 'change_pct']
                    # 重命名列以匹配实时数据需求
                    df.rename(columns={  # type: ignore
                        'price': 'close'  # 当前价格作为收盘价
                    }, inplace=True)

                    # 修复链式赋值警告：添加指数名称和前收盘价
                    # 使用df.loc[]方式避免链式赋值，确保pandas 3.0兼容性
                    df.loc[:, 'index_name'] = index_name  # type: ignore
                    df.loc[:, 'pre_close'] = df['close'] - df['change']  # type: ignore  # 计算前收盘价
                    df.loc[:, 'volume_ratio'] = 1.0  # type: ignore  # 默认量比

                    # 修复链式赋值警告：处理时间格式
                    df.loc[:, 'trade_time'] = pd.to_datetime(df['trade_time'])  # type: ignore

                    # 修复链式赋值警告：数据类型转换
                    # 使用df.loc[]方式进行数值类型转换，提高性能和兼容性
                    numeric_columns = ['open', 'high', 'low', 'close', 'pre_close', 'volume', 'amount', 'change', 'change_pct', 'volume_ratio']
                    for col in numeric_columns:
                        if col in df.columns:  # type: ignore
                            # 使用df.loc[]避免链式赋值警告
                            df.loc[:, col] = pd.to_numeric(df[col], errors='coerce')  # type: ignore

                    # 价格字段精度控制：确保价格字段最多保留两位小数
                    df = round_price_fields(df, ['open', 'high', 'low', 'close', 'pre_close'], decimal_places=2)  # type: ignore

                    # 删除不需要的字段
                    if 'trade_date' in df.columns:
                        df = df.drop('trade_date', axis=1)

                    all_index_data.append(df)  # type: ignore

                except Exception as e:
                    logger.warning(f"获取指数 {index_code} 实时数据失败: {e}")
                    continue

            if not all_index_data:
                logger.warning("没有获取到任何指数实时数据")
                return pd.DataFrame()

            # 合并所有指数数据
            result_df = pd.concat(all_index_data, ignore_index=True)  # type: ignore

            # 选择需要的列
            columns = ['trade_time', 'index_code', 'index_name', 'open', 'close', 'high', 'low',
                      'pre_close', 'volume', 'amount', 'volume_ratio', 'change', 'change_pct']

            available_columns = [col for col in columns if col in result_df.columns]
            result_df = result_df[available_columns]

            logger.info(f"✅ 获取指数实时数据完成: {len(result_df)} 条记录")
            return result_df

        except Exception as e:
            logger.error(f"❌ 获取指数实时数据失败: {e}")
            return pd.DataFrame()

    # 概念数据获取

    @retry_on_error(retries=3, delay=1)
    def collect_all_concept_data(self):
        """
        采集所有概念数据 - 智能更新版本

        包含：
        1. 概念信息表(concept_info) - 全量更新
        2. 概念成分股数据(concept_stocks) - 智能对比更新

        智能更新逻辑：
        - 获取最新的概念成分股数据
        - 对比数据库现有数据
        - 删除数据库中有但最新数据没有的记录
        - 添加最新数据有但数据库没有的记录
        """
        try:
            logger.info("🚀 开始概念数据智能更新...")

            # 1. 更新概念信息表（已有逻辑，保持不变）
            logger.info("📊 更新概念信息表...")
            self.get_all_concept_code_ths()

            # 2. 智能更新概念成分股数据
            logger.info("🧠 开始概念成分股数据智能更新...")
            self.smart_update_concept_stocks()

            logger.info("✅ 概念数据智能更新完成")

        except Exception as e:
            logger.error(f"❌ 采集所有概念数据失败: {e}")
            raise

    def smart_update_concept_stocks(self):
        """
        智能更新概念成分股数据

        实现逻辑：
        1. 获取所有股票的最新概念数据
        2. 与数据库现有数据进行对比
        3. 删除数据库中有但最新数据没有的记录
        4. 添加最新数据有但数据库没有的记录
        """
        try:
            logger.info("🧠 开始概念成分股数据智能对比更新...")

            # 1. 获取所有股票代码
            stock_codes = self.stock_codes
            if not stock_codes:
                logger.warning("⚠️ 未找到股票代码，跳过概念成分股更新")
                return

            logger.info(f"📊 准备更新 {len(stock_codes)} 只股票的概念数据")

            # 2. 获取数据库中现有的概念成分股数据
            logger.info("📋 获取数据库现有概念成分股数据...")
            existing_data = self.db.execute_query_df(  # type: ignore
                "SELECT index_code, stock_code, concept_name, stock_name, reason FROM concept_stocks"
            )

            # 创建现有数据的唯一标识集合
            existing_keys = set()  # type: ignore
            if not existing_data.empty:  # type: ignore
                existing_keys = set(  # type: ignore
                    zip(existing_data['index_code'], existing_data['stock_code'])  # type: ignore
                )

            logger.info(f"📊 数据库现有概念成分股关系: {len(existing_keys)} 条")  # type: ignore

            # 3. 获取最新的概念成分股数据
            logger.info("🔄 获取最新概念成分股数据...")
            new_concept_data = []
            new_keys = set()  # type: ignore

            # 使用进度条显示更新进度
            with tqdm(total=len(stock_codes), desc="获取概念数据") as pbar:
                for stock_code in stock_codes:
                    try:
                        # 获取单只股票的概念数据
                        concept_data = ad.stock.info.get_concept_ths(stock_code)

                        if concept_data is not None and not concept_data.empty:
                            # 数据清洗和格式化
                            concept_data['stock_code'] = stock_code
                            concept_data.rename(columns={
                                'concept_code': 'index_code',
                                'name': 'concept_name',
                                'reason': 'reason'
                            }, inplace=True)

                            # 获取股票名称
                            try:
                                stock_name_result = self.db.execute_query(  # type: ignore
                                    "SELECT stock_name FROM stock_info WHERE stock_code = %s",
                                    (stock_code,)
                                )
                                stock_name = stock_name_result[0][0] if stock_name_result else f"股票{stock_code}"  # type: ignore
                            except:
                                stock_name = f"股票{stock_code}"

                            concept_data['stock_name'] = stock_name

                            # 重新组织字段顺序
                            concept_data = concept_data[['index_code', 'concept_name', 'stock_code', 'stock_name', 'reason']]

                            # 收集新数据
                            new_concept_data.append(concept_data)  # type: ignore

                            # 记录新数据的唯一标识
                            for _, row in concept_data.iterrows():  # type: ignore
                                new_keys.add((row['index_code'], row['stock_code']))  # type: ignore

                    except Exception as e:
                        logger.debug(f"获取股票 {stock_code} 概念数据失败: {e}")

                    finally:
                        pbar.update(1)

            # 4. 合并所有新数据
            if new_concept_data:
                all_new_data = pd.concat(new_concept_data, ignore_index=True)  # type: ignore
                logger.info(f"📊 获取到最新概念成分股关系: {len(new_keys)} 条")  # type: ignore
            else:
                logger.warning("⚠️ 未获取到任何概念成分股数据")
                return

            # 5. 智能对比和更新
            logger.info("🔍 开始智能对比分析...")

            # 找出需要删除的记录（数据库有但最新数据没有）
            to_delete = existing_keys - new_keys  # type: ignore

            # 找出需要添加的记录（最新数据有但数据库没有）
            to_add = new_keys - existing_keys  # type: ignore

            logger.info(f"📊 对比结果:")
            logger.info(f"   - 需要删除: {len(to_delete)} 条")  # type: ignore
            logger.info(f"   - 需要添加: {len(to_add)} 条")  # type: ignore
            logger.info(f"   - 保持不变: {len(existing_keys & new_keys)} 条")  # type: ignore

            # 6. 执行删除操作
            if to_delete:
                logger.info(f"🗑️ 删除过时的概念成分股关系...")
                delete_count = 0
                for index_code, stock_code in to_delete:  # type: ignore
                    try:
                        deleted = self.db.execute_update(  # type: ignore
                            "DELETE FROM concept_stocks WHERE index_code = %s AND stock_code = %s",
                            (index_code, stock_code)  # type: ignore
                        )
                        delete_count += deleted
                    except Exception as e:
                        logger.debug(f"删除概念关系 {index_code}-{stock_code} 失败: {e}")

                logger.info(f"✅ 删除过时记录: {delete_count} 条")

            # 7. 执行添加操作
            if to_add:
                logger.info(f"➕ 添加新的概念成分股关系...")

                # 筛选出需要添加的数据
                add_data = all_new_data[  # type: ignore
                    all_new_data.apply(  # type: ignore
                        lambda row: (row['index_code'], row['stock_code']) in to_add,  # type: ignore
                        axis=1
                    )
                ]

                if not add_data.empty:
                    inserted = self.db.batch_insert_df(
                        'concept_stocks',
                        add_data,
                        conflict_fields=['index_code', 'stock_code'],
                        enable_dedup=True
                    )
                    logger.info(f"✅ 添加新记录: {inserted} 条")

            # 8. 更新现有记录（确保数据最新）
            logger.info("🔄 更新现有记录...")
            existing_to_update = existing_keys & new_keys  # type: ignore
            if existing_to_update:
                update_data = all_new_data[  # type: ignore
                    all_new_data.apply(  # type: ignore
                        lambda row: (row['index_code'], row['stock_code']) in existing_to_update,  # type: ignore
                        axis=1
                    )
                ]

                if not update_data.empty:  # type: ignore
                    _ = self.db.batch_insert_df(  # type: ignore
                        'concept_stocks',
                        update_data,
                        conflict_fields=['index_code', 'stock_code'],
                        enable_dedup=True
                    )
                    logger.info(f"✅ 更新现有记录: {len(update_data)} 条")

            logger.info("🎉 概念成分股数据智能更新完成")

        except Exception as e:
            logger.error(f"❌ 概念成分股数据智能更新失败: {e}")
            raise

    @retry_on_error(retries=3, delay=1)
    def get_concept_ths_daily_data(self, index_code: str) -> Union[pd.DataFrame, None]:
        """
        获取单个同花顺概念指数的K线行情
        :param index_code:
        :return:
        """
        try:
            con_daily_data = ad.stock.market.get_market_concept_ths(index_code)
            
            # 检查数据是否有效
            if con_daily_data is None or con_daily_data.empty:  # type: ignore
                logger.warning(f"概念指数 {index_code} 的K线行情数据为空")
                return

            # 选择需要的列
            con_daily_data = con_daily_data[['index_code', 'trade_time', 'open', 'close', 'high', 'low', 'volume', 'amount', 'change', 'change_pct']]  # type: ignore
            
            # 修复链式赋值警告：处理时间格式
            # 使用df.loc[]方式避免链式赋值，确保pandas 3.0兼容性
            try:
                # 强制转换trade_time为datetime类型
                con_daily_data.loc[:, 'trade_time'] = pd.to_datetime(con_daily_data['trade_time'].astype(str), errors='coerce')  # type: ignore

                # 检查时间转换是否成功
                if con_daily_data['trade_time'].isna().all():  # type: ignore
                    logger.error(f"❌ 概念指数 {index_code} 时间字段转换失败，所有值都是NaT")
                    return

                # 删除时间转换失败的行
                con_daily_data = con_daily_data.dropna(subset=['trade_time'])  # type: ignore

                if con_daily_data.empty:  # type: ignore
                    logger.debug(f"概念指数 {index_code} 时间转换后无有效数据")
                    return

                # 验证转换成功
                if not pd.api.types.is_datetime64_any_dtype(con_daily_data['trade_time']):  # type: ignore
                    logger.error(f"❌ 概念指数 {index_code} 时间类型验证失败: {con_daily_data['trade_time'].dtype}")  # type: ignore
                    return

                # 只对没有时区的数据添加时区
                if con_daily_data['trade_time'].dt.tz is None:  # type: ignore
                    con_daily_data.loc[:, 'trade_time'] = con_daily_data['trade_time'].dt.tz_localize('Asia/Shanghai')  # type: ignore
            except Exception as time_error:
                logger.error(f"❌ 概念指数 {index_code} 时间处理失败: {time_error}")
                return

            # 修复链式赋值警告：确保数值列为浮点数类型
            # 使用df.loc[]方式进行数值类型转换，提高性能和兼容性
            numeric_columns = ['open', 'close', 'high', 'low', 'amount', 'change', 'change_pct']
            for col in numeric_columns:
                # 使用df.loc[]避免链式赋值警告
                con_daily_data.loc[:, col] = pd.to_numeric(con_daily_data[col], errors='coerce')  # type: ignore

            # 价格字段精度控制：确保价格字段最多保留两位小数
            con_daily_data = round_price_fields(con_daily_data, ['open', 'high', 'low', 'close'], decimal_places=2)  # type: ignore

            # 修复链式赋值警告：volume 列转为整数
            con_daily_data.loc[:, 'volume'] = pd.to_numeric(con_daily_data['volume'], errors='coerce').fillna(0).astype(int)  # type: ignore

            # 修复链式赋值警告：确保 index_code 为字符串类型
            con_daily_data.loc[:, 'index_code'] = con_daily_data['index_code'].astype(str)  # type: ignore

            # 处理 NaN 值
            con_daily_data = con_daily_data.fillna(0)  # type: ignore
            con_daily_data = con_daily_data[con_daily_data['trade_time'] >= '2025-01-01']
            
            # 保存到数据库
            self.db.batch_insert_df(
                'concept_daily_data',
                con_daily_data,
                conflict_fields=['index_code', 'trade_time'],
                enable_dedup=True
            )
            
            #logger.info(f"成功获取概念指数 {index_code} 的K线行情，共 {len(con_daily_data)} 条")
            
        except Exception as e:
            logger.error(f"获取概念指数 {index_code} 的K线行情失败: {e}")
            # 不抛出异常，允许程序继续运行
            return None
        

    @retry_on_error(retries=3, delay=1)
    def _collect_concept_min_data(self, index_code: str):
        """采集概念分钟数据
        
        获取概念指数的分钟K线数据并生成不同周期的K线
        
        参数:
            index_code: 概念指数代码
            
        返回:
            None, 数据直接保存到数据库
        """
        try:
            # 获取1分钟K线数据
            df = ad.stock.market.get_market_concept_min_ths(index_code)
            if df is None or df.empty:  # type: ignore
                logger.warning(f"获取概念指数 {index_code} 的分钟K线数据为空")
                return None

            # 修复链式赋值警告：数据类型处理
            # 使用df.loc[]方式进行数值类型转换，提高性能和兼容性
            numeric_columns = ['volume', 'amount', 'price']
            for col in numeric_columns:
                if col in df.columns:  # type: ignore
                    # 使用df.loc[]避免链式赋值警告
                    df.loc[:, col] = pd.to_numeric(df[col], errors='coerce').fillna(0)  # type: ignore
                    # 验证转换是否成功
                    if not pd.api.types.is_numeric_dtype(df[col]):  # type: ignore
                        logger.warning(f"⚠️ 概念指数 {index_code} 列 {col} 数值转换失败，类型: {df[col].dtype}")  # type: ignore
                        df.loc[:, col] = 0  # type: ignore  # 设置默认值

            # 转换单位（volume 和 amount 转换为百万）
            for col in ['volume', 'amount']:
                if col in df.columns and pd.api.types.is_numeric_dtype(df[col]):  # type: ignore
                    df.loc[:, col] = (df[col] / 1000000).round(2)  # type: ignore  # 转换为百万

            # 重命名列并处理时间 - 使用inplace=True避免数据复制
            df.rename(columns={  # type: ignore
                'price': 'close'
            }, inplace=True)

            # 确保 close 列也是数值类型
            if 'close' in df.columns:  # type: ignore
                df.loc[:, 'close'] = pd.to_numeric(df['close'], errors='coerce').fillna(0)  # type: ignore

            # 修复链式赋值警告：处理时间格式
            df.loc[:, 'trade_time'] = pd.to_datetime(df['trade_time'])  # type: ignore

            # 生成不同周期K线
            for period in [5, 15]:
                # 按周期聚合数据
                period_df = df.groupby(  # type: ignore
                    pd.Grouper(key='trade_time', freq=f'{period}min', closed='right', label='right')
                ).agg({  # type: ignore
                    'close': 'last',
                    'volume': 'sum',
                    'amount': 'sum'
                }).dropna().reset_index()  # type: ignore

                if period_df.empty:  # type: ignore
                    logger.warning(f"概念指数 {index_code} 的 {period} 分钟K线数据为空")
                    continue

                # 安全地排除 9:30 的数据
                try:
                    # 确保 trade_time 是 datetime 类型
                    if not pd.api.types.is_datetime64_any_dtype(period_df['trade_time']):  # type: ignore
                        period_df.loc[:, 'trade_time'] = pd.to_datetime(period_df['trade_time'], errors='coerce')  # type: ignore

                    # 排除 9:30 的数据
                    period_df = period_df[~((period_df['trade_time'].dt.hour == 9) & (period_df['trade_time'].dt.minute == 30))]  # type: ignore

                    if period_df.empty:  # type: ignore
                        logger.debug(f"概念指数 {index_code} 的 {period} 分钟K线数据过滤后为空")
                        continue

                except Exception as filter_error:
                    logger.error(f"❌ 概念指数 {index_code} {period}分钟数据过滤失败: {filter_error}")
                    continue

                # 修复链式赋值警告：添加必要字段
                # 使用df.loc[]方式避免链式赋值，确保pandas 3.0兼容性
                period_df.loc[:, 'index_code'] = index_code  # type: ignore
                period_df.loc[:, 'period'] = period  # type: ignore

                # 安全的时间处理
                try:
                    period_df.loc[:, 'trade_time'] = pd.to_datetime(period_df['trade_time'], errors='coerce')  # type: ignore

                    # 检查时间转换是否成功
                    if period_df['trade_time'].isna().all():  # type: ignore
                        logger.error(f"❌ 概念指数 {index_code} {period}分钟数据时间转换失败，所有值都是NaT")
                        continue

                    # 删除时间转换失败的行
                    period_df = period_df.dropna(subset=['trade_time'])  # type: ignore

                    if period_df.empty:  # type: ignore
                        logger.debug(f"概念指数 {index_code} {period}分钟数据时间转换后无有效数据")
                        continue

                    # 只对没有时区的数据添加时区
                    if period_df['trade_time'].dt.tz is None:  # type: ignore
                        period_df.loc[:, 'trade_time'] = period_df['trade_time'].dt.tz_localize('Asia/Shanghai')  # type: ignore
                except Exception as time_error:
                    logger.error(f"❌ 概念指数 {index_code} {period}分钟数据时间处理失败: {time_error}")
                    continue

                try:
                    # 先保存新数据
                    self.db.batch_insert_df(
                        'concept_min_data',
                        period_df,  # type: ignore
                        ['index_code', 'period', 'trade_time']
                    )
                    
                    # 然后计算并更新 EMA 值
                    #ema_df = await self.update_ema_values('concept_min_data', index_code, period, period_df)
                    
                    #if not ema_df.empty:
                        # 更新 EMA 值到数据库
                        #await self.db.batch_insert_df(
                            #'concept_min_data',
                            #ema_df,
                            #['index_code', 'period', 'trade_time']
                        #)
                        #logger.info(f"成功保存概念指数 {index_code} 的 {period} 分钟数据，共 {len(ema_df)} 条")
                    
                except Exception as db_error:
                    logger.error(f"保存概念指数 {index_code} 的 {period} 分钟数据到数据库失败: {db_error}")
                
        except Exception as e:
            logger.error(f"获取概念指数 {index_code} 的分钟K线行情失败: {e}")
            return None