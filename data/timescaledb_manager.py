#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TimescaleDB数据库管理器
基于PostgreSQL + TimescaleDB的高性能时序数据库解决方案

功能特性:
- 连接池管理
- 批量数据插入
- 时序数据优化
- 自动字段映射
- UPSERT支持
- 超表(hypertable)管理

作者: Xzh
版本: 3.0.0
"""

import psycopg2
import psycopg2.pool
import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Any, Union, Tuple
from contextlib import contextmanager
from datetime import datetime

import logging

logger = logging.getLogger(__name__)


class TimescaleDBManager:
    """TimescaleDB数据库管理器 - 高性能时序数据库"""

    def __init__(self,
                 host: str = "localhost",
                 port: int = 5432,
                 username: str = "xystock",
                 password: str = "xystock123",
                 database: str = "xystock",
                 minconn: int = 2,
                 maxconn: int = 10):
        """
        初始化数据库管理器
        
        Args:
            host: 数据库主机
            port: 数据库端口
            username: 用户名
            password: 密码
            database: 数据库名
            minconn: 最小连接数
            maxconn: 最大连接数
        """
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.database = database
        self.minconn = minconn
        self.maxconn = maxconn
        self.connection_pool = None
        
        logger.info("✅ TimescaleDB管理器初始化")
        logger.info(f"🔗 连接参数: {host}:{port}/{database}")

    def connect(self) -> bool:
        """建立数据库连接池"""
        try:
            self.connection_pool = psycopg2.pool.ThreadedConnectionPool(
                self.minconn,
                self.maxconn,
                host=self.host,
                port=self.port,
                user=self.username,
                password=self.password,
                database=self.database,
                options="-c timezone=Asia/Shanghai"
            )
            logger.info("✅ TimescaleDB连接池创建成功")
            return True
        except Exception as e:
            logger.error(f"❌ TimescaleDB连接失败: {e}")
            return False

    def disconnect(self):
        """关闭数据库连接池"""
        if self.connection_pool:
            self.connection_pool.closeall()
            self.connection_pool = None
            logger.info("✅ TimescaleDB连接已断开")

    @contextmanager
    def get_cursor(self):
        """获取数据库游标（上下文管理器）"""
        if not self.connection_pool:
            self.connect()
        
        conn = None
        cursor = None
        try:
            conn = self.connection_pool.getconn()
            cursor = conn.cursor()
            yield cursor
            conn.commit()
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                self.connection_pool.putconn(conn)

    def execute_query_df(self, query: str, params: tuple = None) -> pd.DataFrame:
        """执行查询并返回DataFrame"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute(query, params)
                columns = [desc[0] for desc in cursor.description]
                data = cursor.fetchall()
                return pd.DataFrame(data, columns=columns)
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            return pd.DataFrame()

    def execute_query(self, query: str, params: tuple = None) -> List[tuple]:
        """执行查询并返回结果"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            return []

    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新操作"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute(query, params)
                return cursor.rowcount
        except Exception as e:
            logger.error(f"更新执行失败: {e}")
            return 0

    def batch_insert_df(self, table_name: str, df: pd.DataFrame, 
                       conflict_fields: List[str] = None, 
                       enable_dedup: bool = True,
                       chunk_size: int = 1000) -> int:
        """
        批量插入DataFrame数据
        
        Args:
            table_name: 目标表名
            df: 要插入的DataFrame
            conflict_fields: 冲突字段
            enable_dedup: 是否启用去重
            chunk_size: 批次大小
            
        Returns:
            插入的记录数
        """
        if df.empty:
            logger.warning(f"DataFrame为空，跳过插入到表 {table_name}")
            return 0

        try:
            # 数据预处理
            df_processed = self._prepare_dataframe(df, table_name)
            
            # 构建插入SQL
            columns = list(df_processed.columns)
            placeholders = ', '.join(['%s'] * len(columns))
            
            if enable_dedup and conflict_fields:
                # 使用UPSERT（恢复断点续传功能）
                conflict_cols = ', '.join(conflict_fields)
                update_cols = ', '.join([f"{col} = EXCLUDED.{col}"
                                       for col in columns if col not in conflict_fields])

                sql = f"""
                    INSERT INTO {table_name} ({', '.join(columns)})
                    VALUES ({placeholders})
                    ON CONFLICT ({conflict_cols})
                    DO UPDATE SET {update_cols}
                """
            else:
                # 普通插入
                sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
            
            # 分批插入
            total_rows = 0
            for i in range(0, len(df_processed), chunk_size):
                chunk = df_processed.iloc[i:i + chunk_size]
                data_tuples = [tuple(row) for row in chunk.values]
                
                with self.get_cursor() as cursor:
                    cursor.executemany(sql, data_tuples)
                    total_rows += cursor.rowcount
                
            logger.info(f"✅ 成功插入 {total_rows} 条记录到表 {table_name}")
            return total_rows
            
        except Exception as e:
            logger.error(f"❌ 批量插入数据到表 {table_name} 失败: {e}")
            raise

    def _prepare_dataframe(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """
        预处理DataFrame数据

        修复pandas链式赋值警告，使用df.loc[]方式进行数据修改，
        避免在pandas 3.0中的兼容性问题，同时优化数据处理性能

        Args:
            df: 原始DataFrame数据
            table_name: 目标表名

        Returns:
            pd.DataFrame: 预处理后的DataFrame
        """
        df_copy = df.copy()

        # 字段映射 - 统一字段名称
        field_mappings = {
            'timestamp': 'trade_time',
            'time': 'trade_time',
            'title': 'news_title',
            'content': 'news_summary',
            'publish_time': 'trade_time'
        }

        # 应用字段映射 - 使用rename方法避免链式赋值
        for old_field, new_field in field_mappings.items():
            if old_field in df_copy.columns and old_field != new_field:
                df_copy = df_copy.rename(columns={old_field: new_field})

        # 修复链式赋值警告：时间戳字段处理
        # 使用df.loc[]方式避免链式赋值，确保pandas 3.0兼容性
        timestamp_columns = ['trade_time', 'kline_time', 'action_time']
        for col in timestamp_columns:
            if col in df_copy.columns:
                if df_copy[col].dtype == 'object':
                    # 使用df.loc[]避免链式赋值警告
                    df_copy.loc[:, col] = pd.to_datetime(df_copy[col], errors='coerce')
                # 确保时区信息 - 使用df.loc[]方式
                if hasattr(df_copy[col].dtype, 'tz') and df_copy[col].dt.tz is None:
                    df_copy.loc[:, col] = df_copy[col].dt.tz_localize('Asia/Shanghai')

        # 价格字段精度控制：确保价格字段最多保留两位小数
        # 适用于所有包含价格字段的表
        price_columns = ['open', 'high', 'low', 'close', 'pre_close', 'price']
        existing_price_columns = [col for col in price_columns if col in df_copy.columns]

        if existing_price_columns:
            for col in existing_price_columns:
                # 确保是数值类型后再进行精度控制
                if pd.api.types.is_numeric_dtype(df_copy[col]):
                    # 使用df.loc[]避免链式赋值警告，四舍五入到2位小数
                    df_copy.loc[:, col] = df_copy[col].round(2)

        # 处理NaN值 - 替换为None以适配数据库
        df_copy = df_copy.replace({np.nan: None})

        return df_copy

    def update_stock_score(self, stock_code: str, score: float) -> bool:
        """更新股票评分"""
        try:
            # 使用UPSERT：重复检测，重复则更新
            sql = """
                INSERT INTO stock_info (stock_code, score)
                VALUES (%s, %s)
                ON CONFLICT (stock_code)
                DO UPDATE SET score = EXCLUDED.score
            """
            affected_rows = self.execute_update(sql, (stock_code, score))
            return affected_rows > 0
        except Exception as e:
            logger.error(f"更新股票 {stock_code} 评分失败: {e}")
            return False

    def get_stock_name(self, stock_code: str) -> Optional[str]:
        """获取股票名称"""
        try:
            result = self.execute_query(
                "SELECT stock_name FROM stock_info WHERE stock_code = %s",
                (stock_code,)
            )
            return result[0][0] if result and result[0][0] else None
        except Exception as e:
            logger.debug(f"获取股票 {stock_code} 名称失败: {e}")
            return None

    def delete_data(self, table_name: str, condition: str, params: tuple = None) -> int:
        """删除数据"""
        try:
            sql = f"DELETE FROM {table_name} WHERE {condition}"
            return self.execute_update(sql, params)
        except Exception as e:
            logger.error(f"删除数据失败: {e}")
            return 0

    def create_hypertable(self, table_name: str, time_column: str, 
                         chunk_time_interval: str = '1 day') -> bool:
        """创建超表"""
        try:
            sql = f"""
                SELECT create_hypertable('{table_name}', '{time_column}', 
                                        chunk_time_interval => INTERVAL '{chunk_time_interval}',
                                        if_not_exists => TRUE)
            """
            self.execute_update(sql)
            logger.info(f"✅ 超表 {table_name} 创建成功")
            return True
        except Exception as e:
            logger.error(f"❌ 创建超表 {table_name} 失败: {e}")
            return False

    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()
        # 返回False表示不抑制异常
        return False
