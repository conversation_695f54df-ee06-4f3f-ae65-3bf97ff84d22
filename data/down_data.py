from datetime import datetime
from utils.logger import setup_logger
from config.config import settings
# from data.kdb_manager import KDBManager  # 注释掉KDB+相关导入
# from data.questdb_manager import QuestDBManager  # 注释掉QuestDB相关导入
from data.database_manager import DatabaseManager
from data.market_fetcher import MarketDataFetcher
from typing import Dict, Any, Optional
import json


# 创建日志记录器
logger = setup_logger(__name__)


class DownData:
    _last_index_tick_data: Dict[str, Dict[str, Any]] = {}
    _last_stock_tick_data: Dict[str, Dict[str, Any]] = {}

    # 删除断点续传文件路径常量

    def __init__(self, db: DatabaseManager):
        """初始化数据采集器"""
        logger.info("初始化市场数据采集器 - 使用TimescaleDB")
        # 不再使用线程池，改为串行执行
        self.max_workers = settings.MAX_WORKERS
        # 移除线程池executor初始化
        self.db = db
        self.fetcher = MarketDataFetcher(db)

        self._stock_codes = None
        self._concept_index = None
        self._concept_names = None
        self._index_codes = {
            '000001': '上证指数',
            '399001': '深证成指',
            '399006': '创业板指',
            '000852': '中证1000',
            '930050': '中证A50'
        }  


    def __enter__(self):
        """同步上下文管理器入口"""
        # 检查连接是否已存在，如果没有才连接
        if not self._is_db_connected():
            logger.info("数据库未连接，尝试连接...")
            success = self.db.connect()
            if not success:
                logger.error("❌ 数据库连接失败")
                raise Exception("数据库连接失败")
            else:
                logger.info("✅ 数据库连接成功")
        else:
            logger.debug("数据库已连接，复用现有连接")
        return self

    def _is_db_connected(self) -> bool:
        """检查数据库是否已连接"""
        if hasattr(self.db, 'health_check'):
            # TimescaleDB和QuestDB使用health_check方法
            return self.db.health_check()  # type: ignore
        elif hasattr(self.db, 'connection'):
            # KDB+使用connection属性
            return bool(self.db.connection)  # type: ignore
        else:
            return False

    def __exit__(self, exc_type: Optional[type], exc_val: Optional[Exception], exc_tb: Optional[Any]) -> None:
        """同步上下文管理器出口"""
        # 标准上下文管理器参数，符合协议要求
        _ = exc_type, exc_val, exc_tb  # 避免未使用参数警告
        self.db.disconnect()
        # 不再需要关闭线程池

    @classmethod
    def load_data(cls) -> None:
        try:
            with open('tick_data.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                cls._last_index_tick_data = data.get('index_tick_data', {})
                #cls._last_stock_tick_data = data.get('stock_tick_data', {})
            logger.info(f"数据加载成功: {cls._last_index_tick_data}")
        except FileNotFoundError:
            logger.info("没有找到数据文件，使用默认值")
        except json.JSONDecodeError as e:
            logger.error(f"数据文件格式错误: {e}, 使用默认值")

    @classmethod
    def save_data(cls) -> None:
        data = {
            'index_tick_data': cls._last_index_tick_data,
            #'stock_tick_data': cls._last_stock_tick_data
        }
        try:
            with open('tick_data.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False)
            logger.info("数据保存成功")
        except Exception as e:
            logger.error(f"保存数据时发生错误: {e}")

    @classmethod
    def initialize(cls) -> None:
        # 载入持久化数据
        cls.load_data()
        logger.info(f'指数和股票tick量差和金额差初始化完毕!{cls._last_index_tick_data}')

    @classmethod
    def reset_data(cls) -> None:
        cls._last_index_tick_data = {}
        #cls._last_stock_tick_data = {}
        cls.save_data()
        logger.info(f'数据已重置并保存!{cls._last_index_tick_data}')

    # 删除断点续传相关的方法

    def collect_index_tick(self):
        """
        从东财下载指数分时数据
        :return:
        """
        try:
            logger.info(f"实时tick数据采集ing{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            index_tick = self.fetcher.get_index_tick()

            # 初始化增量列
            index_tick['tick_volume'] = 0.0
            index_tick['tick_amount'] = 0.0

            # 计算增量
            for idx, row in index_tick.iterrows():  # type: ignore
                code = row['index_code']  # type: ignore
                if code in self._last_index_tick_data:
                    last_data = self._last_index_tick_data[code]
                    # 计算成交量和成交额的增量
                    index_tick.at[idx, 'tick_volume'] = float(row['volume'] - last_data['volume'])  # type: ignore
                    index_tick.at[idx, 'tick_amount'] = float(row['amount'] - last_data['amount'])  # type: ignore
                else:
                    # 当天第一条数据，增量等于当前值
                    index_tick.at[idx, 'tick_volume'] = float(row['volume'])  # type: ignore
                    index_tick.at[idx, 'tick_amount'] = float(row['amount'])  # type: ignore

            # 更新最后一次的数据
            self._last_index_tick_data.update({
                row['index_code']: {'volume': float(row['volume']), 'amount': float(row['amount'])}  # type: ignore
                for _, row in index_tick.iterrows()  # type: ignore
            })

            index_tick = index_tick[
                ['trade_time', 'index_code', 'index_name', 'open', 'close', 'high', 'low', 'pre_close', 'volume', 'tick_volume', 'amount', 'tick_amount', 'volume_ratio', 'change', 'change_pct']]
            index_tick = index_tick.query('tick_volume != 0')  # type: ignore
            #print(index_tick.columns)
            #self.save_data()
            # 保存当前数据
            if hasattr(self.db, 'batch_insert_df'):
                # KDB+方式（关闭去重以提高性能）
                self.db.batch_insert_df(
                    'index_tick_data',
                    index_tick,
                    conflict_fields=['trade_time', 'index_code'],
                    enable_dedup=False
                )
            else:
                # TimescaleDB和QuestDB方式
                # 重命名时间列以匹配TimescaleDB表结构
                if 'trade_time' in index_tick.columns:
                    index_tick = index_tick.rename(columns={'trade_time': 'time'})

                self.db.insert_dataframe(  # type: ignore
                    'index_daily_data',  # 使用正确的表名
                    index_tick,
                    upsert_mode=True,
                    conflict_cols=['time', 'index_code']
                )
        except Exception as e:
            logger.error(f"实时数据采集出错: {str(e)}")
            raise

    def collect_force_download_data(self, start_date: str, end_date: str) -> None:
        """
        强制下载历史数据任务

        用于获取大量历史数据：
        - 日线数据（2000条历史记录/股票）
        - 5分钟数据（2000条历史记录/股票）
        - 15分钟数据（2000条历史记录/股票）
        - 股票评分数据（1条记录/股票）

        Args:
            start_date: 开始日期（用于日志显示）
            end_date: 结束日期（用于日志显示）
        """
        try:
            # 获取 fetcher 实例
            fetcher = self.fetcher

            logger.info("🚀 开始强制下载历史数据任务...")
            logger.info(f"📅 参考日期: {start_date} 到 {end_date}")
            logger.info("📊 下载内容: 2000条历史数据（日线+分钟线+评分）")

            # 强制下载股票数据（单循环高效版本）
            try:
                logger.info("📈 开始强制下载股票历史数据（单循环高效版本）...")
                logger.info("💡 包含内容：")
                logger.info("   - 日线数据：2000条历史记录/股票")
                logger.info("   - 5分钟K线：2000条历史记录/股票")
                logger.info("   - 15分钟K线：2000条历史记录/股票")
                logger.info("   - 股票评分：1条记录/股票")
                logger.info("💡 优势：按股票代码单循环执行，避免多次循环，提高效率")

                # 使用单循环强制下载模式，一次性完成所有股票相关数据
                # 明确传递force_download=True，确保使用强制下载模式
                fetcher.collect_daily_incremental_data(start_date, end_date, force_download=True)

                logger.info("✅ 强制下载股票历史数据完成（单循环高效版本）")
            except Exception as e:
                logger.error(f"❌ 强制下载股票历史数据失败: {e}")
                raise  # 股票数据是核心任务，失败时抛出异常

            logger.info("✅ 强制下载历史数据任务完成")

        except Exception as e:
            logger.error(f"❌ 强制下载历史数据任务失败: {e}")
            raise

    def collect_daily_schedule(self, start_date: str, end_date: str) -> None:
        """
        每日15:30收盘后任务 - 增量更新模式

        专门用于每日收盘后的增量数据更新：
        - 当天日线数据（1条记录/股票）
        - 当天5分钟K线数据（约48条记录/股票）
        - 当天15分钟K线数据（约16条记录/股票）
        - 指数的日线和周期K线数据
        - 概念相关数据（当天）

        注意：此方法只做增量更新，不做历史数据下载

        Args:
            start_date: 开始日期（通常是当天）
            end_date: 结束日期（通常是当天）
        """
        try:
            # 获取 fetcher 实例
            fetcher = self.fetcher

            logger.info("🔄 开始每日15:30数据更新任务...")
            logger.info(f"📅 更新日期: {start_date} 到 {end_date}")
            logger.info("📊 更新内容: 当天日线+分钟线+指数+概念数据")

            # 1. 更新当天股票数据（单循环高效版本）
            try:
                logger.info("📈 开始更新当天股票数据（单循环高效版本）...")
                logger.info("💡 包含内容：")
                logger.info("   - 日线数据：1条记录/股票")
                logger.info("   - 5分钟K线：约48条记录/股票")
                logger.info("   - 15分钟K线：约16条记录/股票")
                logger.info("   - 股票评分：1条记录/股票")
                logger.info("💡 优势：按股票代码单循环执行，避免多次循环，提高效率")

                # 使用单循环增量模式，一次性完成所有股票相关数据
                # 明确传递force_download=False，确保使用增量模式
                fetcher.collect_daily_incremental_data(start_date, end_date, force_download=False)

                logger.info("✅ 当天股票数据更新完成（单循环高效版本）")
            except Exception as e:
                logger.error(f"❌ 更新当天股票数据失败: {e}")
                raise  # 股票数据是核心任务，失败时抛出异常

            # 3. 更新指数数据（日线和周期K线）
            try:
                logger.info("📊 开始更新指数数据（日线和周期K线）...")

                fetcher.collect_index_data(start_date=start_date, end_date=end_date)

                logger.info("✅ 指数数据更新完成")
            except Exception as e:
                logger.error(f"❌ 更新指数数据失败: {e}")
                # 指数数据失败不影响其他任务

            # 4. 更新概念K线数据（当天）
            try:
                logger.info("🏷️ 开始更新概念K线数据（当天）...")
                logger.info("   - 概念日线K线：当天数据")
                logger.info("   - 概念5分钟K线：约48条记录")
                logger.info("   - 概念15分钟K线：约16条记录")

                # 获取当天的概念K线数据（日线+分钟线）
                fetcher.collect_concept_kline_incremental_data(start_date, end_date)

                logger.info("✅ 概念K线数据更新完成")
            except Exception as e:
                logger.error(f"❌ 更新概念K线数据失败: {e}")
                # 概念数据失败不影响其他任务

            logger.info("🎉 每日15:30数据更新任务完成")
            logger.info("📊 更新统计:")
            logger.info("   - 股票数据（单循环）: 日线+5分钟+15分钟+评分")
            logger.info("     * 日线数据: 1条/股票")
            logger.info("     * 5分钟数据: ~48条/股票")
            logger.info("     * 15分钟数据: ~16条/股票")
            logger.info("     * 评分数据: 1条/股票")
            logger.info("   - 指数数据: 当天日线和周期K线")
            logger.info("   - 概念K线数据: 当天日线+分钟K线")
            logger.info("💡 优化效果: 单循环执行，避免多次遍历股票列表")
            logger.info("💡 概念信息和成分股数据在每周三更新")

        except Exception as e:
            logger.error(f"❌ 每日数据更新失败: {e}")
            raise

    def collect_weekly_schedule(self, start_date: str, end_date: str):
        """
        每周三更新任务 - 正确版本

        根据用户需求：
        - 概念相关的所有数据（全量更新）
        - 股票信息数据（基础信息更新）

        Args:
            start_date: 开始日期（用于日志显示）
            end_date: 结束日期（用于日志显示）
        """
        try:
            # 获取 fetcher 实例
            fetcher = self.fetcher

            logger.info("🔄 开始每周三批量更新任务...")
            logger.info(f"📅 更新日期范围: {start_date} 到 {end_date}")
            logger.info("📊 更新内容: 概念所有数据+股票信息")

            # 1. 更新股票信息数据（基础信息）
            try:
                logger.info("📊 开始更新股票信息数据...")

                fetcher.collect_stock_info()

                logger.info("✅ 股票信息数据更新完成")
            except Exception as e:
                logger.error(f"❌ 更新股票信息数据失败: {e}")
                # 不抛出异常，继续执行其他任务

            # 2. 更新概念基本信息（全量）
            try:
                logger.info("🏷️ 开始更新概念基本信息（全量）...")

                fetcher.get_all_concept_code_ths()

                logger.info("✅ 概念基本信息更新完成")
            except Exception as e:
                logger.error(f"❌ 更新概念基本信息失败: {e}")
                # 不抛出异常，继续执行其他任务

            # 3. 更新概念信息和成分股数据（智能对比更新）
            try:
                logger.info("🏷️ 开始更新概念信息和成分股数据（智能对比更新）...")
                logger.info("💡 包含内容：")
                logger.info("   - 概念信息表(concept_info)：概念基本信息（全量更新）")
                logger.info("   - 概念成分股数据(concept_stocks)：概念与股票关系（智能对比）")
                logger.info("💡 智能对比逻辑：")
                logger.info("   - 获取最新概念成分股数据")
                logger.info("   - 删除数据库中有但最新数据没有的记录")
                logger.info("   - 添加最新数据有但数据库没有的记录")
                logger.info("   - 更新现有记录确保数据最新")

                # 智能更新概念信息和成分股数据
                fetcher.collect_all_concept_data()

                logger.info("✅ 概念信息和成分股数据智能更新完成")
            except Exception as e:
                logger.error(f"❌ 更新概念信息和成分股数据失败: {e}")
                # 概念数据更新失败不影响其他任务

            logger.info("🎉 每周三批量更新任务完成")
            logger.info("📊 更新统计:")
            logger.info("   - 股票信息数据: 全量更新")
            logger.info("   - 概念信息表(concept_info): 全量更新")
            logger.info("   - 概念成分股数据(concept_stocks): 全量更新")
            logger.info("💡 下次更新时间：下周三")
            logger.info("💡 注意：概念K线数据在每日15:30更新")

        except Exception as e:
            logger.error(f"❌ 每周数据更新失败: {e}")
            raise

    def collect_news(self):
        """
        下载新闻数据
        :return:
        """
        try:
            logger.info(f"开始收集新闻数据{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            # 确保数据库连接是可用的
            if not self._is_db_connected():
                logger.warning("数据库连接不可用，尝试重新连接")
                self.db.connect()

            logger.info("开始下载新闻数据...")
            # self.fetcher.collect_cx_news()  # 方法不存在，暂时注释
            logger.info("新闻数据下载完成")
        except Exception as e:
            logger.error(f"新闻数据下载出错:{e}")
            raise

    def collect_realtime_data(self) -> None:
        """实时行情数据采集任务"""
        try:
            logger.info(f"实时tick数据采集ing{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            # 确保数据库连接是可用的
            if not self._is_db_connected():
                logger.warning("数据库连接不可用，尝试重新连接")
                self.db.connect()

            # 收集指数实时数据
            #self.collect_index_tick()

            # 收集股票实时数据
            try:
                # 更新实时tick数据（使用现有的方法）
                tick_data = self.fetcher.get_stock_tick_data()
                if not tick_data.empty:
                    # 保存实时数据
                    if hasattr(self.db, 'batch_insert_df'):
                        self.db.batch_insert_df(
                            'stock_tick_data',
                            tick_data,
                            conflict_fields=['stock_code', 'trade_time'],
                            enable_dedup=False
                        )
                    logger.info(f"✅ 实时数据更新完成: {len(tick_data)} 条记录")
                else:
                    logger.warning("⚠️ 实时数据为空")
            except Exception as e:
                logger.error(f"实时数据采集过程中出错: {e}")

        except Exception as e:
            logger.error(f"实时数据采集失败: {e}")
            raise