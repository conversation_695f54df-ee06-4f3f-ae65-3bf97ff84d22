#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器
基于PostgreSQL + TimescaleDB的高性能时序数据库解决方案

功能特性:
- 连接池管理
- 批量数据插入
- 时序数据优化
- 自动字段映射
- UPSERT支持
- 超表(hypertable)管理

作者: Xzh
版本: 3.0.0
"""

from typing import Optional, Any
from .timescaledb_manager import TimescaleDBManager
from config.config import config
import logging

logger = logging.getLogger(__name__)

# 为了保持向后兼容性，创建DatabaseManager别名
class DatabaseManager(TimescaleDBManager):
    """数据库管理器 - 兼容性包装器"""

    def __init__(self,
                 host: Optional[str] = None,
                 port: Optional[int] = None,
                 username: Optional[str] = None,
                 password: Optional[str] = None,
                 database: Optional[str] = None,
                 minconn: Optional[int] = None,
                 maxconn: Optional[int] = None):
        """
        初始化数据库管理器（向后兼容）
        """
        # 使用配置文件中的默认值
        super().__init__(
            host=host or config.database.host,
            port=port or config.database.port,
            username=username or config.database.username,
            password=password or config.database.password,
            database=database or config.database.database,
            minconn=minconn or config.database.minconn,
            maxconn=maxconn or config.database.maxconn
        )

        logger.info("✅ 数据库管理器初始化完成 (TimescaleDB)")

# 保持原有的接口兼容性
def create_database_manager(**kwargs: Any) -> DatabaseManager:
    """创建数据库管理器实例"""
    return DatabaseManager(**kwargs)


