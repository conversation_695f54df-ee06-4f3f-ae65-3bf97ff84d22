#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单独下载指数数据的脚本
修复指数数据下载中的时间字段处理问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from datetime import datetime, timedelta
from data.market_fetcher import MarketDataFetcher
from data.database_manager import DatabaseManager
from utils.logger import setup_logger

logger = setup_logger(__name__)

def download_index_data(start_date=None, end_date=None):
    """
    下载指数数据
    
    Args:
        start_date: 开始日期，格式：YYYY-MM-DD，默认为30天前
        end_date: 结束日期，格式：YYYY-MM-DD，默认为今天
    """
    
    # 设置默认日期
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    if start_date is None:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    logger.info("🚀 开始下载指数数据")
    logger.info(f"📅 日期范围: {start_date} 到 {end_date}")
    
    try:
        # 初始化数据库连接
        db = DatabaseManager()
        logger.info("✅ 数据库连接成功")
        
        # 初始化市场数据获取器
        fetcher = MarketDataFetcher(db)
        logger.info("✅ 市场数据获取器初始化成功")
        
        # 下载指数数据
        logger.info("📊 开始下载指数数据...")
        fetcher.collect_index_data(start_date, end_date)
        
        logger.info("✅ 指数数据下载完成")
        
    except Exception as e:
        logger.error(f"❌ 指数数据下载失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    finally:
        # 关闭数据库连接
        if 'db' in locals():
            try:
                if hasattr(db, 'close'):
                    db.close()
                elif hasattr(db, 'connection') and hasattr(db.connection, 'close'):
                    db.connection.close()
                logger.info("✅ 数据库连接已关闭")
            except Exception as close_error:
                logger.warning(f"⚠️ 关闭数据库连接时出现警告: {close_error}")
    
    return True

def test_single_index(index_code='000001', start_date=None, end_date=None):
    """
    测试单个指数数据下载
    
    Args:
        index_code: 指数代码，默认为000001（上证指数）
        start_date: 开始日期，格式：YYYY-MM-DD，默认为7天前
        end_date: 结束日期，格式：YYYY-MM-DD，默认为今天
    """
    
    # 设置默认日期
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    if start_date is None:
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    
    logger.info(f"🔍 测试单个指数数据下载: {index_code}")
    logger.info(f"📅 日期范围: {start_date} 到 {end_date}")
    
    try:
        # 初始化数据库连接
        db = DatabaseManager()
        logger.info("✅ 数据库连接成功")
        
        # 初始化市场数据获取器
        fetcher = MarketDataFetcher(db)
        logger.info("✅ 市场数据获取器初始化成功")
        
        # 测试单个指数日线数据下载
        logger.info(f"📊 开始下载指数 {index_code} 日线数据...")
        try:
            fetcher._collect_single_index_daily_data(index_code, start_date, end_date)
            logger.info(f"✅ 指数 {index_code} 日线数据下载成功")
        except Exception as e:
            logger.error(f"❌ 指数 {index_code} 日线数据下载失败: {e}")
        
        # 测试单个指数分钟数据下载
        logger.info(f"📊 开始下载指数 {index_code} 分钟数据...")
        try:
            fetcher._collect_single_index_minute(index_code)
            logger.info(f"✅ 指数 {index_code} 分钟数据下载成功")
        except Exception as e:
            logger.error(f"❌ 指数 {index_code} 分钟数据下载失败: {e}")
        
        logger.info(f"✅ 指数 {index_code} 测试完成")
        
    except Exception as e:
        logger.error(f"❌ 指数 {index_code} 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    finally:
        # 关闭数据库连接
        if 'db' in locals():
            try:
                if hasattr(db, 'close'):
                    db.close()
                elif hasattr(db, 'connection') and hasattr(db.connection, 'close'):
                    db.connection.close()
                logger.info("✅ 数据库连接已关闭")
            except Exception as close_error:
                logger.warning(f"⚠️ 关闭数据库连接时出现警告: {close_error}")
    
    return True

def check_index_data(index_code='000001', days=7):
    """
    检查指数数据
    
    Args:
        index_code: 指数代码，默认为000001（上证指数）
        days: 检查最近几天的数据，默认为7天
    """
    
    logger.info(f"🔍 检查指数 {index_code} 最近 {days} 天的数据")
    
    try:
        # 初始化数据库连接
        db = DatabaseManager()
        logger.info("✅ 数据库连接成功")
        
        # 检查日线数据
        daily_query = f"""
        SELECT COUNT(*) as count, MIN(trade_time) as min_time, MAX(trade_time) as max_time
        FROM index_daily_data 
        WHERE index_code = '{index_code}' 
        AND trade_time >= CURRENT_DATE - INTERVAL '{days} days'
        """
        
        daily_result = db.execute_query(daily_query)
        if daily_result:
            count, min_time, max_time = daily_result[0]
            logger.info(f"📊 指数 {index_code} 日线数据: {count} 条记录")
            logger.info(f"📅 时间范围: {min_time} 到 {max_time}")
        else:
            logger.warning(f"⚠️ 指数 {index_code} 没有日线数据")
        
        # 检查分钟数据
        minute_query = f"""
        SELECT period, COUNT(*) as count, MIN(trade_time) as min_time, MAX(trade_time) as max_time
        FROM index_min_data 
        WHERE index_code = '{index_code}' 
        AND trade_time >= CURRENT_DATE - INTERVAL '{days} days'
        GROUP BY period
        ORDER BY period
        """
        
        minute_result = db.execute_query(minute_query)
        if minute_result:
            for period, count, min_time, max_time in minute_result:
                logger.info(f"📊 指数 {index_code} {period}分钟数据: {count} 条记录")
                logger.info(f"📅 时间范围: {min_time} 到 {max_time}")
        else:
            logger.warning(f"⚠️ 指数 {index_code} 没有分钟数据")
        
        logger.info(f"✅ 指数 {index_code} 数据检查完成")
        
    except Exception as e:
        logger.error(f"❌ 指数 {index_code} 数据检查失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    finally:
        # 关闭数据库连接
        if 'db' in locals():
            try:
                if hasattr(db, 'close'):
                    db.close()
                elif hasattr(db, 'connection') and hasattr(db.connection, 'close'):
                    db.connection.close()
                logger.info("✅ 数据库连接已关闭")
            except Exception as close_error:
                logger.warning(f"⚠️ 关闭数据库连接时出现警告: {close_error}")
    
    return True

def main():
    """主函数"""
    
    import argparse
    
    parser = argparse.ArgumentParser(description='指数数据下载工具')
    parser.add_argument('--start-date', type=str, help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--test', type=str, help='测试单个指数 (指数代码)')
    parser.add_argument('--check', type=str, help='检查指数数据 (指数代码)')
    parser.add_argument('--days', type=int, default=7, help='检查最近几天的数据 (默认7天)')
    
    args = parser.parse_args()
    
    if args.test:
        # 测试单个指数
        success = test_single_index(args.test, args.start_date, args.end_date)
        if success:
            logger.info("🎉 测试成功")
        else:
            logger.error("❌ 测试失败")
            sys.exit(1)
    
    elif args.check:
        # 检查指数数据
        success = check_index_data(args.check, args.days)
        if success:
            logger.info("🎉 检查完成")
        else:
            logger.error("❌ 检查失败")
            sys.exit(1)
    
    else:
        # 下载所有指数数据
        success = download_index_data(args.start_date, args.end_date)
        if success:
            logger.info("🎉 下载成功")
        else:
            logger.error("❌ 下载失败")
            sys.exit(1)

if __name__ == "__main__":
    main()
