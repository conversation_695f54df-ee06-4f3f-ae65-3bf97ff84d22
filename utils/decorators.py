import time
import random
from functools import wraps
from utils.logger import setup_logger
import socket
import requests
from urllib.error import URLError

logger = setup_logger(__name__)

def retry_on_error(retries=3, base_delay=None, delay=None, max_delay=60, connection_error_extra_delay=5):
    """
    带指数退避策略的重试装饰器
    
    Args:
        retries: 重试次数
        base_delay: 初始延迟（秒）
        delay: 旧参数名，初始延迟（秒），为了向后兼容
        max_delay: 最大延迟时间（秒）
        connection_error_extra_delay: 连接错误时额外增加的延迟（秒）
    """
    # 处理向后兼容
    if base_delay is None and delay is not None:
        base_delay = delay
    elif base_delay is None and delay is None:
        base_delay = 1  # 默认值
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            connection_errors = 0  # 跟踪连接错误的次数
            
            for i in range(retries):
                try:
                    return func(*args, **kwargs)
                except (ConnectionRefusedError, ConnectionError, ConnectionAbortedError, 
                        ConnectionResetError, socket.timeout, socket.error,
                        TimeoutError, URLError, requests.exceptions.ConnectionError) as e:
                    # 连接相关错误，需要更长的等待时间
                    connection_errors += 1
                    
                    if i == retries - 1:  # 最后一次重试
                        logger.error(f"最终重试失败（连接错误）: {str(e)}")
                        raise
                    
                    # 指数退避 + 抖动 + 额外延迟
                    delay_time = min(base_delay * (2 ** i) + connection_error_extra_delay * connection_errors, max_delay)
                    # 添加随机抖动（0-30%）
                    jitter = random.uniform(0, 0.3 * delay_time)
                    total_delay = delay_time + jitter
                    
                    logger.warning(f"连接错误: {str(e)}. 第 {i+1}/{retries} 次重试，等待 {total_delay:.2f} 秒")
                    time.sleep(total_delay)
                
                except Exception as e:
                    if i == retries - 1:  # 最后一次重试
                        logger.error(f"最终重试失败: {str(e)}")
                        raise
                    
                    # 普通错误的指数退避 + 抖动
                    delay_time = min(base_delay * (2 ** i), max_delay)
                    # 添加随机抖动（0-30%）
                    jitter = random.uniform(0, 0.3 * delay_time)
                    total_delay = delay_time + jitter
                    
                    logger.warning(f"错误: {str(e)}. 第 {i+1}/{retries} 次重试，等待 {total_delay:.2f} 秒")
                    time.sleep(total_delay)
                    
            return None
        return wrapper
    return decorator