import akshare as ak
import pandas as pd
from datetime import datetime, time
import pytz
from utils.logger import setup_logger
from functools import lru_cache

logger = setup_logger(__name__)


class TradeTimeManager:
    """交易时间管理器"""

    # 定义交易时段
    MORNING_START = time(9, 29)  # 上午开盘
    MORNING_END = time(11, 31)  # 上午收盘
    AFTERNOON_START = time(12, 59)  # 下午开盘
    AFTERNOON_END = time(15, 1)  # 下午收盘

    # 定义数据采集时间
    DATA_COLLECT_TIME = time(17, 00)  # 每天下午17点00分采集数据

    def __init__(self):
        self.tz = pytz.timezone('Asia/Shanghai')
        self._trade_dates = None

    def refresh_trade_dates(self):
        """强制刷新交易日历数据"""
        logger.info("开始强制刷新交易日历数据...")
        retry_count = 3
        while retry_count > 0:
            try:
                df = ak.tool_trade_date_hist_sina()
                self._trade_dates = pd.to_datetime(df['trade_date']).dt.strftime('%Y%m%d').tolist()
                logger.info(f"交易日历刷新成功，获取到 {len(self._trade_dates)} 个交易日")
                return True
            except Exception as e:
                retry_count -= 1
                logger.error(f"获取交易日历失败 (剩余重试次数: {retry_count}): {e}")
                if retry_count > 0:
                    import time
                    time.sleep(2)  # 等待2秒后重试

        logger.error("刷新交易日历失败，已达到最大重试次数")
        return False

    @property
    def trade_dates(self):
        """获取交易日历（缓存）"""
        if self._trade_dates is None:
            logger.info("首次获取交易日历数据...")
            self.refresh_trade_dates()
            if not self._trade_dates:
                logger.warning("无法获取交易日历，使用空列表")
                self._trade_dates = []
        return self._trade_dates

    def get_current_time(self) -> datetime:
        """获取当前北京时间"""
        return datetime.now(self.tz)

    def is_trade_date(self, date_str: str = None) -> bool:
        """
        检查是否为交易日

        Args:
            date_str: 日期字符串，格式为 YYYYMMDD，默认为当天
        """
        if not date_str:
            date_str = self.get_current_time().strftime('%Y%m%d')

        # 确保交易日历已加载
        if not self._trade_dates:
            logger.warning("交易日历为空，尝试刷新...")
            self.refresh_trade_dates()
            if not self._trade_dates:
                logger.error("无法获取交易日历，无法判断是否为交易日")
                return False

        is_trade_day = date_str in self.trade_dates
        logger.info(f"日期 {date_str} {'是' if is_trade_day else '不是'} 交易日")
        return is_trade_day

    def is_trading_time(self) -> bool:
        """检查当前是否为交易时段"""
        current_time = self.get_current_time().time()

        # 检查是否在上午或下午交易时段
        is_morning_session = self.MORNING_START <= current_time <= self.MORNING_END
        is_afternoon_session = self.AFTERNOON_START <= current_time <= self.AFTERNOON_END

        return is_morning_session or is_afternoon_session

    def is_data_collect_time(self) -> bool:
        """检查是否到了数据采集时间"""
        current_time = self.get_current_time()
        target_time = current_time.replace(
            hour=self.DATA_COLLECT_TIME.hour,
            minute=self.DATA_COLLECT_TIME.minute,
            second=0,
            microsecond=0
        )

        # 检查是否是交易日
        if not self.is_trade_date(current_time.strftime('%Y%m%d')):
            logger.info("今天不是交易日")
            return False

        # 检查是否在采集时间点前后1分钟内
        time_diff = abs((current_time - target_time).total_seconds())
        is_collect_time = time_diff <= 60  # 允许1分钟的误差

        if is_collect_time:
            logger.info("现在是数据采集时间")
        else:
            logger.debug(f"距离采集时间还有 {self.wait_time_to_next_collect() / 3600:.2f} 小时")

        return is_collect_time

    def get_next_trade_date(self, date_str: str = None) -> str:
        """获取下一个交易日"""
        if not date_str:
            date_str = self.get_current_time().strftime('%Y%m%d')

        try:
            current_index = self.trade_dates.index(date_str)
            if current_index < len(self.trade_dates) - 1:
                return self.trade_dates[current_index + 1]
        except ValueError:
            pass

        return None

    def wait_time_to_next_collect(self) -> float:
        """计算到下次数据采集的等待时间（秒）"""
        current_time = self.get_current_time()

        # 获取今天的目标时间
        today_target = current_time.replace(
            hour=self.DATA_COLLECT_TIME.hour,
            minute=self.DATA_COLLECT_TIME.minute,
            second=0,
            microsecond=0
        )

        # 如果今天的采集时间已过或者今天不是交易日
        if current_time >= today_target or not self.is_trade_date():
            # 获取下一个交易日
            next_date = self.get_next_trade_date()
            if next_date:
                # 转换为datetime对象
                next_day = datetime.strptime(next_date, '%Y%m%d').replace(
                    hour=self.DATA_COLLECT_TIME.hour,
                    minute=self.DATA_COLLECT_TIME.minute,
                    second=0,
                    microsecond=0,
                    tzinfo=self.tz
                )
                wait_seconds = (next_day - current_time).total_seconds()
                logger.info(f"下次盘后采集时间: {next_day}, 等待时间: {wait_seconds / 3600:.2f}小时")
                return wait_seconds
            else:
                # 如果找不到下一个交易日，等待24小时后重试
                logger.warning("找不到下一个交易日，24小时后重试")
                return 24 * 3600

        # 如果今天是交易日且还未到采集时间
        wait_seconds = (today_target - current_time).total_seconds()
        logger.info(f"今天的采集时间: {today_target}, 等待时间: {wait_seconds / 3600:.2f}小时")
        return wait_seconds

    def wait_time_to_next_trading(self) -> float:
        """计算到下一个交易时段的等待时间（秒）"""
        current_time = self.get_current_time()
        current_date = current_time.date()
        current_time_only = current_time.time()

        # 如果不是交易日，计算到下一个交易日开盘的等待时间
        if not self.is_trade_date():
            next_trade_date = self.get_next_trade_date()
            if next_trade_date:
                next_opening = datetime.strptime(f"{next_trade_date} 09:29:00", "%Y%m%d %H:%M:%S")
                next_opening = next_opening.replace(tzinfo=self.tz)
                wait_seconds = (next_opening - current_time).total_seconds()
                return max(0, int(wait_seconds))
            return 24 * 3600  # 如果找不到下一个交易日，等待24小时

        # 计算当天的各个时间点
        morning_open = datetime.combine(current_date, self.MORNING_START).replace(tzinfo=self.tz)
        morning_close = datetime.combine(current_date, self.MORNING_END).replace(tzinfo=self.tz)
        afternoon_open = datetime.combine(current_date, self.AFTERNOON_START).replace(tzinfo=self.tz)
        afternoon_close = datetime.combine(current_date, self.AFTERNOON_END).replace(tzinfo=self.tz)

        # 判断当前处于哪个时间段，并计算等待时间
        if current_time_only < self.MORNING_START:
            # 等待早盘开始
            wait_seconds = (morning_open - current_time).total_seconds()
            logger.info(f"今天的采集时间: {morning_open}, 等待时间: {wait_seconds / 3600:.2f}小时")
        elif self.MORNING_END < current_time_only < self.AFTERNOON_START:
            # 等待午盘开始
            wait_seconds = (afternoon_open - current_time).total_seconds()
            logger.info(f"下阶段的采集时间: {afternoon_open}, 等待时间: {wait_seconds / 3600:.2f}小时")
        elif self.AFTERNOON_END < current_time_only:
            # 等待下一个交易日
            next_trade_date = self.get_next_trade_date()
            if next_trade_date:
                next_opening = datetime.strptime(f"{next_trade_date} 09:29:00", "%Y%m%d %H:%M:%S")
                next_opening = next_opening.replace(tzinfo=self.tz)
                wait_seconds = (next_opening - current_time).total_seconds()
                logger.info(f"下阶段的采集时间: {next_opening}, 等待时间: {wait_seconds / 3600:.2f}小时")
            else:
                wait_seconds = 24 * 3600
        else:
            # 当前在交易时段内
            wait_seconds = 0

        return max(0, wait_seconds)

    def wait_time_next(self):
        """
        输出下次执行数据时间差
        :return:
        """
        trading_time = trade_time_manager.wait_time_to_next_trading()
        collect_time = trade_time_manager.wait_time_to_next_collect()
        logger.info(f"距离下次开盘时间还有{trading_time/ 3600:.2f}小时,距离下次收集数据时间还有{collect_time/ 3600:.2f}小时")


# 创建全局实例
trade_time_manager = TradeTimeManager()