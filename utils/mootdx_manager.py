#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
mootdx资源管理器

专门用于管理mootdx客户端的生命周期，确保资源正确释放，
避免ResourceWarning和连接泄漏问题。

功能特性:
- 自动资源管理
- 连接池复用
- 异常安全处理
- 警告抑制

作者: Xzh
版本: 1.0.0
"""

import warnings
import threading
import time
from contextlib import contextmanager
from typing import Optional, List, Tuple
from mootdx.quotes import Quotes

from utils.logger import setup_logger

logger = setup_logger(__name__)


class MootdxResourceManager:
    """
    mootdx资源管理器
    
    负责管理mootdx客户端的创建、复用和销毁，确保资源正确释放
    """
    
    def __init__(self):
        """初始化资源管理器"""
        self._clients = {}  # 客户端缓存
        self._lock = threading.Lock()  # 线程锁
        self._last_cleanup = time.time()  # 上次清理时间
        self._cleanup_interval = 300  # 清理间隔（秒）
        
        # 预定义的通达信服务器列表
        self.tdx_servers = [
            ('************', 7709),
            ('**************', 7709),
            ('**************', 7709),
            ('*************', 7709),
            ('**************', 7709),
        ]
        
        # mootdx客户端配置
        self.client_config = {
            'market': 'std',           # 标准股票市场
            'multithread': True,       # 启用多线程
            'heartbeat': True,         # 启用心跳包
            'bestip': False,           # 不自动选择最快服务器
            'timeout': 15,             # 连接超时时间（秒）
            'quiet': True              # 静默模式，不打印日志
        }
        
        logger.debug("✅ mootdx资源管理器初始化完成")
    
    def _suppress_mootdx_warnings(self):
        """抑制mootdx相关的警告"""
        # 抑制ResourceWarning
        warnings.filterwarnings(
            'ignore',
            category=ResourceWarning,
            module='mootdx.*'
        )
        
        # 抑制文件未关闭警告
        warnings.filterwarnings(
            'ignore',
            message='.*unclosed file.*',
            category=ResourceWarning
        )
    
    def _create_client(self, server: Tuple[str, int]) -> Optional[Quotes]:
        """
        创建mootdx客户端
        
        Args:
            server: 服务器地址元组 (host, port)
            
        Returns:
            Quotes: mootdx客户端实例，失败返回None
        """
        host, port = server
        
        try:
            # 抑制警告
            self._suppress_mootdx_warnings()
            
            logger.debug(f"🔗 创建mootdx客户端: {host}:{port}")
            
            # 创建客户端
            client = Quotes.factory(
                market=self.client_config['market'],
                multithread=self.client_config['multithread'],
                heartbeat=self.client_config['heartbeat'],
                bestip=self.client_config['bestip'],
                server=server,
                timeout=self.client_config['timeout'],
                quiet=self.client_config['quiet']
            )
            
            # 测试连接
            test_result = client.quotes(symbol=["000001"])
            if test_result is not None:
                logger.debug(f"✅ mootdx客户端创建成功: {host}:{port}")
                return client
            else:
                logger.debug(f"⚠️ 服务器连接测试失败: {host}:{port}")
                self._safe_close_client(client)
                return None
                
        except Exception as e:
            logger.debug(f"⚠️ 创建mootdx客户端失败: {host}:{port}, 错误: {e}")
            return None
    
    def _safe_close_client(self, client: Quotes):
        """
        安全关闭mootdx客户端
        
        Args:
            client: 要关闭的客户端
        """
        try:
            if client:
                client.close()
                logger.debug("✅ mootdx客户端已安全关闭")
        except Exception as e:
            logger.debug(f"⚠️ 关闭mootdx客户端时出错: {e}")
    
    def get_client(self, preferred_server: Optional[Tuple[str, int]] = None) -> Optional[Quotes]:
        """
        获取mootdx客户端
        
        Args:
            preferred_server: 首选服务器，为None时自动选择
            
        Returns:
            Quotes: mootdx客户端实例，失败返回None
        """
        with self._lock:
            # 定期清理过期连接
            self._cleanup_expired_clients()
            
            # 确定服务器列表
            servers = [preferred_server] + self.tdx_servers if preferred_server else self.tdx_servers
            
            # 尝试连接每个服务器
            for server in servers:
                if server is None:
                    continue
                    
                server_key = f"{server[0]}:{server[1]}"
                
                # 检查是否有缓存的客户端
                if server_key in self._clients:
                    client = self._clients[server_key]
                    try:
                        # 测试连接是否仍然有效
                        test_result = client.quotes(symbol=["000001"])
                        if test_result is not None:
                            logger.debug(f"✅ 复用mootdx客户端: {server_key}")
                            return client
                        else:
                            # 连接失效，移除缓存
                            logger.debug(f"⚠️ 缓存的客户端连接失效: {server_key}")
                            self._safe_close_client(client)
                            del self._clients[server_key]
                    except Exception as e:
                        logger.debug(f"⚠️ 测试缓存客户端失败: {server_key}, 错误: {e}")
                        self._safe_close_client(client)
                        del self._clients[server_key]
                
                # 创建新客户端
                client = self._create_client(server)
                if client:
                    self._clients[server_key] = client
                    logger.debug(f"✅ 新建mootdx客户端: {server_key}")
                    return client
            
            logger.error("❌ 所有mootdx服务器连接失败")
            return None
    
    def _cleanup_expired_clients(self):
        """清理过期的客户端连接"""
        current_time = time.time()
        
        # 检查是否需要清理
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
        
        logger.debug("🧹 开始清理过期的mootdx客户端连接")
        
        expired_keys = []
        for server_key, client in self._clients.items():
            try:
                # 测试连接是否仍然有效
                test_result = client.quotes(symbol=["000001"])
                if test_result is None:
                    expired_keys.append(server_key)
            except Exception:
                expired_keys.append(server_key)
        
        # 清理过期连接
        for key in expired_keys:
            client = self._clients.pop(key, None)
            if client:
                self._safe_close_client(client)
                logger.debug(f"🗑️ 清理过期客户端: {key}")
        
        self._last_cleanup = current_time
        
        if expired_keys:
            logger.debug(f"✅ 清理完成，移除 {len(expired_keys)} 个过期连接")
    
    @contextmanager
    def get_client_context(self, preferred_server: Optional[Tuple[str, int]] = None):
        """
        获取mootdx客户端的上下文管理器
        
        Args:
            preferred_server: 首选服务器
            
        Yields:
            Quotes: mootdx客户端实例
            
        Usage:
            with mootdx_manager.get_client_context() as client:
                if client:
                    data = client.bars(...)
        """
        client = self.get_client(preferred_server)
        try:
            yield client
        finally:
            # 注意：这里不关闭客户端，因为它可能被缓存复用
            # 客户端的生命周期由资源管理器统一管理
            pass
    
    def close_all_clients(self):
        """关闭所有客户端连接"""
        with self._lock:
            logger.debug("🔒 关闭所有mootdx客户端连接")
            
            for server_key, client in self._clients.items():
                self._safe_close_client(client)
                logger.debug(f"🗑️ 关闭客户端: {server_key}")
            
            self._clients.clear()
            logger.debug("✅ 所有mootdx客户端已关闭")
    
    def get_connection_status(self) -> dict:
        """
        获取连接状态信息
        
        Returns:
            dict: 连接状态信息
        """
        with self._lock:
            active_connections = len(self._clients)
            server_status = {}
            
            for server_key, client in self._clients.items():
                try:
                    test_result = client.quotes(symbol=["000001"])
                    server_status[server_key] = "active" if test_result is not None else "inactive"
                except Exception:
                    server_status[server_key] = "error"
            
            return {
                'active_connections': active_connections,
                'server_status': server_status,
                'last_cleanup': self._last_cleanup,
                'cleanup_interval': self._cleanup_interval
            }
    
    def __del__(self):
        """析构函数：确保资源被正确释放"""
        try:
            self.close_all_clients()
        except Exception:
            pass


# 全局mootdx资源管理器实例
mootdx_manager = MootdxResourceManager()


def get_mootdx_client(preferred_server: Optional[Tuple[str, int]] = None) -> Optional[Quotes]:
    """
    便捷函数：获取mootdx客户端
    
    Args:
        preferred_server: 首选服务器
        
    Returns:
        Quotes: mootdx客户端实例
    """
    return mootdx_manager.get_client(preferred_server)


@contextmanager
def mootdx_client_context(preferred_server: Optional[Tuple[str, int]] = None):
    """
    便捷函数：mootdx客户端上下文管理器
    
    Args:
        preferred_server: 首选服务器
        
    Yields:
        Quotes: mootdx客户端实例
    """
    with mootdx_manager.get_client_context(preferred_server) as client:
        yield client


if __name__ == '__main__':
    # 测试mootdx资源管理器
    print("🧪 测试mootdx资源管理器")
    
    # 测试客户端获取
    with mootdx_client_context() as client:
        if client:
            print("✅ mootdx客户端获取成功")
            result = client.quotes(symbol=["000001"])
            print(f"✅ 测试查询成功: {len(result) if result is not None else 0} 条记录")
        else:
            print("❌ mootdx客户端获取失败")
    
    # 显示连接状态
    status = mootdx_manager.get_connection_status()
    print(f"📊 连接状态: {status}")
    
    print("✅ mootdx资源管理器测试完成")
