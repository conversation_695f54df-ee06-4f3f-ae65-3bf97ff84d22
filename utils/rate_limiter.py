import time
import threading
from utils.logger import setup_logger
from config.config import settings

logger = setup_logger(__name__)

class RateLimiter:
    """
    令牌桶速率限制器，用于控制API请求频率
    
    使用方法:
    ```
    # 每秒最多2个请求，桶容量为10
    limiter = RateLimiter(rate=2, capacity=10)
    
    # 在发送请求前调用
    limiter.acquire()  # 会在必要时阻塞线程以遵守速率限制
    
    # 然后发送请求
    response = make_api_request()
    ```
    """
    
    def __init__(self, rate=1, capacity=10):
        """
        初始化速率限制器
        
        参数:
            rate: 每秒允许的请求数
            capacity: 令牌桶容量（可以突发的最大请求数）
        """
        self.rate = rate
        self.capacity = capacity
        self.tokens = capacity
        self.last_update = time.time()
        self.lock = threading.Lock()
    
    def acquire(self, tokens=1, block=True, timeout=None):
        """
        获取令牌，如果没有足够的令牌，会阻塞等待
        
        参数:
            tokens: 需要的令牌数，默认为1
            block: 如果为True，在没有足够令牌时会阻塞等待；否则会立即返回
            timeout: 等待的最大时间（秒），如果为None则无限等待
            
        返回:
            如果获取到了令牌，返回True；如果超时或不阻塞且没有足够令牌，返回False
        """
        start_time = time.time()
        
        while True:
            with self.lock:
                # 更新令牌数量
                current_time = time.time()
                elapsed = current_time - self.last_update
                self.tokens = min(self.capacity, self.tokens + elapsed * self.rate)
                self.last_update = current_time
                
                # 检查是否有足够的令牌
                if self.tokens >= tokens:
                    self.tokens -= tokens
                    return True
            
            # 如果没有足够的令牌且不阻塞，立即返回False
            if not block:
                return False
            
            # 检查是否超时
            if timeout is not None and time.time() - start_time > timeout:
                return False
            
            # 计算需要等待的时间
            with self.lock:
                required_tokens = tokens - self.tokens
                if required_tokens <= 0:
                    continue  # 已经有足够的令牌了，重新检查
                
                # 计算需要等待多久才能有足够的令牌
                wait_time = required_tokens / self.rate
                
                # 如果等待时间超过超时时间，返回False
                if timeout is not None and wait_time > timeout - (time.time() - start_time):
                    return False
            
            # 等待一小段时间后重试
            sleep_time = min(wait_time, 0.1)  # 至少等待0.1秒，避免CPU过度使用
            time.sleep(sleep_time)

# 创建全局速率限制器实例
# 使用settings中的配置
api_rate_limiter = RateLimiter(rate=settings.API_RATE_LIMIT, capacity=settings.API_RATE_CAPACITY) 