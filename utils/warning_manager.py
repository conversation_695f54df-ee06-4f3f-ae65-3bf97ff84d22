#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
警告管理模块

用于管理和过滤pandas链式赋值警告，特别是来自第三方库的警告
"""

import warnings
import logging
from contextlib import contextmanager
from typing import List, Optional, Any, Callable, Dict

logger = logging.getLogger(__name__)

class WarningManager:
    """
    警告管理器

    用于管理pandas FutureWarning和其他警告的显示和过滤
    """

    def __init__(self):
        self.original_filters: Optional[List[Any]] = None
        self.suppressed_warnings: List[Dict[str, str]] = []
    
    def suppress_pandas_warnings(self, 
                                modules: Optional[List[str]] = None,
                                warning_types: Optional[List[str]] = None):
        """
        抑制pandas相关警告
        
        Args:
            modules: 要抑制警告的模块列表，默认为第三方库
            warning_types: 要抑制的警告类型列表
        """
        if modules is None:
            # 默认抑制第三方库的警告
            modules = [
                'adata',
                'pandas.core.frame',
                'pandas.core.series'
            ]
        
        if warning_types is None:
            warning_types = ['FutureWarning']
        
        # 保存当前的警告过滤器
        self.original_filters = list(warnings.filters)  # type: ignore

        # 为每个模块和警告类型组合添加过滤器
        for module in modules:
            for warning_type in warning_types:
                warnings.filterwarnings(
                    'ignore',
                    category=FutureWarning if warning_type == 'FutureWarning' else UserWarning,
                    module=module
                )

                self.suppressed_warnings.append({  # type: ignore
                    'module': module,
                    'warning_type': warning_type
                })

        logger.debug(f"已抑制 {len(self.suppressed_warnings)} 个警告过滤器")  # type: ignore
    
    def restore_warnings(self):
        """恢复原始的警告设置"""
        if self.original_filters is not None:
            warnings.filters[:] = self.original_filters  # type: ignore
            logger.debug("已恢复原始警告设置")
            self.suppressed_warnings.clear()  # type: ignore
    
    @contextmanager
    def suppress_warnings_context(self, 
                                 modules: Optional[List[str]] = None,
                                 warning_types: Optional[List[str]] = None):
        """
        上下文管理器：临时抑制警告
        
        Args:
            modules: 要抑制警告的模块列表
            warning_types: 要抑制的警告类型列表
            
        Usage:
            with warning_manager.suppress_warnings_context():
                # 在这里执行可能产生警告的代码
                data = adata_function()
        """
        try:
            self.suppress_pandas_warnings(modules, warning_types)
            yield
        finally:
            self.restore_warnings()
    
    def filter_specific_warnings(self):
        """
        过滤特定的已知警告

        这个方法专门用于过滤我们已知的、来自第三方库的链式赋值警告
        包括akshare、adata、baostock等库产生的pandas FutureWarning
        """
        # 1. 过滤akshare库的链式赋值警告
        # akshare是产生最多pandas链式赋值警告的库
        akshare_modules = [
            'akshare.*',
            'akshare.stock_feature.*',
            'akshare.stock.*',
            'akshare.index.*'
        ]

        for module in akshare_modules:
            warnings.filterwarnings(
                'ignore',
                message='.*ChainedAssignmentError.*',
                category=FutureWarning,
                module=module
            )
            # 同时过滤所有pandas相关的FutureWarning
            warnings.filterwarnings(
                'ignore',
                category=FutureWarning,
                module=module
            )

        # 2. 过滤adata库的链式赋值警告
        warnings.filterwarnings(
            'ignore',
            message='.*ChainedAssignmentError.*',
            category=FutureWarning,
            module='adata.*'
        )

        # 3. 过滤baostock库的链式赋值警告
        warnings.filterwarnings(
            'ignore',
            message='.*ChainedAssignmentError.*',
            category=FutureWarning,
            module='baostock.*'
        )

        # 4. 过滤pandas内部的链式赋值警告（当被第三方库调用时）
        warnings.filterwarnings(
            'ignore',
            message='.*ChainedAssignmentError.*',
            category=FutureWarning,
            module='pandas.*'
        )

        # 5. 过滤特定的akshare警告模式
        # 这些是从实际运行中观察到的具体警告模式
        akshare_warning_patterns = [
            ".*temp_df\\[.*\\] = pd\\.to_numeric.*",
            ".*temp_df\\[.*\\] = temp_df\\[.*\\]\\.astype.*",
            ".*df\\[.*\\] = pd\\.to_numeric.*",
            ".*df\\[.*\\] = df\\[.*\\]\\.astype.*",
            ".*temp_df\\[\".*\"\\] = pd\\.to_numeric.*",
            ".*temp_df\\[\".*\"\\] = temp_df\\[\".*\"\\]\\.astype.*"
        ]

        for pattern in akshare_warning_patterns:
            warnings.filterwarnings(
                'ignore',
                message=pattern,
                category=FutureWarning
            )

        # 6. 过滤特定的adata警告模式
        adata_warning_patterns = [
            ".*df\\['pre_close'\\] = df\\['close'\\].*",
            ".*df\\['volume'\\] = df\\['volume'\\].*",
            ".*df\\['trade_time'\\] = pd\\.to_datetime.*",
            ".*df\\['stock_code'\\] = stock_code.*",
            ".*df\\[numeric_columns\\] = df\\[numeric_columns\\].*"
        ]

        for pattern in adata_warning_patterns:
            warnings.filterwarnings(
                'ignore',
                message=pattern,
                category=FutureWarning
            )

        # 7. 过滤mootdx库的链式赋值警告
        warnings.filterwarnings(
            'ignore',
            message='.*ChainedAssignmentError.*',
            category=FutureWarning,
            module='mootdx.*'
        )

        # 8. 过滤mootdx库的ResourceWarning（文件未关闭警告）
        warnings.filterwarnings(
            'ignore',
            message='.*unclosed file.*mootdx.*',
            category=ResourceWarning,
            module='mootdx.*'
        )

        # 9. 过滤所有来自mootdx的ResourceWarning
        warnings.filterwarnings(
            'ignore',
            category=ResourceWarning,
            module='mootdx.*'
        )

        # 8. 过滤来自第三方库的所有pandas相关警告
        # 这是一个更广泛的过滤器，确保捕获所有可能的第三方库警告
        third_party_modules = [
            'akshare',
            'adata',
            'baostock',
            'mootdx',
            'tushare',
            'yfinance',
            'pandas_datareader'
        ]

        for module in third_party_modules:
            # 过滤所有来自这些模块的FutureWarning
            warnings.filterwarnings(
                'ignore',
                category=FutureWarning,
                module=f'{module}.*'
            )

        logger.info("✅ 已配置全面的第三方库警告过滤器（包括akshare、adata、baostock等）")

    def filter_akshare_warnings(self):
        """
        专门过滤akshare库的pandas链式赋值警告

        akshare库在处理股票数据时会产生大量的pandas链式赋值警告，
        这些警告来自库内部的数据处理逻辑，不是我们项目代码的问题。
        此方法专门针对akshare的警告模式进行精确过滤。
        """
        # akshare库的所有子模块
        akshare_modules = [
            'akshare',
            'akshare.stock',
            'akshare.stock_feature',
            'akshare.stock_feature.stock_hist_em',
            'akshare.index',
            'akshare.fund',
            'akshare.bond',
            'akshare.futures',
            'akshare.option',
            'akshare.forex',
            'akshare.crypto',
            'akshare.macro',
            'akshare.news',
            'akshare.tool'
        ]

        # 为每个akshare模块添加警告过滤器
        for module in akshare_modules:
            # 过滤所有FutureWarning
            warnings.filterwarnings(
                'ignore',
                category=FutureWarning,
                module=f'{module}.*'
            )

            # 过滤链式赋值相关的警告
            warnings.filterwarnings(
                'ignore',
                message='.*ChainedAssignmentError.*',
                category=FutureWarning,
                module=f'{module}.*'
            )

            # 过滤pandas相关的警告
            warnings.filterwarnings(
                'ignore',
                message='.*pandas.*',
                category=FutureWarning,
                module=f'{module}.*'
            )

        # 过滤akshare中常见的具体警告模式
        akshare_specific_patterns = [
            # temp_df赋值模式
            '.*temp_df\\[".*"\\] = pd\\.to_numeric\\(temp_df\\[".*"\\].*',
            '.*temp_df\\[".*"\\] = temp_df\\[".*"\\]\\.astype\\(.*',

            # 索引处理模式
            '.*temp_df\\["index"\\] = temp_df\\["index"\\]\\.astype\\(int\\).*',

            # 数值转换模式
            '.*temp_df\\["最新价"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["涨跌幅"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["涨跌额"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["成交量"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["成交额"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["振幅"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["最高"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["最低"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["今开"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["昨收"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["量比"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["换手率"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["市盈率-动态"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["市净率"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["总市值"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["流通市值"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["涨速"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["5分钟涨跌"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["60日涨跌幅"\\] = pd\\.to_numeric.*',
            '.*temp_df\\["年初至今涨跌幅"\\] = pd\\.to_numeric.*',

            # f3字段处理
            '.*temp_df\\["f3"\\] = pd\\.to_numeric.*'
        ]

        # 应用所有akshare特定的警告过滤器
        for pattern in akshare_specific_patterns:
            warnings.filterwarnings(
                'ignore',
                message=pattern,
                category=FutureWarning
            )

        logger.info("✅ 已配置akshare专用警告过滤器")

    def is_third_party_warning(self, filename: str, lineno: int) -> bool:
        """
        智能判断警告是否来自第三方库

        通过分析调用栈中的文件路径，判断警告是否来自第三方库。
        这是一个更智能的过滤方法，可以准确区分项目代码和第三方库代码。

        Args:
            filename: 产生警告的文件路径
            lineno: 产生警告的行号

        Returns:
            bool: True表示来自第三方库，False表示来自项目代码
        """
        if not filename:
            return False

        # 第三方库的典型路径特征
        third_party_indicators = [
            'site-packages',  # pip安装的包
            'dist-packages',  # 系统包
            'akshare',        # akshare库
            'adata',          # adata库
            'baostock',       # baostock库
            'mootdx',         # mootdx库
            'pandas',         # pandas库
            'numpy',          # numpy库
            'tushare',        # tushare库
            'yfinance',       # yfinance库
            'venv',           # 虚拟环境
            'conda',          # conda环境
        ]

        # 检查文件路径是否包含第三方库标识
        filename_lower = filename.lower()
        for indicator in third_party_indicators:
            if indicator in filename_lower:
                return True

        # 项目代码的路径特征
        project_indicators = [
            '/home/<USER>/Program/stock_data',  # 项目根目录
            'data/',                        # 项目数据模块
            'utils/',                       # 项目工具模块
            'config/',                      # 项目配置模块
        ]

        # 如果明确是项目代码，返回False
        for indicator in project_indicators:
            if indicator in filename:
                return False

        # 默认情况下，如果无法确定，保守地认为是第三方库
        return True

    def smart_warning_filter(self, message: str, category: Any, filename: str, lineno: int, file: Optional[Any] = None, line: Optional[str] = None) -> None:
        """
        智能警告过滤器

        这是一个自定义的警告处理函数，可以根据警告来源智能地决定是否显示警告。
        只显示来自项目代码的警告，过滤第三方库的警告。

        Args:
            message: 警告消息
            category: 警告类别
            filename: 产生警告的文件名
            lineno: 产生警告的行号
            file: 文件对象（可选）
            line: 代码行（可选）
        """
        # 检查是否是pandas的FutureWarning
        if category == FutureWarning and 'ChainedAssignmentError' in str(message):  # type: ignore
            # 如果是第三方库的警告，直接忽略
            if self.is_third_party_warning(filename, lineno):  # type: ignore
                return

        # 如果不是第三方库警告，使用默认的警告处理
        warnings._showwarning_orig(message, category, filename, lineno, file, line)  # type: ignore

    def enable_smart_filtering(self):
        """
        启用智能警告过滤

        替换默认的警告显示函数为我们的智能过滤器
        """
        # 保存原始的警告显示函数
        if not hasattr(warnings, '_showwarning_orig'):
            warnings._showwarning_orig = warnings.showwarning  # type: ignore

        # 设置我们的智能过滤器
        warnings.showwarning = self.smart_warning_filter  # type: ignore

        logger.info("✅ 已启用智能警告过滤器")

    def disable_smart_filtering(self):
        """
        禁用智能警告过滤，恢复默认行为
        """
        if hasattr(warnings, '_showwarning_orig'):
            warnings.showwarning = warnings._showwarning_orig  # type: ignore
            logger.info("✅ 已禁用智能警告过滤器")

    def setup_production_warnings(self):
        """
        设置生产环境的警告配置

        在生产环境中，我们通常只想看到我们自己代码的警告，
        而不是第三方库的警告。特别是akshare库会产生大量的
        pandas链式赋值警告，这些都需要被过滤掉。

        使用双重过滤策略：
        1. 基于模块名的静态过滤（传统方法）
        2. 基于调用栈的智能过滤（新方法）
        """
        # 首先重置所有警告过滤器
        warnings.resetwarnings()

        # 默认显示所有警告
        warnings.filterwarnings('default')

        # 方法1：专门过滤akshare库的警告（最主要的警告来源）
        self.filter_akshare_warnings()

        # 方法2：过滤其他第三方库的特定警告
        self.filter_specific_warnings()

        # 方法3：启用智能警告过滤（基于调用栈分析）
        # 这是最精确的过滤方法，可以准确区分项目代码和第三方库代码
        self.enable_smart_filtering()

        logger.info("✅ 生产环境警告配置已设置（双重过滤：静态+智能）")
    
    def setup_development_warnings(self):
        """
        设置开发环境的警告配置

        在开发环境中，我们可能想要看到更多的警告信息，
        但仍然需要过滤第三方库的噪音警告，特别是akshare的警告
        """
        # 显示所有警告
        warnings.filterwarnings('default')

        # 过滤akshare库的警告（即使在开发环境中也要过滤，因为太多了）
        self.filter_akshare_warnings()

        # 过滤其他已知的第三方库警告
        self.filter_specific_warnings()

        logger.info("✅ 开发环境警告配置已设置（保留项目警告，过滤第三方库警告）")

# 全局警告管理器实例
warning_manager = WarningManager()

def suppress_third_party_warnings():
    """
    便捷函数：抑制第三方库警告
    
    这是一个便捷函数，可以在应用启动时调用
    """
    warning_manager.setup_production_warnings()

def setup_warnings_for_environment(environment: str = 'production'):
    """
    根据环境设置警告配置（优化版）

    Args:
        environment: 环境类型 ('production', 'development', 'testing')
    """
    if environment == 'production':
        # 生产环境：高效过滤第三方库警告
        setup_production_warnings_optimized()
    elif environment == 'development':
        warning_manager.setup_development_warnings()
    elif environment == 'testing':
        # 测试环境中通常希望看到所有警告
        warnings.filterwarnings('default')
    else:
        logger.warning(f"未知环境类型: {environment}，使用默认配置")
        setup_production_warnings_optimized()

def setup_production_warnings_optimized():
    """
    优化的生产环境警告配置

    专门针对股票数据采集系统的第三方库警告进行高效过滤，
    特别优化了mootdx、akshare、adata等库的警告处理。
    """
    # 重置警告过滤器
    warnings.resetwarnings()
    warnings.filterwarnings('default')

    # === 高优先级过滤器：最常见的警告 ===

    # 1. mootdx库警告（ResourceWarning + FutureWarning）
    warnings.filterwarnings('ignore', category=ResourceWarning, module='mootdx.*')
    warnings.filterwarnings('ignore', category=FutureWarning, module='mootdx.*')
    warnings.filterwarnings('ignore', message='.*unclosed file.*mootdx.*')

    # 2. akshare库链式赋值警告（最大的警告来源）
    warnings.filterwarnings('ignore', message='.*ChainedAssignmentError.*', category=FutureWarning, module='akshare.*')
    warnings.filterwarnings('ignore', category=FutureWarning, module='akshare.*')

    # 3. adata库警告
    warnings.filterwarnings('ignore', message='.*ChainedAssignmentError.*', category=FutureWarning, module='adata.*')
    warnings.filterwarnings('ignore', category=FutureWarning, module='adata.*')

    # 4. pandas内部警告（当被第三方库调用时）
    warnings.filterwarnings('ignore', message='.*ChainedAssignmentError.*', category=FutureWarning, module='pandas.*')

    # === 通用第三方库过滤器 ===

    # 5. 其他常见第三方库
    third_party_modules = [
        'baostock.*', 'tushare.*', 'yfinance.*', 'requests.*',
        'urllib3.*', 'numpy.*', 'scipy.*', 'matplotlib.*'
    ]

    for module in third_party_modules:
        warnings.filterwarnings('ignore', category=FutureWarning, module=module)
        warnings.filterwarnings('ignore', category=ResourceWarning, module=module)

    logger.info("✅ 优化的生产环境警告配置已设置（高效过滤第三方库警告）")

# 装饰器：临时抑制警告
def suppress_warnings(modules: Optional[List[str]] = None,
                     warning_types: Optional[List[str]] = None) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """
    装饰器：临时抑制警告

    Args:
        modules: 要抑制警告的模块列表
        warning_types: 要抑制的警告类型列表

    Usage:
        @suppress_warnings(['adata'])
        def get_stock_data():
            return adata.stock.market.get_market(...)
    """
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            with warning_manager.suppress_warnings_context(modules, warning_types):
                return func(*args, **kwargs)
        return wrapper
    return decorator

if __name__ == '__main__':
    # 测试警告管理器
    print("🧪 测试警告管理器")
    
    # 设置生产环境警告
    setup_warnings_for_environment('production')
    
    # 测试警告抑制
    with warning_manager.suppress_warnings_context(['adata']):
        print("✅ 在此上下文中，adata警告将被抑制")
    
    print("✅ 警告管理器测试完成")
