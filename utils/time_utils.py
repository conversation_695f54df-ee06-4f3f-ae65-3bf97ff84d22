from datetime import datetime
import pandas as pd
import logging

# 尝试导入配置，如果失败则使用默认值
try:
    from config.settings import settings
except ImportError:
    # 如果没有config.settings，使用默认配置
    class DefaultSettings:
        timezone = 'Asia/Shanghai'
    settings = DefaultSettings()

logger = logging.getLogger(__name__)


def process_datetime(
        df: pd.DataFrame,
        column: str = 'trade_time',
        add_timezone: bool = True,
        format: str = None
) -> pd.DataFrame:
    """
    处理DataFrame中的时间列

    Args:
        df: 待处理的DataFrame
        column: 时间列名
        add_timezone: 是否添加时区
        format: 时间格式，如果为None则自动推断

    Returns:
        处理后的DataFrame
    """
    if df.empty or column not in df.columns:
        return df

    try:
        # 确保列数据类型正确
        if not pd.api.types.is_datetime64_any_dtype(df[column]):
            # 修复链式赋值警告：如果是字符串，先转换为datetime
            # 使用df.loc[]方式避免链式赋值，确保pandas 3.0兼容性
            if pd.api.types.is_string_dtype(df[column]):
                df.loc[:, column] = pd.to_datetime(df[column], format=format)
            else:
                logger.warning(f"列 {column} 的数据类型不是datetime或字符串: {df[column].dtype}")
                return df

        # 修复链式赋值警告：添加时区（如果需要且尚未有时区信息）
        # 使用df.loc[]方式避免链式赋值，确保pandas 3.0兼容性
        if add_timezone and df[column].dt.tz is None:
            try:
                df.loc[:, column] = df[column].dt.tz_localize(settings.TIMEZONE)
            except Exception as e:
                logger.warning(f"添加时区失败: {e}")
                # 如果有时区冲突，先移除再添加 - 使用df.loc[]方式
                df.loc[:, column] = df[column].dt.tz_localize(None)
                df.loc[:, column] = df[column].dt.tz_localize(settings.TIMEZONE)

        return df

    except Exception as e:
        logger.error(f"处理时间列 {column} 失败: {e}")
        return df


def format_date(date_str: str) -> str:
    """
    格式化日期字符串

    Args:
        date_str: 日期字符串，支持多种格式

    Returns:
        格式化后的日期字符串 (YYYYMMDD)
    """
    if not date_str:
        return None

    try:
        # 移除可能存在的分隔符
        date_str = str(date_str).replace('-', '').replace('/', '')
        # 确保是8位数字格式
        if len(date_str) != 8:
            raise ValueError(f"日期格式错误: {date_str}")
        return date_str
    except Exception as e:
        logger.error(f"日期格式化失败: {e}")
        return None